Discount(
    home_operators=(2549,),
    partner_operators=(120, 121, 954, 763),
    direction=<DiscountDirectionEnum.BIDIRECTIONAL: 3>,
    service_types=(
        <ServiceTypeEnum.DATA: 5>,
        <ServiceTypeEnum.SMS_MO: 3>,
        <ServiceTypeEnum.SMS_MT: 4>,
        <ServiceTypeEnum.VOICE_MO: 1>,
        <ServiceTypeEnum.VOICE_MT: 2>,
        <ServiceTypeEnum.VOLTE: 6>
    ),
    period=DatePeriod(
        start_date=datetime.date(2025, 1, 1),
        end_date=datetime.date(2025, 12, 31)
    ),
    call_destinations=None,
    called_countries=None,
    traffic_segments=None,
    imsi_count_type=None,
    id=620,
    agreement_id=96,
    model_type=<DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL: 5>,
    currency_code='USD',
    tax_type=<TaxTypeEnum.NET: 1>,
    volume_type=<VolumeTypeEnum.ACTUAL: 1>,
    settlement_method=<DiscountSettlementMethodEnum.CREDIT_NOTE_EOA: 1>,
    qualifying_rule=None,
    parent_id=None,
    parameters=(
        DiscountParameter(
            discount_id=620,
            calculation_type=<DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL: 5>,
            basis=<DiscountBasisEnum.VALUE: 1>,
            basis_value=None,
            balancing=None,
            bound_type=<DiscountBoundTypeEnum.FINANCIAL_COMMITMENT: 2>,
            lower_bound=Decimal('100000.00'),
            upper_bound=None,
            toll_rate=None,
            airtime_rate=None,
            fair_usage_rate=None,
            fair_usage_threshold=None,
            access_fee_rate=None,
            incremental_rate=None
        ),
    ),
    sub_discounts=(
        Discount(
            home_operators=(2549,),
            partner_operators=(120, 121, 954, 763),
            direction=<DiscountDirectionEnum.BIDIRECTIONAL: 3>,
            service_types=(<ServiceTypeEnum.VOICE_MO: 1>,),
            period=DatePeriod(
                start_date=datetime.date(2025, 1, 1),
                end_date=datetime.date(2025, 12, 31)
            ),
            call_destinations=(<CallDestinationEnum.HOME: 1>, <CallDestinationEnum.LOCAL: 2>),
            called_countries=None,
            traffic_segments=None,
            imsi_count_type=None,
            id=622,
            agreement_id=96,
            model_type=<DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE: 1>,
            currency_code='USD',
            tax_type=<TaxTypeEnum.NET: 1>,
            volume_type=<VolumeTypeEnum.ACTUAL: 1>,
            settlement_method=<DiscountSettlementMethodEnum.CREDIT_NOTE_EOA: 1>,
            qualifying_rule=None,
            parent_id=620,
            parameters=(
                DiscountParameter(
                    discount_id=622,
                    calculation_type=<DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE: 1>,
                    basis=<DiscountBasisEnum.VALUE: 1>,
                    basis_value=Decimal('0.1000000000'),
                    balancing=None,
                    bound_type=None,
                    lower_bound=None,
                    upper_bound=None,
                    toll_rate=None,
                    airtime_rate=None,
                    fair_usage_rate=None,
                    fair_usage_threshold=None,
                    access_fee_rate=None,
                    incremental_rate=None
                ),
            ),
            sub_discounts=(),
            include_premium=True,
            include_premium_in_commitment=True,
            financial_threshold=None,
            above_financial_threshold_rate=None,
            above_commitment_rate=None,
            inbound_market_share=None,
            commitment_distribution_parameters=None,
            budget=None
        ),
        Discount(
            home_operators=(2549,),
            partner_operators=(120, 121, 954, 763),
            direction=<DiscountDirectionEnum.BIDIRECTIONAL: 3>,
            service_types=(<ServiceTypeEnum.VOICE_MO: 1>,),
            period=DatePeriod(
                start_date=datetime.date(2025, 1, 1),
                end_date=datetime.date(2025, 12, 31)
            ),
            call_destinations=(<CallDestinationEnum.INTERNATIONAL: 3>,),
            called_countries=None,
            traffic_segments=None,
            imsi_count_type=None,
            id=623,
            agreement_id=96,
            model_type=<DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE: 1>,
            currency_code='USD',
            tax_type=<TaxTypeEnum.NET: 1>,
            volume_type=<VolumeTypeEnum.ACTUAL: 1>,
            settlement_method=<DiscountSettlementMethodEnum.CREDIT_NOTE_EOA: 1>,
            qualifying_rule=None,
            parent_id=620,
            parameters=(
                DiscountParameter(
                    discount_id=623,
                    calculation_type=<DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE: 1>,
                    basis=<DiscountBasisEnum.VALUE: 1>,
                    basis_value=Decimal('0.1500000000'),
                    balancing=None,
                    bound_type=None,
                    lower_bound=None,
                    upper_bound=None,
                    toll_rate=None,
                    airtime_rate=None,
                    fair_usage_rate=None,
                    fair_usage_threshold=None,
                    access_fee_rate=None,
                    incremental_rate=None
                ),
            ),
            sub_discounts=(),
            include_premium=True,
            include_premium_in_commitment=True,
            financial_threshold=None,
            above_financial_threshold_rate=None,
            above_commitment_rate=None,
            inbound_market_share=None,
            commitment_distribution_parameters=None,
            budget=None
        ),
        Discount(
            home_operators=(2549,),
            partner_operators=(120, 121, 954, 763),
            direction=<DiscountDirectionEnum.BIDIRECTIONAL: 3>,
            service_types=(<ServiceTypeEnum.VOICE_MT: 2>,),
            period=DatePeriod(
                start_date=datetime.date(2025, 1, 1),
                end_date=datetime.date(2025, 12, 31)
            ),
            call_destinations=None,
            called_countries=None,
            traffic_segments=None,
            imsi_count_type=None,
            id=624,
            agreement_id=96,
            model_type=<DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE: 1>,
            currency_code='USD',
            tax_type=<TaxTypeEnum.NET: 1>,
            volume_type=<VolumeTypeEnum.ACTUAL: 1>,
            settlement_method=<DiscountSettlementMethodEnum.CREDIT_NOTE_EOA: 1>,
            qualifying_rule=None,
            parent_id=620,
            parameters=(
                DiscountParameter(
                    discount_id=624,
                    calculation_type=<DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE: 1>,
                    basis=<DiscountBasisEnum.VALUE: 1>,
                    basis_value=Decimal('0E-10'),
                    balancing=None,
                    bound_type=None,
                    lower_bound=None,
                    upper_bound=None,
                    toll_rate=None,
                    airtime_rate=None,
                    fair_usage_rate=None,
                    fair_usage_threshold=None,
                    access_fee_rate=None,
                    incremental_rate=None
                ),
            ),
            sub_discounts=(),
            include_premium=True,
            include_premium_in_commitment=True,
            financial_threshold=None,
            above_financial_threshold_rate=None,
            above_commitment_rate=None,
            inbound_market_share=None,
            commitment_distribution_parameters=None,
            budget=None
        ),
        Discount(
            home_operators=(2549,),
            partner_operators=(120, 121, 954, 763),
            direction=<DiscountDirectionEnum.BIDIRECTIONAL: 3>,
            service_types=(<ServiceTypeEnum.SMS_MO: 3>,),
            period=DatePeriod(
                start_date=datetime.date(2025, 1, 1),
                end_date=datetime.date(2025, 12, 31)
            ),
            call_destinations=None,
            called_countries=None,
            traffic_segments=None,
            imsi_count_type=None,
            id=625,
            agreement_id=96,
            model_type=<DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE: 1>,
            currency_code='USD',
            tax_type=<TaxTypeEnum.NET: 1>,
            volume_type=<VolumeTypeEnum.ACTUAL: 1>,
            settlement_method=<DiscountSettlementMethodEnum.CREDIT_NOTE_EOA: 1>,
            qualifying_rule=None,
            parent_id=620,
            parameters=(
                DiscountParameter(
                    discount_id=625,
                    calculation_type=<DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE: 1>,
                    basis=<DiscountBasisEnum.VALUE: 1>,
                    basis_value=Decimal('0.0020000000'),
                    balancing=None,
                    bound_type=None,
                    lower_bound=None,
                    upper_bound=None,
                    toll_rate=None,
                    airtime_rate=None,
                    fair_usage_rate=None,
                    fair_usage_threshold=None,
                    access_fee_rate=None,
                    incremental_rate=None
                ),
            ),
            sub_discounts=(),
            include_premium=True,
            include_premium_in_commitment=True,
            financial_threshold=None,
            above_financial_threshold_rate=None,
            above_commitment_rate=None,
            inbound_market_share=None,
            commitment_distribution_parameters=None,
            budget=None
        ),
        Discount(
            home_operators=(2549,),
            partner_operators=(120, 121, 954, 763),
            direction=<DiscountDirectionEnum.BIDIRECTIONAL: 3>,
            service_types=(<ServiceTypeEnum.DATA: 5>,),
            period=DatePeriod(
                start_date=datetime.date(2025, 1, 1),
                end_date=datetime.date(2025, 12, 31)
            ),
            call_destinations=None,
            called_countries=None,
            traffic_segments=None,
            imsi_count_type=None,
            id=626,
            agreement_id=96,
            model_type=None,
            currency_code='USD',
            tax_type=<TaxTypeEnum.NET: 1>,
            volume_type=<VolumeTypeEnum.ACTUAL: 1>,
            settlement_method=<DiscountSettlementMethodEnum.CREDIT_NOTE_EOA: 1>,
            qualifying_rule=None,
            parent_id=620,
            parameters=(
                DiscountParameter(
                    discount_id=626,
                    calculation_type=<DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE: 1>,
                    basis=<DiscountBasisEnum.VALUE: 1>,
                    basis_value=Decimal('0.0050000000'),
                    balancing=None,
                    bound_type=None,
                    lower_bound=None,
                    upper_bound=None,
                    toll_rate=None,
                    airtime_rate=None,
                    fair_usage_rate=None,
                    fair_usage_threshold=None,
                    access_fee_rate=None,
                    incremental_rate=None
                ),
                DiscountParameter(
                    discount_id=626,
                    calculation_type=<DiscountCalculationTypeEnum.STEPPED_TIERED: 7>,
                    basis=<DiscountBasisEnum.VALUE: 1>,
                    basis_value=Decimal('0.0030000000'),
                    balancing=None,
                    bound_type=<DiscountBoundTypeEnum.FINANCIAL_THRESHOLD: 7>,
                    lower_bound=None,
                    upper_bound=None,
                    toll_rate=None,
                    airtime_rate=None,
                    fair_usage_rate=None,
                    fair_usage_threshold=None,
                    access_fee_rate=None,
                    incremental_rate=None
                ),
            ),
            sub_discounts=(),
            include_premium=True,
            include_premium_in_commitment=True,
            financial_threshold=None,
            above_financial_threshold_rate=None,
            above_commitment_rate=None,
            inbound_market_share=None,
            commitment_distribution_parameters=None,
            budget=None
        ),
    ),
    include_premium=True,
    include_premium_in_commitment=True,
    financial_threshold=None,
    above_financial_threshold_rate=None,
    above_commitment_rate=None,
    inbound_market_share=None,
    commitment_distribution_parameters=None,
    budget=None
)
