import pytest

from nga.apps.agreements.domain.events import BudgetAgreementStatusChangedEvent
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.agreements.event_handlers import LogBudgetAgreementStatusChange
from nga.apps.agreements.infra.orm.models import AgreementStatusChangeLog
from nga.apps.agreements.infra.repositories.budget_agreement import from_orm_to_domain
from tests.factories.agreements import BudgetAgreementORMFactory
from tests.factories.users import UserORMFactory


@pytest.mark.django_db
class TestLogBudgetAgreementStatusChange:
    handler_cls = LogBudgetAgreementStatusChange

    def test_handle(self, override_deps, in_memory_mediator):
        budget_agreement_orm = BudgetAgreementORMFactory()

        budget_agreement = from_orm_to_domain(budget_agreement_orm)

        user_orm = UserORMFactory()

        event = BudgetAgreementStatusChangedEvent(
            budget_agreement=budget_agreement,
            old_status=AgreementStatusEnum.IN_REVIEW,
            new_status=AgreementStatusEnum.APPROVED,
            changed_by_user_id=user_orm.id,
        )

        handler = self.handler_cls()
        handler.handle(event)

        assert (
            AgreementStatusChangeLog.objects.filter(
                agreement_id=budget_agreement.agreement_id,
                old_status=event.old_status,
                new_status=event.new_status,
                changed_by_id=event.changed_by_user_id,
            ).exists()
            is True
        )
