import pytest

from nga.apps.agreements.domain.discount_model_properties import DiscountModelProperties
from nga.apps.agreements.enums import DiscountModelTypeEnum
from tests.factories.agreements.domain import (
    BackToFirstDiscountFactory,
    BalancedUnbalancedSREDiscountFactory,
    BalancedUnbalancedSteppedTieredDiscountFactory,
    DiscountFactory,
    PMPIAboveThresholdDiscountFactory,
    PMPIBackToFirstDiscountFactory,
    PMPIDiscountFactory,
    PMPISteppedTieredDiscountFactory,
    PMPIWithIncrementalChargingDiscountFactory,
    SoPFinancialDiscountFactory,
    SoPTrafficSREDiscountFactory,
    SoPTrafficSteppedTieredDiscountFactory,
    SREDiscountFactory,
    SteppedTieredDiscountFactory,
    FinancialThresholdDiscountFactory
)


class TestDiscountModelProperties:
    model_properties_cls = DiscountModelProperties

    def test_is_bidirectional(self):
        discount = DiscountFactory()

        properties = self.model_properties_cls(discount)

        assert properties.is_bidirectional == discount.is_bidirectional

    @pytest.mark.parametrize(
        "discount_factory_cls",
        (
            SoPFinancialDiscountFactory,
            SoPTrafficSREDiscountFactory,
            SoPTrafficSteppedTieredDiscountFactory,
        ),
    )
    def test_has_commitment(self, discount_factory_cls):
        discount = discount_factory_cls()

        properties = self.model_properties_cls(discount)

        assert properties.has_commitment is True

    def test_without_commitment(self):
        sre_discount = SREDiscountFactory()

        properties = self.model_properties_cls(sre_discount)

        assert properties.has_commitment is False

    @pytest.mark.parametrize(
        "discount_factory_cls,is_balancing",
        (
            (BalancedUnbalancedSREDiscountFactory, True),
            (BalancedUnbalancedSteppedTieredDiscountFactory, True),
            (SREDiscountFactory, False),
            (SteppedTieredDiscountFactory, False),
            (SoPFinancialDiscountFactory, False),
            (BackToFirstDiscountFactory, False),
            (SoPTrafficSteppedTieredDiscountFactory, False),
            (SoPTrafficSREDiscountFactory, False),
            (PMPIDiscountFactory, False),
            (PMPIAboveThresholdDiscountFactory, False),
            (PMPISteppedTieredDiscountFactory, False),
            (PMPIWithIncrementalChargingDiscountFactory, False),
            (PMPIBackToFirstDiscountFactory, False),
        ),
    )
    def test_is_balancing(self, discount_factory_cls, is_balancing):
        discount = discount_factory_cls()

        properties = self.model_properties_cls(discount)

        assert properties.is_balancing == is_balancing

    @pytest.mark.parametrize("discount_factory_cls,supports_sub_discounts", ((SoPFinancialDiscountFactory, True),))
    def test_supports_sub_discounts_when_model_is_set(self, discount_factory_cls, supports_sub_discounts):
        discount = discount_factory_cls(model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL)

        properties = self.model_properties_cls(discount)

        assert properties.supports_sub_discounts == supports_sub_discounts

    def test_supports_sub_discounts_when_model_is_not_set(self):
        discount = SoPFinancialDiscountFactory(model_type=None)

        properties = self.model_properties_cls(discount)

        assert properties.supports_sub_discounts is True

    @pytest.mark.parametrize(
        "discount_factory_cls,result",
        (
            (SoPFinancialDiscountFactory, True),
            (SREDiscountFactory, False),
        ),
    )
    def test_is_sop_financial(self, discount_factory_cls, result: bool):
        discount = discount_factory_cls()

        model_properties = self.model_properties_cls(discount)

        assert model_properties.is_sop_financial == result

    @pytest.mark.parametrize(
        "discount_factory_cls,expected_result",
        [
            (SoPFinancialDiscountFactory, False),
            (PMPIAboveThresholdDiscountFactory, False),
        ],
    )
    def test_is_financial_threshold_negative(self, discount_factory_cls, expected_result: bool):
        discount = discount_factory_cls()
        props = self.model_properties_cls(discount)
        assert props.is_financial_threshold == expected_result

    @pytest.mark.parametrize(
        "discount_factory_cls,expected_result",
        [
            (FinancialThresholdDiscountFactory, True),
        ],
    )
    def test_is_financial_threshold_positive(self, discount_factory_cls, expected_result: bool):
        discount = discount_factory_cls()
        props = self.model_properties_cls(discount)
        assert props.is_financial_threshold == expected_result
