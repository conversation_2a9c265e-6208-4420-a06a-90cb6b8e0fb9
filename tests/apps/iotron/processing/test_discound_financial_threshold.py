from datetime import date
from decimal import Decimal
from functools import partial

from nga.apps.agreements.enums import (
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
)
from nga.apps.iotron.processing.discount_financial_threshold import DiscountFinancialThresholdFiller
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryDiscountRepository
from tests.factories.agreements.domain import SREDiscountFactory, SREDiscountParameterFactory


class TestDiscountFinancialThresholdFiller:

    resolver_cls = DiscountFinancialThresholdFiller

    home_operators = (4, 5)
    partner_operators = (6, 7)
    period = DatePeriod(date(2024, 1, 1), date(2024, 7, 1))
    service_types = (ServiceTypeEnum.VOICE_MO,)
    direction = DiscountDirectionEnum.BIDIRECTIONAL

    discount_factory = staticmethod(
        partial(
            SREDiscountFactory,
            home_operators=home_operators,
            partner_operators=partner_operators,
            direction=direction,
            service_types=service_types,
            period=period,
            financial_threshold=None,
            model_type=None,
        )
    )

    def test_fill_financial_threshold(self):
        expected_financial_threshold = Decimal("1000")

        agreement_id = 9999
        parent_discount = self.discount_factory(
            id=100,
            agreement_id=agreement_id,
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
        )

        sub_discount_1 = self.discount_factory(
            id=101,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=expected_financial_threshold,
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                ),
            ],
        )

        parent_discount.sub_discounts = (sub_discount_1,)

        discount_repository = InMemoryDiscountRepository([parent_discount, sub_discount_1])

        financial_threshold_filler = DiscountFinancialThresholdFiller(discount_repository)

        financial_threshold_filler.fill_financial_threshold(parent_discount)

        filled_discount = discount_repository.get_by_id(parent_discount.id)

        # Assertions
        assert filled_discount.financial_threshold == expected_financial_threshold
        assert filled_discount.model_type == DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL
        assert len(filled_discount.sub_discounts) == 1
        assert len(filled_discount.parameters) == 1

    def test_fill_financial_threshold_no_sub_discounts(self):
        parent_discount = self.discount_factory()
        parent_discount.sub_discounts = tuple()
        discount_repository = InMemoryDiscountRepository([parent_discount])
        financial_threshold_filler = DiscountFinancialThresholdFiller(discount_repository)
        financial_threshold_filler.fill_financial_threshold(parent_discount)
        assert parent_discount.financial_threshold is None
        assert parent_discount.model_type is None
        assert len(parent_discount.sub_discounts) == 0
        assert len(parent_discount.parameters) == 1
