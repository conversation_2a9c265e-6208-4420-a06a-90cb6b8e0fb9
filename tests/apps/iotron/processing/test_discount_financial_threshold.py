from datetime import date
from decimal import Decimal
from functools import partial

from nga.apps.agreements.enums import (
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
)
from nga.apps.iotron.processing.discount_financial_threshold import DiscountFinancialThresholdFiller
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryDiscountRepository
from tests.factories.agreements.domain import (
    BackToFirstDiscountFactory,
    BackToFirstDiscountParameterFactory,
    BalancedUnbalancedSREDiscountFactory,
    PMPIDiscountFactory,
    PMPIDiscountParameterFactory,
    SREBalancedDiscountParameterFactory,
    SREDiscountFactory,
    SREDiscountParameterFactory,
    SREUnbalancedDiscountParameterFactory,
    SteppedTieredDiscountFactory,
    SteppedTieredDiscountParameterFactory,
)


class TestDiscountFinancialThresholdFiller:

    resolver_cls = DiscountFinancialThresholdFiller

    home_operators = (4, 5)
    partner_operators = (6, 7)
    period = DatePeriod(date(2024, 1, 1), date(2024, 7, 1))
    service_types = (ServiceTypeEnum.VOICE_MO,)
    direction = DiscountDirectionEnum.BIDIRECTIONAL

    discount_factory = staticmethod(
        partial(
            SREDiscountFactory,
            home_operators=home_operators,
            partner_operators=partner_operators,
            direction=direction,
            service_types=service_types,
            period=period,
            financial_threshold=None,
            model_type=None,
        )
    )

    def test_fill_financial_threshold(self):
        expected_financial_threshold = Decimal("1000")

        agreement_id = 9999
        parent_discount = self.discount_factory(
            id=100,
            agreement_id=agreement_id,
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
        )

        sub_discount_1 = self.discount_factory(
            id=101,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=expected_financial_threshold,
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                ),
            ],
        )

        parent_discount.sub_discounts = (sub_discount_1,)

        discount_repository = InMemoryDiscountRepository([parent_discount, sub_discount_1])

        financial_threshold_filler = DiscountFinancialThresholdFiller(discount_repository)

        financial_threshold_filler.fill_financial_threshold(parent_discount)

        filled_discount = discount_repository.get_by_id(parent_discount.id)

        # Assertions
        assert filled_discount.financial_threshold == expected_financial_threshold
        assert filled_discount.model_type == DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL
        assert len(filled_discount.sub_discounts) == 1
        assert len(filled_discount.parameters) == 1

    def test_fill_financial_threshold_no_sub_discounts(self):
        parent_discount = self.discount_factory()
        parent_discount.sub_discounts = tuple()
        discount_repository = InMemoryDiscountRepository([parent_discount])
        financial_threshold_filler = DiscountFinancialThresholdFiller(discount_repository)
        financial_threshold_filler.fill_financial_threshold(parent_discount)
        assert parent_discount.financial_threshold is None
        assert parent_discount.model_type is None
        assert len(parent_discount.sub_discounts) == 0
        assert len(parent_discount.parameters) == 1

    def test_fill_financial_threshold_with_multiple_sre_noisy_discounts(self):
        """Test with multiple SRE sub-discounts as noise alongside financial threshold discount."""
        expected_financial_threshold = Decimal("2500")
        agreement_id = 8888

        parent_discount = self.discount_factory(
            id=200,
            agreement_id=agreement_id,
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
        )

        # Financial threshold sub-discount
        threshold_sub_discount = self.discount_factory(
            id=201,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=expected_financial_threshold,
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                ),
            ],
        )

        # Multiple SRE "noise" sub-discounts with different parameters
        sre_sub_discount_1 = SREDiscountFactory(
            id=202,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.05"),
                )
            ],
        )

        sre_sub_discount_2 = SREDiscountFactory(
            id=203,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_COMMITMENT,
                    basis_value=Decimal("0.08"),
                )
            ],
        )

        sre_sub_discount_3 = SREDiscountFactory(
            id=204,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=None,
                    basis_value=Decimal("0.12"),
                )
            ],
        )

        parent_discount.sub_discounts = (
            threshold_sub_discount,
            sre_sub_discount_1,
            sre_sub_discount_2,
            sre_sub_discount_3,
        )

        discount_repository = InMemoryDiscountRepository(
            [
                parent_discount,
                threshold_sub_discount,
                sre_sub_discount_1,
                sre_sub_discount_2,
                sre_sub_discount_3,
            ]
        )

        financial_threshold_filler = DiscountFinancialThresholdFiller(discount_repository)
        financial_threshold_filler.fill_financial_threshold(parent_discount)

        filled_discount = discount_repository.get_by_id(parent_discount.id)

        # Assertions
        assert filled_discount.financial_threshold == expected_financial_threshold
        assert filled_discount.model_type == DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL

        # Threshold sub-discount should be deleted
        try:
            discount_repository.get_by_id(threshold_sub_discount.id)
            assert False, "Threshold sub-discount should have been deleted"
        except Exception:
            pass

        # All SRE sub-discounts should remain unchanged
        updated_sre_1 = discount_repository.get_by_id(sre_sub_discount_1.id)
        assert len(updated_sre_1.parameters) == 1
        assert updated_sre_1.parameters[0].bound_type == DiscountBoundTypeEnum.VOLUME

        updated_sre_2 = discount_repository.get_by_id(sre_sub_discount_2.id)
        assert len(updated_sre_2.parameters) == 1
        assert updated_sre_2.parameters[0].bound_type == DiscountBoundTypeEnum.FINANCIAL_COMMITMENT

        updated_sre_3 = discount_repository.get_by_id(sre_sub_discount_3.id)
        assert len(updated_sre_3.parameters) == 1
        assert updated_sre_3.parameters[0].bound_type is None

    def test_fill_financial_threshold_with_stepped_tiered_noisy_discounts(self):
        """Test with stepped tiered sub-discounts as noise alongside financial threshold discount."""
        expected_financial_threshold = Decimal("3000")
        agreement_id = 7777

        parent_discount = self.discount_factory(
            id=300,
            agreement_id=agreement_id,
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
        )

        # Financial threshold sub-discount
        threshold_sub_discount = self.discount_factory(
            id=301,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=expected_financial_threshold,
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                ),
            ],
        )

        # Stepped tiered "noise" sub-discounts
        stepped_tiered_sub_discount_1 = SteppedTieredDiscountFactory(
            id=302,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.15"),
                    lower_bound=Decimal("0"),
                    upper_bound=Decimal("1000"),
                )
            ],
        )

        stepped_tiered_sub_discount_2 = SteppedTieredDiscountFactory(
            id=303,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.10"),
                    lower_bound=Decimal("1000"),
                    upper_bound=None,
                )
            ],
        )

        parent_discount.sub_discounts = (
            threshold_sub_discount,
            stepped_tiered_sub_discount_1,
            stepped_tiered_sub_discount_2,
        )

        discount_repository = InMemoryDiscountRepository(
            [
                parent_discount,
                threshold_sub_discount,
                stepped_tiered_sub_discount_1,
                stepped_tiered_sub_discount_2,
            ]
        )

        financial_threshold_filler = DiscountFinancialThresholdFiller(discount_repository)
        financial_threshold_filler.fill_financial_threshold(parent_discount)

        filled_discount = discount_repository.get_by_id(parent_discount.id)

        # Assertions
        assert filled_discount.financial_threshold == expected_financial_threshold
        assert filled_discount.model_type == DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL

        # Threshold sub-discount should be deleted
        try:
            discount_repository.get_by_id(threshold_sub_discount.id)
            assert False, "Threshold sub-discount should have been deleted"
        except Exception:
            pass

        # Stepped tiered sub-discounts should remain unchanged
        updated_st_1 = discount_repository.get_by_id(stepped_tiered_sub_discount_1.id)
        assert len(updated_st_1.parameters) == 1
        assert updated_st_1.parameters[0].calculation_type == DiscountCalculationTypeEnum.STEPPED_TIERED
        assert updated_st_1.parameters[0].upper_bound == Decimal("1000")

        updated_st_2 = discount_repository.get_by_id(stepped_tiered_sub_discount_2.id)
        assert len(updated_st_2.parameters) == 1
        assert updated_st_2.parameters[0].calculation_type == DiscountCalculationTypeEnum.STEPPED_TIERED
        assert updated_st_2.parameters[0].upper_bound is None

    def test_fill_financial_threshold_with_mixed_discount_types_as_noise(self):
        """Test with a mix of different discount types as noise."""
        expected_financial_threshold = Decimal("4500")
        agreement_id = 6666

        parent_discount = self.discount_factory(
            id=400,
            agreement_id=agreement_id,
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
        )

        # Financial threshold sub-discount
        threshold_sub_discount = self.discount_factory(
            id=401,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=expected_financial_threshold,
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                ),
            ],
        )

        # SRE noise discount
        sre_noise_discount = SREDiscountFactory(
            id=402,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.20"),
                )
            ],
        )

        # Back to First noise discount
        back_to_first_noise_discount = BackToFirstDiscountFactory(
            id=403,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                BackToFirstDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.25"),
                    lower_bound=Decimal("0"),
                ),
                BackToFirstDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.18"),
                    lower_bound=Decimal("500"),
                ),
            ],
        )

        # Balanced/Unbalanced SRE noise discount
        balanced_unbalanced_noise_discount = BalancedUnbalancedSREDiscountFactory(
            id=404,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SREBalancedDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.30"),
                ),
                SREUnbalancedDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.35"),
                ),
            ],
        )

        parent_discount.sub_discounts = (
            threshold_sub_discount,
            sre_noise_discount,
            back_to_first_noise_discount,
            balanced_unbalanced_noise_discount,
        )

        discount_repository = InMemoryDiscountRepository(
            [
                parent_discount,
                threshold_sub_discount,
                sre_noise_discount,
                back_to_first_noise_discount,
                balanced_unbalanced_noise_discount,
            ]
        )

        financial_threshold_filler = DiscountFinancialThresholdFiller(discount_repository)
        financial_threshold_filler.fill_financial_threshold(parent_discount)

        filled_discount = discount_repository.get_by_id(parent_discount.id)

        # Assertions
        assert filled_discount.financial_threshold == expected_financial_threshold
        assert filled_discount.model_type == DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL

        # Threshold sub-discount should be deleted
        try:
            discount_repository.get_by_id(threshold_sub_discount.id)
            assert False, "Threshold sub-discount should have been deleted"
        except Exception:
            pass

        # All noise discounts should remain unchanged
        updated_sre = discount_repository.get_by_id(sre_noise_discount.id)
        assert len(updated_sre.parameters) == 1
        assert updated_sre.model_type == DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE

        updated_btf = discount_repository.get_by_id(back_to_first_noise_discount.id)
        assert len(updated_btf.parameters) == 2
        assert updated_btf.model_type == DiscountModelTypeEnum.BACK_TO_FIRST

        updated_balanced = discount_repository.get_by_id(balanced_unbalanced_noise_discount.id)
        assert len(updated_balanced.parameters) == 2
        assert updated_balanced.model_type == DiscountModelTypeEnum.BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE

    def test_fill_financial_threshold_with_pmpi_and_multiple_thresholds(self):
        """Test with PMPI discounts and multiple financial threshold discounts."""
        expected_financial_threshold_1 = Decimal("1500")
        expected_financial_threshold_2 = Decimal("2000")
        agreement_id = 5555

        parent_discount = self.discount_factory(
            id=500,
            agreement_id=agreement_id,
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
        )

        # First financial threshold sub-discount
        threshold_sub_discount_1 = self.discount_factory(
            id=501,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=expected_financial_threshold_1,
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                ),
            ],
        )

        # Second financial threshold sub-discount
        threshold_sub_discount_2 = self.discount_factory(
            id=502,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=expected_financial_threshold_2,
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                ),
            ],
        )

        # PMPI noise discount
        pmpi_noise_discount = PMPIDiscountFactory(
            id=503,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            period=self.period,
            parameters=[
                PMPIDiscountParameterFactory(
                    basis_value=Decimal("5.00"),
                )
            ],
        )

        # Mixed SRE and Stepped Tiered noise discounts
        mixed_sre_discount = SREDiscountFactory(
            id=504,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_COMMITMENT,
                    basis_value=Decimal("0.40"),
                )
            ],
        )

        mixed_stepped_tiered_discount = SteppedTieredDiscountFactory(
            id=505,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.45"),
                    lower_bound=Decimal("0"),
                    upper_bound=Decimal("2000"),
                ),
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.35"),
                    lower_bound=Decimal("2000"),
                    upper_bound=None,
                ),
            ],
        )

        parent_discount.sub_discounts = (
            threshold_sub_discount_1,
            threshold_sub_discount_2,
            pmpi_noise_discount,
            mixed_sre_discount,
            mixed_stepped_tiered_discount,
        )

        discount_repository = InMemoryDiscountRepository(
            [
                parent_discount,
                threshold_sub_discount_1,
                threshold_sub_discount_2,
                pmpi_noise_discount,
                mixed_sre_discount,
                mixed_stepped_tiered_discount,
            ]
        )

        financial_threshold_filler = DiscountFinancialThresholdFiller(discount_repository)
        financial_threshold_filler.fill_financial_threshold(parent_discount)

        filled_discount = discount_repository.get_by_id(parent_discount.id)

        # Assertions - should have one of the threshold values (last one processed)
        assert filled_discount.financial_threshold in [expected_financial_threshold_1, expected_financial_threshold_2]
        assert filled_discount.model_type == DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL

        # Both threshold sub-discounts should be deleted
        try:
            discount_repository.get_by_id(threshold_sub_discount_1.id)
            assert False, "First threshold sub-discount should have been deleted"
        except Exception:
            pass

        try:
            discount_repository.get_by_id(threshold_sub_discount_2.id)
            assert False, "Second threshold sub-discount should have been deleted"
        except Exception:
            pass

        # All noise discounts should remain unchanged
        updated_pmpi = discount_repository.get_by_id(pmpi_noise_discount.id)
        assert len(updated_pmpi.parameters) == 1
        assert updated_pmpi.model_type == DiscountModelTypeEnum.PER_MONTH_PER_IMSI

        updated_mixed_sre = discount_repository.get_by_id(mixed_sre_discount.id)
        assert len(updated_mixed_sre.parameters) == 1
        assert updated_mixed_sre.model_type == DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE

        updated_mixed_st = discount_repository.get_by_id(mixed_stepped_tiered_discount.id)
        assert len(updated_mixed_st.parameters) == 2
        assert updated_mixed_st.model_type == DiscountModelTypeEnum.STEPPED_TIERED

    def test_fill_financial_threshold_with_non_matching_traffic_noise(self):
        """Test that financial threshold is only applied when traffic fields match."""
        expected_financial_threshold = Decimal("3500")
        agreement_id = 4444

        parent_discount = self.discount_factory(
            id=600,
            agreement_id=agreement_id,
            model_type=DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
        )

        # Financial threshold sub-discount with specific traffic
        threshold_sub_discount = self.discount_factory(
            id=601,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    lower_bound=expected_financial_threshold,
                    calculation_type=DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD,
                ),
            ],
        )

        # Noise discount with DIFFERENT traffic fields - should NOT get threshold applied
        different_traffic_discount = SREDiscountFactory(
            id=602,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=(99, 100),  # Different home operators
            partner_operators=(101, 102),  # Different partner operators
            direction=DiscountDirectionEnum.INBOUND,  # Different direction
            service_types=(ServiceTypeEnum.DATA,),  # Different service type
            period=self.period,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.50"),
                )
            ],
        )

        # Noise discount with MATCHING traffic fields - should get threshold applied
        matching_traffic_discount = SREDiscountFactory(
            id=603,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=self.home_operators,  # Same as threshold
            partner_operators=self.partner_operators,  # Same as threshold
            direction=self.direction,  # Same as threshold
            service_types=self.service_types,  # Same as threshold
            period=self.period,  # Same as threshold
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.60"),
                )
            ],
        )

        # Additional noise with mixed parameters
        complex_noise_discount = SteppedTieredDiscountFactory(
            id=604,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            home_operators=(200, 201),  # Different traffic
            partner_operators=(202, 203),
            direction=DiscountDirectionEnum.OUTBOUND,
            service_types=(ServiceTypeEnum.SMS_MO,),
            period=self.period,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.70"),
                    lower_bound=Decimal("0"),
                    upper_bound=Decimal("1500"),
                ),
            ],
        )

        parent_discount.sub_discounts = (
            threshold_sub_discount,
            different_traffic_discount,
            matching_traffic_discount,
            complex_noise_discount,
        )

        discount_repository = InMemoryDiscountRepository(
            [
                parent_discount,
                threshold_sub_discount,
                different_traffic_discount,
                matching_traffic_discount,
                complex_noise_discount,
            ]
        )

        financial_threshold_filler = DiscountFinancialThresholdFiller(discount_repository)
        financial_threshold_filler.fill_financial_threshold(parent_discount)

        filled_discount = discount_repository.get_by_id(parent_discount.id)

        # Parent should have threshold applied only if traffic matches
        # Since threshold discount matches parent traffic, threshold should be applied
        assert filled_discount.financial_threshold == expected_financial_threshold
        assert filled_discount.model_type == DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL

        # Threshold sub-discount should be deleted
        try:
            discount_repository.get_by_id(threshold_sub_discount.id)
            assert False, "Threshold sub-discount should have been deleted"
        except Exception:
            pass

        # Different traffic discount should remain unchanged (no threshold applied)
        updated_different = discount_repository.get_by_id(different_traffic_discount.id)
        assert len(updated_different.parameters) == 1
        assert updated_different.model_type == DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE

        # Matching traffic discount should remain unchanged (no threshold applied to sub-discounts)
        updated_matching = discount_repository.get_by_id(matching_traffic_discount.id)
        assert len(updated_matching.parameters) == 1
        assert updated_matching.model_type == DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE

        # Complex noise discount should remain unchanged
        updated_complex = discount_repository.get_by_id(complex_noise_discount.id)
        assert len(updated_complex.parameters) == 1
        assert updated_complex.model_type == DiscountModelTypeEnum.STEPPED_TIERED
