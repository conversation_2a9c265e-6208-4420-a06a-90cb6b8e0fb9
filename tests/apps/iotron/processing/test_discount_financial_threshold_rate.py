from datetime import date
from decimal import Decimal
from functools import partial

from nga.apps.agreements.enums import (
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
)
from nga.apps.iotron.processing.discount_financial_threshold_rate import DiscountFinancialThresholdRateFiller
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryDiscountRepository
from tests.factories.agreements.domain import (
    SREDiscountFactory,
    SREDiscountParameterFactory,
    SteppedTieredDiscountParameterFactory,
)


class TestDiscountFinancialThresholdRateFiller:
    resolver_cls = DiscountFinancialThresholdRateFiller

    home_operators = (4, 5)
    partner_operators = (6, 7)
    period = DatePeriod(date(2024, 1, 1), date(2024, 7, 1))
    service_types = (ServiceTypeEnum.VOICE_MO,)
    direction = DiscountDirectionEnum.BIDIRECTIONAL

    discount_factory = staticmethod(
        partial(
            SREDiscountFactory,
            home_operators=home_operators,
            partner_operators=partner_operators,
            direction=direction,
            service_types=service_types,
            period=period,
            traffic_segments=(),
            above_commitment_rate=None,
            model_type=None,
        )
    )

    def test_fill_financial_threshold_rate(self):
        expected_rate = Decimal("0.007")
        agreement_id = 9999

        parent_discount = self.discount_factory()

        sub_discount_1 = self.discount_factory(
            id=101,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    basis_value=expected_rate,
                )
            ],
        )

        sub_discount_2 = self.discount_factory(
            id=102,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=None,
                    basis_value=Decimal("0.1"),
                )
            ],
        )

        sub_discount_3 = self.discount_factory(
            id=103,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=None,
                    basis_value=Decimal("0.15"),
                )
            ],
        )

        parent_discount.sub_discounts = (sub_discount_1, sub_discount_2, sub_discount_3)

        discount_repository = InMemoryDiscountRepository(
            [parent_discount, sub_discount_1, sub_discount_2, sub_discount_3]
        )

        filler = self.resolver_cls(discount_repository)
        filler.fill_financial_threshold_rate(parent_discount)

        # Discount with financial threshold parameter should have parameters cleared
        updated_sub_2 = discount_repository.get_by_id(sub_discount_2.id)
        assert len(updated_sub_2.parameters) != 0

        # Other discounts should be updated with the threshold rate
        updated_sub_2 = discount_repository.get_by_id(sub_discount_2.id)
        assert updated_sub_2.above_financial_threshold_rate == expected_rate

        updated_sub_3 = discount_repository.get_by_id(sub_discount_3.id)
        assert updated_sub_3.above_financial_threshold_rate == expected_rate

    def test_fill_financial_threshold_rate_deletes_discount_with_only_threshold_parameters(self):
        """Test that sub-discounts with only financial threshold parameters are deleted."""
        expected_rate = Decimal("0.005")
        agreement_id = 8888

        parent_discount = self.discount_factory()

        # Sub-discount with only financial threshold parameter - should be deleted
        sub_discount_threshold_only = self.discount_factory(
            id=201,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    basis_value=expected_rate,
                )
            ],
        )

        # Sub-discount with regular parameter - should be updated with threshold rate
        sub_discount_regular = self.discount_factory(
            id=202,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.2"),
                )
            ],
        )

        parent_discount.sub_discounts = (sub_discount_threshold_only, sub_discount_regular)

        discount_repository = InMemoryDiscountRepository(
            [parent_discount, sub_discount_threshold_only, sub_discount_regular]
        )

        filler = self.resolver_cls(discount_repository)
        filler.fill_financial_threshold_rate(parent_discount)

        # Sub-discount with only threshold parameter should be deleted
        try:
            discount_repository.get_by_id(sub_discount_threshold_only.id)
            assert False, "Sub-discount with only threshold parameter should have been deleted"
        except Exception:
            pass  # Expected - discount should not exist

        # Regular sub-discount should still exist and have threshold rate applied
        updated_regular = discount_repository.get_by_id(sub_discount_regular.id)
        assert updated_regular.above_financial_threshold_rate == expected_rate
        assert len(updated_regular.parameters) == 1

    def test_fill_financial_threshold_rate_mixed_parameters(self):
        """Test sub-discount with both threshold and regular parameters."""
        expected_rate = Decimal("0.012")
        agreement_id = 7777

        parent_discount = self.discount_factory()

        # Sub-discount with mixed parameters
        sub_discount_mixed = self.discount_factory(
            id=301,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    basis_value=expected_rate,
                ),
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.3"),
                ),
            ],
        )

        parent_discount.sub_discounts = (sub_discount_mixed,)

        discount_repository = InMemoryDiscountRepository([parent_discount, sub_discount_mixed])

        filler = self.resolver_cls(discount_repository)
        filler.fill_financial_threshold_rate(parent_discount)

        # Sub-discount should still exist but with threshold parameter removed
        updated_mixed = discount_repository.get_by_id(sub_discount_mixed.id)
        assert len(updated_mixed.parameters) == 1
        assert updated_mixed.parameters[0].bound_type == DiscountBoundTypeEnum.VOLUME
        assert updated_mixed.above_financial_threshold_rate == expected_rate

    def test_fill_financial_threshold_rate_no_matching_traffic(self):
        """Test that threshold rate is only applied to discounts with matching traffic fields."""
        expected_rate = Decimal("0.008")
        agreement_id = 6666

        parent_discount = self.discount_factory()

        # Sub-discount with threshold parameter
        sub_discount_threshold = self.discount_factory(
            id=401,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    basis_value=expected_rate,
                )
            ],
        )

        # Sub-discount with different traffic fields - should NOT get threshold rate
        sub_discount_different_traffic = self.discount_factory(
            id=402,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            home_operators=(99, 100),  # Different home operators
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.4"),
                )
            ],
        )

        # Sub-discount with matching traffic fields - should get threshold rate
        sub_discount_matching_traffic = self.discount_factory(
            id=403,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            home_operators=self.home_operators,  # Same as threshold discount
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=self.period,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.5"),
                )
            ],
        )

        parent_discount.sub_discounts = (
            sub_discount_threshold,
            sub_discount_different_traffic,
            sub_discount_matching_traffic,
        )

        discount_repository = InMemoryDiscountRepository(
            [
                parent_discount,
                sub_discount_threshold,
                sub_discount_different_traffic,
                sub_discount_matching_traffic,
            ]
        )

        filler = self.resolver_cls(discount_repository)
        filler.fill_financial_threshold_rate(parent_discount)

        # Different traffic discount should NOT have threshold rate applied
        updated_different = discount_repository.get_by_id(sub_discount_different_traffic.id)
        assert updated_different.above_financial_threshold_rate is None

        # Matching traffic discount should have threshold rate applied
        updated_matching = discount_repository.get_by_id(sub_discount_matching_traffic.id)
        assert updated_matching.above_financial_threshold_rate == expected_rate

    def test_fill_financial_threshold_rate_multiple_threshold_parameters(self):
        """Test handling of multiple threshold parameters in different sub-discounts."""
        rate_1 = Decimal("0.010")
        rate_2 = Decimal("0.015")
        agreement_id = 5555

        parent_discount = self.discount_factory()

        # First sub-discount with threshold parameter
        sub_discount_threshold_1 = self.discount_factory(
            id=501,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    basis_value=rate_1,
                )
            ],
        )

        # Second sub-discount with different threshold parameter
        sub_discount_threshold_2 = self.discount_factory(
            id=502,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    basis_value=rate_2,
                )
            ],
        )

        # Regular sub-discount that should receive both threshold rates
        sub_discount_regular = self.discount_factory(
            id=503,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.6"),
                )
            ],
        )

        parent_discount.sub_discounts = (
            sub_discount_threshold_1,
            sub_discount_threshold_2,
            sub_discount_regular,
        )

        discount_repository = InMemoryDiscountRepository(
            [
                parent_discount,
                sub_discount_threshold_1,
                sub_discount_threshold_2,
                sub_discount_regular,
            ]
        )

        filler = self.resolver_cls(discount_repository)
        filler.fill_financial_threshold_rate(parent_discount)

        # Both threshold discounts should be deleted
        try:
            discount_repository.get_by_id(sub_discount_threshold_1.id)
            assert False, "First threshold discount should have been deleted"
        except Exception:
            pass

        try:
            discount_repository.get_by_id(sub_discount_threshold_2.id)
            assert False, "Second threshold discount should have been deleted"
        except Exception:
            pass

        # Regular discount should have the last applied threshold rate
        # (order depends on iteration, but should have one of the rates)
        updated_regular = discount_repository.get_by_id(sub_discount_regular.id)
        assert updated_regular.above_financial_threshold_rate in [rate_1, rate_2]

    def test_fill_financial_threshold_rate_no_sub_discounts(self):
        """Test that method handles parent discount with no sub-discounts gracefully."""
        parent_discount = self.discount_factory()
        parent_discount.sub_discounts = tuple()

        discount_repository = InMemoryDiscountRepository([parent_discount])

        filler = self.resolver_cls(discount_repository)
        # Should not raise any exceptions
        filler.fill_financial_threshold_rate(parent_discount)

    def test_fill_financial_threshold_rate_no_threshold_parameters(self):
        """Test that method handles discounts with no threshold parameters gracefully."""
        agreement_id = 4444

        parent_discount = self.discount_factory()

        # Sub-discount with only regular parameters
        sub_discount_regular = self.discount_factory(
            id=601,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.7"),
                )
            ],
        )

        parent_discount.sub_discounts = (sub_discount_regular,)

        discount_repository = InMemoryDiscountRepository([parent_discount, sub_discount_regular])

        filler = self.resolver_cls(discount_repository)
        filler.fill_financial_threshold_rate(parent_discount)

        # Sub-discount should remain unchanged
        updated_regular = discount_repository.get_by_id(sub_discount_regular.id)
        assert updated_regular.above_financial_threshold_rate is None
        assert len(updated_regular.parameters) == 1

    def test_fill_financial_threshold_rate_wrong_calculation_type(self):
        """Test that only STEPPED_TIERED calculation type with FINANCIAL_THRESHOLD bound is processed."""
        agreement_id = 3333

        parent_discount = self.discount_factory()

        # Sub-discount with FINANCIAL_THRESHOLD bound but wrong calculation type
        sub_discount_wrong_calc = self.discount_factory(
            id=701,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    calculation_type=DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE,  # Wrong type
                    basis_value=Decimal("0.009"),
                )
            ],
        )

        # Regular sub-discount
        sub_discount_regular = self.discount_factory(
            id=702,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.8"),
                )
            ],
        )

        parent_discount.sub_discounts = (sub_discount_wrong_calc, sub_discount_regular)

        discount_repository = InMemoryDiscountRepository(
            [
                parent_discount,
                sub_discount_wrong_calc,
                sub_discount_regular,
            ]
        )

        filler = self.resolver_cls(discount_repository)
        filler.fill_financial_threshold_rate(parent_discount)

        # Both discounts should remain unchanged
        updated_wrong_calc = discount_repository.get_by_id(sub_discount_wrong_calc.id)
        assert updated_wrong_calc.above_financial_threshold_rate is None
        assert len(updated_wrong_calc.parameters) == 1

        updated_regular = discount_repository.get_by_id(sub_discount_regular.id)
        assert updated_regular.above_financial_threshold_rate is None
        assert len(updated_regular.parameters) == 1

    def test_fill_financial_threshold_rate_none_basis_value(self):
        """Test handling of threshold parameter with None basis_value."""
        agreement_id = 2222

        parent_discount = self.discount_factory()

        # Sub-discount with threshold parameter but None basis_value
        sub_discount_none_value = self.discount_factory(
            id=801,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    basis_value=None,
                )
            ],
        )

        # Regular sub-discount
        sub_discount_regular = self.discount_factory(
            id=802,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.VOLUME,
                    basis_value=Decimal("0.9"),
                )
            ],
        )

        parent_discount.sub_discounts = (sub_discount_none_value, sub_discount_regular)

        discount_repository = InMemoryDiscountRepository(
            [
                parent_discount,
                sub_discount_none_value,
                sub_discount_regular,
            ]
        )

        filler = self.resolver_cls(discount_repository)
        filler.fill_financial_threshold_rate(parent_discount)

        # Threshold discount should be deleted
        try:
            discount_repository.get_by_id(sub_discount_none_value.id)
            assert False, "Threshold discount with None value should have been deleted"
        except Exception:
            pass

        # Regular discount should have None threshold rate applied
        updated_regular = discount_repository.get_by_id(sub_discount_regular.id)
        assert updated_regular.above_financial_threshold_rate is None
