from datetime import date
from decimal import Decimal
from functools import partial

from nga.apps.agreements.enums import (
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
)
from nga.apps.iotron.processing.discount_financial_threshold_rate import DiscountFinancialThresholdRateFiller
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod
from tests.apps.agreements.fakes import InMemoryDiscountRepository
from tests.factories.agreements.domain import (
    SREDiscountFactory,
    SREDiscountParameterFactory,
    SteppedTieredDiscountParameterFactory,
)


class TestDiscountFinancialThresholdRateFiller:
    resolver_cls = DiscountFinancialThresholdRateFiller

    home_operators = (4, 5)
    partner_operators = (6, 7)
    period = DatePeriod(date(2024, 1, 1), date(2024, 7, 1))
    service_types = (ServiceTypeEnum.VOICE_MO,)
    direction = DiscountDirectionEnum.BIDIRECTIONAL

    discount_factory = staticmethod(
        partial(
            SREDiscountFactory,
            home_operators=home_operators,
            partner_operators=partner_operators,
            direction=direction,
            service_types=service_types,
            period=period,
            traffic_segments=(),
            above_commitment_rate=None,
            model_type=None,
        )
    )

    def test_fill_financial_threshold_rate(self):
        expected_rate = Decimal("0.007")
        agreement_id = 9999

        parent_discount = self.discount_factory()

        sub_discount_1 = self.discount_factory(
            id=101,
            parent_id=parent_discount.id,
            agreement_id=agreement_id,
            parameters=[
                SteppedTieredDiscountParameterFactory(
                    bound_type=DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
                    calculation_type=DiscountCalculationTypeEnum.STEPPED_TIERED,
                    basis_value=expected_rate,
                )
            ],
        )

        sub_discount_2 = self.discount_factory(
            id=102,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=None,
                    basis_value=Decimal("0.1"),
                )
            ],
        )

        sub_discount_3 = self.discount_factory(
            id=103,
            agreement_id=agreement_id,
            parent_id=parent_discount.id,
            parameters=[
                SREDiscountParameterFactory(
                    bound_type=None,
                    basis_value=Decimal("0.15"),
                )
            ],
        )

        parent_discount.sub_discounts = (sub_discount_1, sub_discount_2, sub_discount_3)

        discount_repository = InMemoryDiscountRepository(
            [parent_discount, sub_discount_1, sub_discount_2, sub_discount_3]
        )

        filler = self.resolver_cls(discount_repository)
        filler.fill_financial_threshold_rate(parent_discount)

        # Discount with financial threshold parameter should have parameters cleared
        updated_sub_2 = discount_repository.get_by_id(sub_discount_2.id)
        assert len(updated_sub_2.parameters) != 0

        # Other discounts should be updated with the threshold rate
        updated_sub_2 = discount_repository.get_by_id(sub_discount_2.id)
        assert updated_sub_2.above_financial_threshold_rate == expected_rate

        updated_sub_3 = discount_repository.get_by_id(sub_discount_3.id)
        assert updated_sub_3.above_financial_threshold_rate == expected_rate
