import pytest

from nga.apps.agreements.enums import (
    DiscountBoundTypeEnum,
    DiscountDirectionEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
)
from nga.apps.iotron.domain.mappers import (
    DISCOUNT_CALL_DESTINATION_MAP,
    DISCOUNT_DIRECTION_MAP,
    DISCOUNT_IMSI_COUNT_TYPE_MAP,
    DISCOUNT_PARAMETER_BOUND_TYPE_MAP,
    DISCOUNT_PARAMETER_QUALIFYING_BASIS_MAP,
    DISCOUNT_SETTLEMENT_METHOD_MAP,
    DISCOUNT_TAX_MAP,
    DISCOUNT_VOLUME_TYPE_MAP,
)
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, VolumeTypeEnum


class TestDiscountParameterBoundTypeMap:
    """Test cases for DISCOUNT_PARAMETER_BOUND_TYPE_MAP."""

    def test_volume_mapping(self):
        assert DISCOUNT_PARAMETER_BOUND_TYPE_MAP["Volume"] == DiscountBoundTypeEnum.VOLUME

    def test_financial_commitment_mapping(self):
        assert DISCOUNT_PARAMETER_BOUND_TYPE_MAP["Financial Commitment"] == DiscountBoundTypeEnum.FINANCIAL_COMMITMENT

    def test_market_share_mapping(self):
        assert DISCOUNT_PARAMETER_BOUND_TYPE_MAP["Market Share %"] == DiscountBoundTypeEnum.MARKET_SHARE

    def test_unique_imsi_count_per_month_mapping(self):
        assert DISCOUNT_PARAMETER_BOUND_TYPE_MAP["Unique IMSI Count per Month"] == DiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH

    def test_volume_included_in_access_fee_mapping(self):
        assert DISCOUNT_PARAMETER_BOUND_TYPE_MAP["Volume included in Access Fee"] == DiscountBoundTypeEnum.VOLUME_INCLUDED_IN_ACCESS_FEE

    def test_financial_threshold_mapping(self):
        """Test the newly added Financial Threshold mapping."""
        assert DISCOUNT_PARAMETER_BOUND_TYPE_MAP["Financial Threshold"] == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD

    def test_all_bound_types_are_mapped(self):
        """Ensure all expected bound types are present in the mapping."""
        expected_keys = {
            "Volume",
            "Financial Commitment", 
            "Market Share %",
            "Unique IMSI Count per Month",
            "Volume included in Access Fee",
            "Financial Threshold",
        }
        assert set(DISCOUNT_PARAMETER_BOUND_TYPE_MAP.keys()) == expected_keys

    def test_all_enum_values_are_mapped(self):
        """Ensure all DiscountBoundTypeEnum values are mapped."""
        expected_enum_values = {
            DiscountBoundTypeEnum.VOLUME,
            DiscountBoundTypeEnum.FINANCIAL_COMMITMENT,
            DiscountBoundTypeEnum.MARKET_SHARE,
            DiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH,
            DiscountBoundTypeEnum.VOLUME_INCLUDED_IN_ACCESS_FEE,
            DiscountBoundTypeEnum.FINANCIAL_THRESHOLD,
        }
        assert set(DISCOUNT_PARAMETER_BOUND_TYPE_MAP.values()) == expected_enum_values


class TestDiscountSettlementMethodMap:
    """Test cases for DISCOUNT_SETTLEMENT_METHOD_MAP."""

    def test_credit_note_eoa_mapping(self):
        assert DISCOUNT_SETTLEMENT_METHOD_MAP["Credit Note EoA"] == DiscountSettlementMethodEnum.CREDIT_NOTE_EOA

    def test_tap_level_dch_mapping(self):
        assert DISCOUNT_SETTLEMENT_METHOD_MAP["TAP Level (DCH)"] == DiscountSettlementMethodEnum.TAP_LVL_DCH

    def test_credit_note_monthly_mapping(self):
        assert DISCOUNT_SETTLEMENT_METHOD_MAP["Credit Note Monthly"] == DiscountSettlementMethodEnum.CREDIT_NOTE_MONTHLY


class TestDiscountTaxMap:
    """Test cases for DISCOUNT_TAX_MAP."""

    def test_net_mapping(self):
        assert DISCOUNT_TAX_MAP["Net"] is False

    def test_gross_mapping(self):
        assert DISCOUNT_TAX_MAP["Gross"] is True


class TestDiscountVolumeTypeMap:
    """Test cases for DISCOUNT_VOLUME_TYPE_MAP."""

    def test_actual_mapping(self):
        assert DISCOUNT_VOLUME_TYPE_MAP["Actual"] == VolumeTypeEnum.ACTUAL

    def test_billed_mapping(self):
        assert DISCOUNT_VOLUME_TYPE_MAP["Billed"] == VolumeTypeEnum.BILLED


class TestDiscountImsiCountTypeMap:
    """Test cases for DISCOUNT_IMSI_COUNT_TYPE_MAP."""

    def test_data_mapping(self):
        assert DISCOUNT_IMSI_COUNT_TYPE_MAP["Data"] == IMSICountTypeEnum.DATA

    def test_no_data_mapping(self):
        assert DISCOUNT_IMSI_COUNT_TYPE_MAP["No data"] == IMSICountTypeEnum.NO_DATA


class TestDiscountDirectionMap:
    """Test cases for DISCOUNT_DIRECTION_MAP."""

    def test_bidirectional_mapping(self):
        assert DISCOUNT_DIRECTION_MAP["Bi-Directional"] == DiscountDirectionEnum.BIDIRECTIONAL

    def test_outbound_mapping(self):
        assert DISCOUNT_DIRECTION_MAP["Customer/Outbound"] == DiscountDirectionEnum.OUTBOUND

    def test_inbound_mapping(self):
        assert DISCOUNT_DIRECTION_MAP["Visitor/Inbound"] == DiscountDirectionEnum.INBOUND


class TestDiscountCallDestinationMap:
    """Test cases for DISCOUNT_CALL_DESTINATION_MAP."""

    def test_home_mapping(self):
        assert DISCOUNT_CALL_DESTINATION_MAP["HOM"] == CallDestinationEnum.HOME

    def test_local_mapping(self):
        assert DISCOUNT_CALL_DESTINATION_MAP["LOC"] == CallDestinationEnum.LOCAL

    def test_international_mapping(self):
        assert DISCOUNT_CALL_DESTINATION_MAP["INT"] == CallDestinationEnum.INTERNATIONAL

    def test_unknown_mapping(self):
        assert DISCOUNT_CALL_DESTINATION_MAP["UNKNOWN"] == CallDestinationEnum.UNKNOWN


class TestDiscountParameterQualifyingBasisMap:
    """Test cases for DISCOUNT_PARAMETER_QUALIFYING_BASIS_MAP."""

    def test_volume_mapping(self):
        assert DISCOUNT_PARAMETER_QUALIFYING_BASIS_MAP["Volume"] == DiscountQualifyingBasisEnum.VOLUME

    def test_market_share_mapping(self):
        assert DISCOUNT_PARAMETER_QUALIFYING_BASIS_MAP["Market Share %"] == DiscountQualifyingBasisEnum.MARKET_SHARE_PERCENTAGE

    def test_unique_imsi_count_per_month_mapping(self):
        assert DISCOUNT_PARAMETER_QUALIFYING_BASIS_MAP["Unique IMSI Count per Month"] == DiscountQualifyingBasisEnum.UNIQUE_IMSI_COUNT_PER_MONTH

    def test_average_monthly_usage_per_imsi_mapping(self):
        assert DISCOUNT_PARAMETER_QUALIFYING_BASIS_MAP["Average Monthly Usage per IMSI"] == DiscountQualifyingBasisEnum.AVERAGE_MONTHLY_USAGE_PER_IMSI
