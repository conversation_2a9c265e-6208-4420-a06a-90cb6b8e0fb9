from datetime import datetime, timezone

import pytest
from dateutil.relativedelta import relativedelta

from nga.apps.import_export_data import consts
from nga.apps.import_export_data.models import ImportDataJob
from tests.factories.import_export_data.orm import ImportDataJobORMFactory


@pytest.mark.django_db
class TestImportDataJobManager:
    def test_last_auto_run_sync_date(self):
        # outdated job
        ImportDataJobORMFactory(
            model=consts.EXTERNAL_AGREEMENTS_RECORD,
            imported=datetime(2025, 7, 1, 0, 10, 12, tzinfo=timezone.utc),
            auto_run=True,
            processing_initiated=datetime(2025, 6, 1, 0, 10, 12, tzinfo=timezone.utc),
        )
        expected_job = ImportDataJobORMFactory(
            model=consts.EXTERNAL_AGREEMENTS_RECORD,
            imported=datetime(2025, 7, 1, 0, 10, 12, tzinfo=timezone.utc),
            auto_run=True,
            processing_initiated=datetime(2025, 7, 1, 0, 10, 12, tzinfo=timezone.utc),
        )

        # noise jobs
        ImportDataJobORMFactory(
            model=consts.EXTERNAL_AGREEMENTS_RECORD,
            imported=None,  # not imported yet
            auto_run=True,
            processing_initiated=datetime(2025, 7, 1, 0, 10, 12, tzinfo=timezone.utc),
        )
        ImportDataJobORMFactory(
            model=consts.EXTERNAL_AGREEMENTS_RECORD,
            imported=datetime(2025, 7, 2, 0, 10, 12, tzinfo=timezone.utc),
            auto_run=False,  # manual job
            processing_initiated=datetime(2025, 7, 2, 0, 10, 12, tzinfo=timezone.utc),
        )
        ImportDataJobORMFactory(
            model=consts.IMSI_COUNT_RECORD,  # diff model
            imported=datetime(2025, 7, 1, 0, 10, 12, tzinfo=timezone.utc),
            auto_run=True,
            processing_initiated=datetime(2025, 7, 1, 0, 10, 12, tzinfo=timezone.utc),
        )

        last_sync_date = ImportDataJob.objects.last_auto_run_sync_date(consts.EXTERNAL_AGREEMENTS_RECORD)

        assert last_sync_date == expected_job.processing_initiated

    def test_last_auto_run_sync_date_when_there_are_no_jobs(self):
        ImportDataJobORMFactory(
            model=consts.EXTERNAL_AGREEMENTS_RECORD,
            imported=None,  # not imported yet
            auto_run=True,
            processing_initiated=datetime(2025, 7, 1, 0, 10, 12, tzinfo=timezone.utc),
        )
        ImportDataJobORMFactory(
            model=consts.EXTERNAL_AGREEMENTS_RECORD,
            imported=datetime(2025, 7, 2, 0, 10, 12, tzinfo=timezone.utc),
            auto_run=False,  # manual job
            processing_initiated=datetime(2025, 7, 2, 0, 10, 12, tzinfo=timezone.utc),
        )

        last_sync_date = ImportDataJob.objects.last_auto_run_sync_date(consts.EXTERNAL_AGREEMENTS_RECORD)

        expected_date = datetime.now(timezone.utc) - relativedelta(months=1)

        assert last_sync_date.year == expected_date.year
        assert last_sync_date.month == expected_date.month
        assert last_sync_date.day == expected_date.day
