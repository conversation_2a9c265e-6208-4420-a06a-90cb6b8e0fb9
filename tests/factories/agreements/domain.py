from datetime import timezone
from decimal import Decimal
from typing import Optional

import factory
from factory import Faker, post_generation

from nga.apps.agreements.domain.dto import BudgetAgreementCreateDTO, DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.domain.models import (
    Agreement,
    AgreementNegotiator,
    BudgetAgreement,
    Discount,
    DiscountParameter,
    DiscountTrafficParameters,
)
from nga.apps.agreements.domain.models.discount import CommitmentDistributionParameter
from nga.apps.agreements.domain.value_objects import DiscountQualifyingRule
from nga.apps.agreements.enums import (
    AgreementCalculationStatusEnum,
    AgreementStatusEnum,
    DiscountBalancingEnum,
    DiscountBasisEnum,
    DiscountBoundTypeEnum,
    DiscountCalculationTypeEnum,
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountQualifyingBasisEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, VolumeTypeEnum
from nga.core.types import DatePeriod
from tests.factories.fields import FakeDecimal
from tests.factories.types import DatePeriodFactory
from tests.factories.utils import TupleFactory


class AgreementFactory(factory.Factory):
    id: int = factory.Sequence(lambda n: n)

    external_id = Faker("pyint")
    name: str = Faker("pystr")

    parent_id = None

    status: AgreementStatusEnum = AgreementStatusEnum.DRAFT
    period: DatePeriod = factory.SubFactory(DatePeriodFactory)
    home_operators = [1, 2]
    partner_operators = [3, 4]

    negotiator_id = None

    include_satellite = True

    include_premium = True

    include_premium_in_commitment = True

    is_rolling = True

    updated_at = Faker("date_time", tzinfo=timezone.utc)

    class Meta:
        model = Agreement


class AgreementNegotiatorFactory(factory.Factory):
    id: int = factory.Sequence(lambda n: n)
    name: str = Faker("pystr")

    class Meta:
        model = AgreementNegotiator


class BudgetAgreementFactory(AgreementFactory):
    budget_id: int = Faker("pyint")
    agreement_id: int = Faker("pyint")

    is_active = False
    applied_at = None

    calculation_status = AgreementCalculationStatusEnum.NOT_APPLIED

    class Meta:
        model = BudgetAgreement


class AgreementCreateDTOFactory(factory.Factory):
    name: str = Faker("pystr")

    home_operators = [1, 2]
    partner_operators = [3, 4]

    period: DatePeriod = factory.SubFactory(DatePeriodFactory)

    include_satellite: bool = True
    include_premium: bool = True
    include_premium_in_commitment: bool = True

    is_rolling: bool = True

    negotiator_id: Optional[int] = None

    class Meta:
        model = BudgetAgreementCreateDTO


class DiscountParameterDTOFactory(factory.Factory):
    calculation_type: DiscountCalculationTypeEnum = Faker("random_element", elements=DiscountCalculationTypeEnum)

    basis: Optional[DiscountBasisEnum] = Faker("random_element", elements=DiscountBasisEnum)
    basis_value: Optional[Decimal] = FakeDecimal()

    balancing: Optional[DiscountBalancingEnum] = Faker("random_element", elements=DiscountBalancingEnum)

    bound_type: Optional[DiscountBoundTypeEnum] = Faker("random_element", elements=DiscountBoundTypeEnum)
    lower_bound = FakeDecimal(right_digits=2, min_value=Decimal("0"), max_value=Decimal("1000"))
    upper_bound = FakeDecimal(right_digits=2, min_value=Decimal("1000"))

    toll_rate: Optional[Decimal] = FakeDecimal()
    airtime_rate: Optional[Decimal] = FakeDecimal()

    fair_usage_rate: Optional[Decimal] = FakeDecimal()
    fair_usage_threshold: Optional[Decimal] = FakeDecimal(right_digits=2)

    access_fee_rate: Optional[Decimal] = FakeDecimal()
    incremental_rate: Optional[Decimal] = FakeDecimal()

    class Meta:
        model = DiscountParameterDTO


class DiscountParameterFactory(DiscountParameterDTOFactory):
    discount_id: int = Faker("pyint")

    class Meta:
        model = DiscountParameter


class SREDiscountParameterFactory(DiscountParameterFactory):
    calculation_type: DiscountCalculationTypeEnum = DiscountCalculationTypeEnum.SINGLE_RATE_EFFECTIVE
    basis = DiscountBasisEnum.VALUE
    balancing = None
    bound_type = DiscountBoundTypeEnum.VOLUME


class SREBalancedDiscountParameterFactory(SREDiscountParameterFactory):
    balancing = DiscountBalancingEnum.BALANCED


class SREUnbalancedDiscountParameterFactory(SREDiscountParameterFactory):
    balancing = DiscountBalancingEnum.UNBALANCED


class SteppedTieredDiscountParameterFactory(DiscountParameterFactory):
    calculation_type: DiscountCalculationTypeEnum = DiscountCalculationTypeEnum.STEPPED_TIERED
    basis = DiscountBasisEnum.VALUE
    balancing = DiscountBalancingEnum.NO_BALANCING
    bound_type = DiscountBoundTypeEnum.VOLUME
    lower_bound = Decimal("0")


class SteppedTieredUnbalancedDiscountParameterFactory(SteppedTieredDiscountParameterFactory):
    balancing = DiscountBalancingEnum.UNBALANCED


class SoPTrafficDiscountParameterFactory(DiscountParameterFactory):
    calculation_type: DiscountCalculationTypeEnum = DiscountCalculationTypeEnum.SEND_OR_PAY_TRAFFIC
    basis = DiscountBasisEnum.VALUE
    balancing = DiscountBalancingEnum.NO_BALANCING
    bound_type = DiscountBoundTypeEnum.VOLUME


class SoPFinancialDiscountParameterFactory(DiscountParameterFactory):
    basis = DiscountBasisEnum.VALUE
    balancing = DiscountBalancingEnum.NO_BALANCING
    bound_type = DiscountBoundTypeEnum.FINANCIAL_COMMITMENT
    calculation_type = DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL


class BackToFirstDiscountParameterFactory(DiscountParameterFactory):
    basis = DiscountBasisEnum.VALUE
    balancing = DiscountBalancingEnum.NO_BALANCING
    bound_type = DiscountBoundTypeEnum.VOLUME
    calculation_type = DiscountCalculationTypeEnum.BACK_TO_FIRST
    lower_bound = Decimal("0")
    upper_bound = None


class PMPIDiscountParameterFactory(DiscountParameterFactory):
    basis = DiscountBasisEnum.VALUE
    balancing = None
    calculation_type = DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI


class PMPIAboveThresholdDiscountParameterFactory(DiscountParameterFactory):
    basis = DiscountBasisEnum.VALUE
    balancing = None
    bound_type = DiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH
    calculation_type = DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_ABOVE_THRESHOLD


class PMPISteppedTieredDiscountParameterFactory(DiscountParameterFactory):
    basis = DiscountBasisEnum.VALUE
    balancing = None
    bound_type = DiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH
    calculation_type = DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED
    lower_bound = Decimal("0")
    upper_bound = Decimal("3")


class PMPIWithIncrementalChargingDiscountParameterFactory(DiscountParameterFactory):
    balancing = None
    bound_type = DiscountBoundTypeEnum.VOLUME_INCLUDED_IN_ACCESS_FEE
    calculation_type = DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING
    lower_bound = Decimal("34")


class PMPIBackToFirstDiscountParameterFactory(DiscountParameterFactory):
    basis = DiscountBasisEnum.VALUE
    bound_type = DiscountBoundTypeEnum.UNIQUE_IMSI_COUNT_PER_MONTH
    calculation_type = DiscountCalculationTypeEnum.PER_MONTH_PER_IMSI_BACK_TO_FIRST


class AYCEDiscountParameterFactory(DiscountParameterFactory):
    balancing = DiscountBalancingEnum.NO_BALANCING
    bound_type = DiscountBoundTypeEnum.FINANCIAL_COMMITMENT
    calculation_type = DiscountCalculationTypeEnum.ALL_YOU_CAN_EAT

class FinancialThresholdDiscountParameterFactory(DiscountParameterFactory):
    calculation_type = DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD
    bound_type = DiscountBoundTypeEnum.FINANCIAL_THRESHOLD
    lower_bound = FakeDecimal()


class DiscountTrafficParametersFactory(factory.Factory):
    home_operators: tuple[int] = (1, 2)
    partner_operators: tuple[int] = (3, 4)

    direction: DiscountDirectionEnum = Faker("random_element", elements=DiscountDirectionEnum)
    service_types: tuple[ServiceTypeEnum] = (ServiceTypeEnum.VOICE_MO,)
    period: DatePeriod = DatePeriodFactory()

    call_destinations = None
    called_countries = None
    traffic_segments = None

    imsi_count_type = None

    class Meta:
        model = DiscountTrafficParameters


class DiscountQualifyingRuleFactory(factory.Factory):
    direction = Faker("random_element", elements=DiscountDirectionEnum)

    service_types = (ServiceTypeEnum.SMS_MO,)

    basis = Faker("random_element", elements=DiscountQualifyingBasisEnum)

    volume_type = Faker("random_element", elements=VolumeTypeEnum)

    lower_bound = FakeDecimal()

    upper_bound = None

    class Meta:
        model = DiscountQualifyingRule


class DiscountFactory(DiscountTrafficParametersFactory):
    id: int = factory.Sequence(lambda n: n)
    agreement_id = Faker("pyint")

    currency_code: str = Faker("currency_code")

    tax_type: TaxTypeEnum = Faker("random_element", elements=TaxTypeEnum)
    volume_type: VolumeTypeEnum = Faker("random_element", elements=VolumeTypeEnum)

    settlement_method = DiscountSettlementMethodEnum.CREDIT_NOTE_EOA

    qualifying_rule: Optional[DiscountQualifyingRule] = None

    parent_id = None

    parameters = factory.List([factory.SubFactory(DiscountParameterFactory)], list_factory=TupleFactory)

    sub_discounts = tuple()

    model_type = Faker("random_element", elements=DiscountModelTypeEnum)

    include_premium = True

    include_premium_in_commitment = True

    financial_threshold = None

    above_financial_threshold_rate = None

    above_commitment_rate = None

    inbound_market_share = None

    commitment_distribution_parameters = None

    class Meta:
        model = Discount

    @post_generation
    def post(self, create, extracted, **kwargs):

        if self.qualifying_rule is not None:
            self.qualifying_rule.volume_type = self.volume_type


class VolumeActualDiscountFactory(DiscountFactory):
    volume_type = VolumeTypeEnum.ACTUAL


class DiscountDTOFactory(factory.Factory):
    home_operators: tuple[int] = (1, 2)
    partner_operators: tuple[int] = (3, 4)

    direction: DiscountDirectionEnum = Faker("random_element", elements=DiscountDirectionEnum)
    service_types: tuple[ServiceTypeEnum] = (ServiceTypeEnum.VOICE_MO,)

    call_destinations: tuple[CallDestinationEnum] = tuple()
    called_countries: tuple[int] = tuple()
    traffic_segments: tuple[int] = tuple()

    imsi_count_type = IMSICountTypeEnum.DATA

    start_date = Faker("date_object")
    end_date = Faker("date_object")

    currency_code: str = Faker("currency_code")

    tax_type: TaxTypeEnum = Faker("random_element", elements=TaxTypeEnum)
    volume_type: VolumeTypeEnum = Faker("random_element", elements=VolumeTypeEnum)

    settlement_method: DiscountSettlementMethodEnum = Faker(
        "random_element",
        elements=DiscountSettlementMethodEnum,
    )

    qualifying_rule: Optional[DiscountQualifyingRule] = None

    model_type = Faker("random_element", elements=DiscountModelTypeEnum)

    above_commitment_rate: Optional[Decimal] = None

    inbound_market_share: Optional[Decimal] = None

    commitment_distribution_parameters: Optional[tuple[CommitmentDistributionParameter, ...]] = None

    parameters = factory.List([factory.SubFactory(DiscountParameterDTOFactory)])

    sub_discounts: Optional[list[int]] = None

    class Meta:
        model = DiscountDTO


class SREDiscountFactory(DiscountFactory):
    parameters = factory.List([factory.SubFactory(SREDiscountParameterFactory)])

    model_type = DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE


class SteppedTieredDiscountFactory(DiscountFactory):
    parameters = factory.List([factory.SubFactory(SteppedTieredDiscountParameterFactory)])

    model_type = DiscountModelTypeEnum.STEPPED_TIERED


class SoPTrafficSREDiscountFactory(DiscountFactory):
    direction: DiscountDirectionEnum = DiscountDirectionEnum.INBOUND
    inbound_market_share = Decimal("50.07")

    parameters = factory.List(
        [
            factory.SubFactory(SoPTrafficDiscountParameterFactory),
            factory.SubFactory(SREDiscountParameterFactory),
        ]
    )

    model_type = DiscountModelTypeEnum.SEND_OR_PAY_TRAFFIC_SINGLE_RATE_EFFECTIVE


class SoPTrafficSteppedTieredDiscountFactory(DiscountFactory):
    parameters = factory.List(
        [
            factory.SubFactory(SoPTrafficDiscountParameterFactory, lower_bound=Decimal("10")),
            factory.SubFactory(
                SteppedTieredDiscountParameterFactory,
                lower_bound=Decimal("0"),
                upper_bound=Decimal("10"),
            ),
            factory.SubFactory(SteppedTieredDiscountParameterFactory, lower_bound=Decimal("10")),
        ]
    )

    model_type = DiscountModelTypeEnum.SEND_OR_PAY_TRAFFIC_STEPPED_TIERED


class SoPFinancialDiscountFactory(DiscountFactory):
    parameters = factory.List([factory.SubFactory(SoPFinancialDiscountParameterFactory)])

    sub_discounts = factory.List([factory.SubFactory(SREDiscountFactory)])

    model_type = DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL


class BalancedUnbalancedSREDiscountFactory(DiscountFactory):
    parameters = factory.List(
        [
            factory.SubFactory(SREBalancedDiscountParameterFactory),
            factory.SubFactory(SREUnbalancedDiscountParameterFactory),
        ]
    )

    model_type = DiscountModelTypeEnum.BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE


class BalancedUnbalancedSteppedTieredDiscountFactory(DiscountFactory):
    parameters = factory.List(
        [
            factory.SubFactory(SREBalancedDiscountParameterFactory),
            factory.SubFactory(SteppedTieredUnbalancedDiscountParameterFactory),
        ]
    )

    model_type = DiscountModelTypeEnum.BALANCED_UNBALANCED_STEPPED_TIERED


class BackToFirstDiscountFactory(DiscountFactory):
    parameters = factory.List(
        [
            factory.SubFactory(BackToFirstDiscountParameterFactory, lower_bound=Decimal("0")),
            factory.SubFactory(BackToFirstDiscountParameterFactory, lower_bound=Decimal("100")),
        ]
    )

    model_type = DiscountModelTypeEnum.BACK_TO_FIRST


class PMPIDiscountFactory(DiscountFactory):
    service_types = (ServiceTypeEnum.ACCESS_FEE,)

    parameters = factory.List([factory.SubFactory(PMPIDiscountParameterFactory)])

    model_type = DiscountModelTypeEnum.PER_MONTH_PER_IMSI


class PMPIAboveThresholdDiscountFactory(DiscountFactory):
    service_types = (ServiceTypeEnum.ACCESS_FEE,)

    parameters = factory.List([factory.SubFactory(PMPIAboveThresholdDiscountParameterFactory)])

    model_type = DiscountModelTypeEnum.PER_MONTH_PER_IMSI_ABOVE_THRESHOLD


class PMPISteppedTieredDiscountFactory(DiscountFactory):
    service_types = (ServiceTypeEnum.ACCESS_FEE,)

    parameters = factory.List(
        [
            factory.SubFactory(PMPISteppedTieredDiscountParameterFactory),
        ]
    )

    model_type = DiscountModelTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED


class PMPIWithIncrementalChargingDiscountFactory(DiscountFactory):
    service_types = (ServiceTypeEnum.ACCESS_FEE, ServiceTypeEnum.DATA)

    imsi_count_type = IMSICountTypeEnum.DATA

    parameters = factory.List(
        [
            factory.SubFactory(PMPIWithIncrementalChargingDiscountParameterFactory),
        ]
    )

    model_type = DiscountModelTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING


class PMPIBackToFirstDiscountFactory(DiscountFactory):
    service_types = (ServiceTypeEnum.ACCESS_FEE,)

    parameters = factory.List([factory.SubFactory(PMPIBackToFirstDiscountParameterFactory)])

    model_type = DiscountModelTypeEnum.PER_MONTH_PER_IMSI_BACK_TO_FIRST


class AYCEDiscountFactory(DiscountFactory):
    parameters = factory.List([factory.SubFactory(AYCEDiscountParameterFactory)])

    model_type = DiscountModelTypeEnum.ALL_YOU_CAN_EAT


class CommitmentDistributionParameterFactory(factory.Factory):
    home_operators = (34, 11, 123)

    partner_operators = (45, 56)

    charge = FakeDecimal()

    class Meta:
        model = CommitmentDistributionParameter


class FinancialThresholdDiscountFactory(DiscountFactory):
    parameters = factory.List([factory.SubFactory(FinancialThresholdDiscountParameterFactory)])

    model_type = DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL
