from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.models.utils import calculate_n_months_period
from nga.apps.forecasts.domain import ForecastRule
from nga.core.enums import TrafficTypeEnum
from nga.core.types import DatePeriod

from .base import BaseLHMForecastDistributionModel


class MovingAverageDistributionModel(BaseLHMForecastDistributionModel):
    """
    MOVING_AVERAGE model uses last N-months of historical traffic to calculation operator distribution.
    Volume for a month should be distributed between operators and call destinations based on the last N-month
    historical traffic (average values).
    """

    def get_calculation_budget_traffic(self, forecast_rule: ForecastRule) -> BudgetTraffic[BudgetTrafficRecord]:
        period = self.evaluate_historical_traffic_period(forecast_rule)

        traffic = self._budget_traffic_provider.get(
            budget_id=forecast_rule.budget_id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            traffic_months=list(period),
            traffic_direction=forecast_rule.traffic_direction,
            traffic_types=[TrafficTypeEnum.HISTORICAL],
            service_type=forecast_rule.service_type,
        )

        return traffic

    @classmethod
    def evaluate_historical_traffic_period(cls, forecast_rule: ForecastRule) -> DatePeriod:
        last_historical_month = forecast_rule.get_last_historical_month()

        return calculate_n_months_period(
            expected_period_end_date=last_historical_month,
            months_before_end_date=forecast_rule.get_distribution_moving_average_months(),
        )
