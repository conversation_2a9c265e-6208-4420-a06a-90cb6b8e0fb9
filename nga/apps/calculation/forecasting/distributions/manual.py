import itertools
from decimal import Decimal
from typing import Collection

import pandas as pd

from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.consts import CALCULATION_COLUMNS
from nga.apps.calculation.forecasting.distributions.base import BaseForecastDistributionModel
from nga.apps.calculation.forecasting.distributions.operations import (
    aggregate_traffic_monthly_by_its_col_combination,
    fill_monthly_forecast_values,
    from_distribution_df_to_records,
    historical_traffic_to_df,
    replace_default_empty_operator_keys_with_none,
    replace_empty_operator_keys_with_default,
)
from nga.apps.calculation.forecasting.dto import ForecastDistributionRecord, MonthlyForecastRecord
from nga.apps.calculation.forecasting.dto_factories import ForecastDistributionRecordFactory
from nga.apps.calculation.forecasting.structures import ManualDistributionParameterContainer
from nga.apps.forecasts.domain import ForecastRule


class ManualDistributionModel(BaseForecastDistributionModel):
    def calculate(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
    ) -> tuple[ForecastDistributionRecord, ...]:
        """Performs volume & charge distribution between operators."""

        budget_traffic = self.get_calculation_budget_traffic(forecast_rule)

        if budget_traffic.empty:
            return self.calculate_without_budget_traffic(monthly_forecast_records, forecast_rule)

        return self.calculate_with_budget_traffic(monthly_forecast_records, forecast_rule, budget_traffic)

    def get_calculation_budget_traffic(self, forecast_rule: ForecastRule) -> BudgetTraffic[BudgetTrafficRecord]:
        budget_traffic = self._budget_traffic_provider.get(
            budget_id=forecast_rule.budget_id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            traffic_months=list(forecast_rule.period.previous_year),
            traffic_direction=forecast_rule.traffic_direction,
            service_type=forecast_rule.service_type,
        )

        return budget_traffic

    def calculate_without_budget_traffic(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
    ) -> tuple[ForecastDistributionRecord, ...]:
        """
        Performs distribution without historical traffic. Monthly forecast values are distributed evenly by applying
        distribution parameter operator share to each record.
        """

        distribution_params = ManualDistributionParameterContainer(forecast_rule.get_manual_distribution_parameters())

        operator_share_map = distribution_params.to_dict()

        partner_operators = distribution_params.get_partner_operators()

        distribution_records: list[ForecastDistributionRecord] = []

        for forecast_record in monthly_forecast_records:
            _records = self._create_distribution_records(
                forecast_record=forecast_record,
                forecast_rule=forecast_rule,
                for_partner_operators=partner_operators,
                operator_share_map=operator_share_map,
            )

            distribution_records.extend(_records)

        return tuple(distribution_records)

    @classmethod
    def _create_distribution_records(
        cls,
        forecast_record: MonthlyForecastRecord,
        forecast_rule: ForecastRule,
        for_partner_operators: Collection[int],
        operator_share_map: dict[int, Decimal],
    ) -> list[ForecastDistributionRecord]:
        """
        Creates distribution records for combination of:
            * [forecast_rule.home_operators]
            * [for_partner_operators]
            * [forecast_record.traffic_month]

        And evenly distributes monthly forecast value by applying manual distribution parameter (partner operator share)
        to each record.
        """

        values_combinations = itertools.product(*(forecast_rule.home_operators, for_partner_operators))

        total_records = forecast_rule.total_home_operators

        missing_distribution_records = []

        for home_operator_id, partner_operator_id in values_combinations:
            share = operator_share_map[partner_operator_id]

            coefficients: MonthlyForecastRecord = forecast_record * (share / total_records)

            _distribution_record = ForecastDistributionRecordFactory.create(
                forecast_rule=forecast_rule,
                coefficients=coefficients,
                home_operator_id=home_operator_id,
                partner_operator_id=partner_operator_id,
            )

            missing_distribution_records.append(_distribution_record)

        return missing_distribution_records

    def calculate_with_budget_traffic(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
        historical_traffic: BudgetTraffic[BudgetTrafficRecord],
    ) -> tuple[ForecastDistributionRecord, ...]:

        historical_traffic_df = historical_traffic_to_df(historical_traffic)
        historical_traffic_df = replace_empty_operator_keys_with_default(historical_traffic_df)

        operators_df = aggregate_traffic_monthly_by_its_col_combination(historical_traffic_df)

        operators_df = calculate_partner_operator_totals(operators_df, historical_traffic_df)

        operators_df = fill_forecasted_traffic_month(operators_df, forecast_rule)

        operators_df = fill_monthly_forecast_values(operators_df, monthly_forecast_records)

        distribution_params = ManualDistributionParameterContainer(forecast_rule.get_manual_distribution_parameters())

        operators_df = fill_manual_operator_shares(operators_df, distribution_params)

        for partner_operator_id in distribution_params.get_partner_operators():
            operator = operators_df["partner_operator_id"] == partner_operator_id

            coef = None

            column_values: pd.Series = operators_df.loc[operator, "volume_actual"]

            if column_values.all() == 0:
                # if volume_actual is zero for partner, we need to have an equal share for each record, based on
                # length of partner records sub-set and manual share. scalar value
                coef = operators_df.loc[operator, "manual_share"].values[0] / len(operators_df.loc[operator])

            for column in CALCULATION_COLUMNS:
                column_values = operators_df.loc[operator, column]

                if coef is None:
                    # if coef is not set, then it must be calculated based on each record share inside partner records
                    coef = column_values / operators_df.loc[operator, f"{column}_operator_total"]
                    coef *= operators_df.loc[operator, "manual_share"]

                operators_df.loc[operator, f"forecasted_{column}"] = (
                    coef * operators_df.loc[operator, f"{column}_monthly_forecasted"]
                )

        operators_df = replace_default_empty_operator_keys_with_none(operators_df)

        missing_distribution_records = self._create_distribution_records_for_missing_partner_operators(
            monthly_forecast_records=monthly_forecast_records,
            forecast_rule=forecast_rule,
            operators_df=operators_df,
        )

        distribution_records = from_distribution_df_to_records(operators_df) + missing_distribution_records

        return distribution_records

    def _create_distribution_records_for_missing_partner_operators(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
        operators_df: pd.DataFrame,
    ) -> tuple[ForecastDistributionRecord, ...]:
        """
        Creates distribution records for partner operators that are not present for concrete traffic months. If there
        are 2024-01 and 2024-02 traffic months, and ppmn1, ppm2 partner operators, and there is no traffic record with
        ppmn1 and 2024-01, that record will be created and distribute values evenly by number of these records.

        :param operators_df: pd.DataFrame
            Required columns:
                - forecasted_traffic_month  :date
                - partner_operator_id       :int
        :return:
            Collection of ForecastDistributionRecord objects
        """

        distribution_params = ManualDistributionParameterContainer(forecast_rule.get_manual_distribution_parameters())

        operator_share_map = distribution_params.to_dict()

        missing_distribution_records: list[ForecastDistributionRecord] = []

        partner_operators: set[int] = distribution_params.get_partner_operators()

        for forecast_record in monthly_forecast_records:
            monthly_partner_operators = operators_df.loc[
                operators_df["forecasted_traffic_month"] == forecast_record.traffic_month, "partner_operator_id"
            ]

            missing_partner_operators = partner_operators - set(monthly_partner_operators)

            if not missing_partner_operators:
                continue

            _records = self._create_distribution_records(
                forecast_record=forecast_record,
                forecast_rule=forecast_rule,
                for_partner_operators=missing_partner_operators,
                operator_share_map=operator_share_map,
            )

            missing_distribution_records.extend(_records)

        return tuple(missing_distribution_records)


def calculate_partner_operator_totals(
    agg_traffic_df: pd.DataFrame,
    historical_traffic_df: pd.DataFrame,
) -> pd.DataFrame:
    """
    Adds to input dataframe columns with total value by traffic_month and partner_operator.

    :param agg_traffic_df: pd.DataFrame
        Required columns:
            * partner_operator_id   :int
            * traffic_month         :date

    :param historical_traffic_df: pd.DataFrame
        Required columns:
            * partner_operator_id   :int
            * traffic_month         :date
            * CALCULATION_COLUMNS   :Decimal

    :return: pd.DataFrame
        Result columns:
            * partner_operator_id                       :int
            * traffic_month                             :date
            * {CALCULATION_COLUMNS}_operator_total
                Example:
                * volume_actual_operator_total          :Decimal
                * charge_net_operator_total             :Decimal
    """

    by_operator_df = historical_traffic_df.groupby(by=["traffic_month", "partner_operator_id"], as_index=False)[
        CALCULATION_COLUMNS
    ].apply(lambda x: x.sum())

    result_df = agg_traffic_df.merge(
        by_operator_df, on=("traffic_month", "partner_operator_id"), suffixes=("", "_operator_total")
    )

    return result_df


def fill_forecasted_traffic_month(df: pd.DataFrame, forecast_rule: ForecastRule) -> pd.DataFrame:
    """
    Inserts new column for input dataframe: forecasted_traffic_month. Filling this column is based on forecast_rule
    period and input dataframe "traffic_month". It sets value from traffic_month column but with year from forecast_rule
    period.

    :param df: pd.DataFrame
        Required columns:
        * traffic_month :date

    :param forecast_rule: ForecastRule

    :return: pd.DataFrame
        New added columns:
        * forecasted_traffic_month :date
    """

    df["forecasted_traffic_month"] = df["traffic_month"].apply(lambda x: forecast_rule.period.get_date_by_month(x))

    return df


def fill_manual_operator_shares(
    df: pd.DataFrame,
    distribution_parameters: ManualDistributionParameterContainer,
) -> pd.DataFrame:
    """
    Adds new columns to input dataframe by merging with distribution_parameters by partner_operator_id.

    :param df: pd.DataFrame
        Required columns:
        * partner_operator_id :int

    :param distribution_parameters: ManualDistributionParameterContainer

    :return: pd.DataFrame
        New added columns:
        * manual_share: Decimal
    """

    shares_df = pd.DataFrame(
        (
            {"partner_operator_id": sh.operator_id, "manual_share": sh.share}
            for sh in distribution_parameters.flat_shares()
        )
    )

    result_df = df.merge(shares_df, on=("partner_operator_id",))

    return result_df
