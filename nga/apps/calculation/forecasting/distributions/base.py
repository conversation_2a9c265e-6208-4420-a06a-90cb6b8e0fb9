import itertools
from abc import ABC, abstractmethod

import pandas as pd

from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.consts import CALCULATION_COLUMNS
from nga.apps.calculation.forecasting.dto import ForecastDistributionRecord, MonthlyForecastRecord
from nga.apps.calculation.forecasting.dto_factories import ForecastDistributionRecordFactory
from nga.apps.calculation.forecasting.providers import AbstractBudgetTrafficProvider
from nga.apps.forecasts.domain import ForecastRule
from nga.utils.calc import series_safely_divide

from .abstract import AbstractForecastDistributionModel
from .operations import (
    aggregate_traffic_by_its_col_combination,
    fill_monthly_forecast_values,
    from_distribution_df_to_records,
    historical_traffic_to_df,
    replace_default_empty_operator_keys_with_none,
    replace_empty_operator_keys_with_default,
)


class BaseForecastDistributionModel(AbstractForecastDistributionModel, ABC):
    def __init__(self, budget_traffic_provider: AbstractBudgetTrafficProvider) -> None:
        self._budget_traffic_provider = budget_traffic_provider

    def calculate(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
    ) -> tuple[ForecastDistributionRecord, ...]:
        """Performs volume & charge distribution between operators."""

        budget_traffic = self.get_calculation_budget_traffic(forecast_rule)

        if budget_traffic.no_volume_for_calculation:
            budget_traffic = self.get_full_budget_traffic(forecast_rule)

            if budget_traffic.no_volume_for_calculation:
                return self.calculate_without_budget_traffic(monthly_forecast_records, forecast_rule)

            return self.calculate_the_same_distribution_for_each_month_with_budget_traffic(
                monthly_forecast_records=monthly_forecast_records,
                forecast_rule=forecast_rule,
                historical_traffic=budget_traffic,
            )

        return self.calculate_with_budget_traffic(monthly_forecast_records, forecast_rule, budget_traffic)

    @abstractmethod
    def get_calculation_budget_traffic(self, forecast_rule: ForecastRule) -> BudgetTraffic[BudgetTrafficRecord]:
        """Returns budget traffic that will be used for distribution."""

    def get_full_budget_traffic(self, forecast_rule: ForecastRule) -> BudgetTraffic[BudgetTrafficRecord]:
        """Returns traffic for the whole budget historical period."""

        budget = forecast_rule.get_budget()

        budget_traffic = self._budget_traffic_provider.get(
            budget_id=budget.id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            traffic_direction=forecast_rule.traffic_direction,
            service_type=forecast_rule.service_type,
            period=budget.historical_period,
        )

        return budget_traffic

    @abstractmethod
    def calculate_with_budget_traffic(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
        historical_traffic: BudgetTraffic[BudgetTrafficRecord],
    ) -> tuple[ForecastDistributionRecord, ...]:
        """Calculates operator distribution based on historical traffic."""

    def calculate_without_budget_traffic(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
    ) -> tuple[ForecastDistributionRecord, ...]:
        """Calculates operator distribution without historical traffic."""

        distribution_records = []

        for monthly_record in monthly_forecast_records:
            total_records = len(forecast_rule.home_operators) * forecast_rule.partner.total_operators

            coefficients: MonthlyForecastRecord = monthly_record / total_records

            values_combinations = itertools.product(
                *(
                    forecast_rule.home_operators,
                    forecast_rule.partner.operators_ids,
                )
            )

            for home_operator_id, partner_operator_id in values_combinations:
                _distribution_record = ForecastDistributionRecordFactory.create(
                    forecast_rule=forecast_rule,
                    coefficients=coefficients,
                    home_operator_id=home_operator_id,
                    partner_operator_id=partner_operator_id,
                )

                distribution_records.append(_distribution_record)

        return tuple(distribution_records)

    def calculate_the_same_distribution_for_each_month_with_budget_traffic(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
        historical_traffic: BudgetTraffic[BudgetTrafficRecord],
    ) -> tuple[ForecastDistributionRecord, ...]:
        """Applies historical traffic distribution for each forecasted month."""

        historical_traffic_df = historical_traffic_to_df(historical_traffic)
        historical_traffic_df = replace_empty_operator_keys_with_default(historical_traffic_df)

        operators_df = aggregate_traffic_by_its_col_combination(historical_traffic_df)

        for calculation_column in CALCULATION_COLUMNS:
            operators_df[f"{calculation_column}_coefficient"] = series_safely_divide(
                operators_df[calculation_column], sum(operators_df[calculation_column])
            )

        records_df = pd.DataFrame(columns=operators_df.columns).astype(operators_df.dtypes)

        # copy traffic record combinations for every forecasted month
        for forecasted_traffic_month in forecast_rule.period:
            operators_df["forecasted_traffic_month"] = forecasted_traffic_month
            records_df = pd.concat([records_df, operators_df])

        operators_df = records_df
        operators_df.reset_index(inplace=True, drop=True)

        operators_df = fill_monthly_forecast_values(operators_df, monthly_forecast_records)

        # calculate shares
        for column in CALCULATION_COLUMNS:
            monthly_share = operators_df[f"{column}_coefficient"]
            operators_df[f"forecasted_{column}"] = monthly_share * operators_df[f"{column}_monthly_forecasted"]

        operators_df = replace_default_empty_operator_keys_with_none(operators_df)

        distribution_records = from_distribution_df_to_records(operators_df)

        return distribution_records


class BaseLHMForecastDistributionModel(BaseForecastDistributionModel, ABC):
    def calculate_with_budget_traffic(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
        historical_traffic: BudgetTraffic[BudgetTrafficRecord],
    ) -> tuple[ForecastDistributionRecord, ...]:
        """Calculates forecast distribution."""

        return self.calculate_the_same_distribution_for_each_month_with_budget_traffic(
            monthly_forecast_records=monthly_forecast_records,
            forecast_rule=forecast_rule,
            historical_traffic=historical_traffic,
        )
