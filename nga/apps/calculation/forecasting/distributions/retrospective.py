import pandas as pd
from dateutil.relativedelta import relativedelta

from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.consts import CALCULATION_COLUMNS
from nga.apps.calculation.forecasting.dto import ForecastDistributionRecord, MonthlyForecastRecord
from nga.apps.forecasts.domain import ForecastRule
from nga.core.types import MONTHS_IN_A_YEAR, DatePeriod
from nga.utils.calc import series_safely_divide

from .base import BaseForecastDistributionModel
from .operations import (
    aggregate_traffic_monthly_by_its_col_combination,
    fill_monthly_forecast_values,
    from_distribution_df_to_records,
    historical_traffic_to_df,
    replace_default_empty_operator_keys_with_none,
    replace_empty_operator_keys_with_default,
)


class RetrospectiveDistributionModel(BaseForecastDistributionModel):
    """
    RETROSPECTIVE traffic distribution model is responsible for distributing traffic (volume & charge) values between
    operators equally. Volume for a month should be distributed between operators and call destinations in the same
    proportion as in the retrospective month.
    """

    def get_calculation_budget_traffic(self, forecast_rule: ForecastRule) -> BudgetTraffic[BudgetTrafficRecord]:
        """Returns historical traffic for provided forecast rule."""

        traffic = self._budget_traffic_provider.get_last(
            budget_id=forecast_rule.budget_id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            period=forecast_rule.period.previous_year,
            traffic_direction=forecast_rule.traffic_direction,
            service_type=forecast_rule.service_type,
        )

        return traffic

    def calculate_with_budget_traffic(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
        historical_traffic: BudgetTraffic[BudgetTrafficRecord],
    ) -> tuple[ForecastDistributionRecord, ...]:
        """Calculates operator distribution based on historical traffic."""

        historical_traffic_df = historical_traffic_to_df(historical_traffic)
        historical_traffic_df = replace_empty_operator_keys_with_default(historical_traffic_df)

        monthly_df = historical_traffic_df.groupby(by=["traffic_month"], as_index=False)[CALCULATION_COLUMNS].apply(
            lambda x: x.sum()
        )

        monthly_df = self.roll_extra_period_monthly_records(
            monthly_df=monthly_df,
            forecasted_period=forecast_rule.period,
        )

        operators_df = aggregate_traffic_monthly_by_its_col_combination(historical_traffic_df)

        operators_df = operators_df.merge(monthly_df, on=["traffic_month"], suffixes=("", "_monthly"))

        operators_df = self.fill_forecasted_months(operators_df, forecast_rule.period)

        operators_df = fill_monthly_forecast_values(operators_df, monthly_forecast_records)

        for column in CALCULATION_COLUMNS:
            monthly_share = series_safely_divide(operators_df[column], operators_df[f"{column}_monthly"])
            operators_df[f"forecasted_{column}"] = monthly_share * operators_df[f"{column}_monthly_forecasted"]

        distribution_df = replace_default_empty_operator_keys_with_none(operators_df)

        operator_distribution = from_distribution_df_to_records(distribution_df)

        return operator_distribution

    @classmethod
    def roll_extra_period_monthly_records(cls, monthly_df: pd.DataFrame, forecasted_period: DatePeriod) -> pd.DataFrame:
        """
        If forecasted period is bigger than 1 year, result forecasted values should be rolled over by number of months
        that are out of forecasted period.

        Example:
            forecasted period - 15 months
            historical traffic that is used for calculation: 1-12 months && 1-3 (1-3 months are duplicated) = 15 months

        :param monthly_df:
            required df.types:
            ___________________
            traffic_month: date

        :param forecasted_period: DatePeriod

        :return pd.DataFrame
            new df.types:
            _____________
            rolling: bool
        """

        monthly_df["rolling"] = False

        if forecasted_period.total_months <= MONTHS_IN_A_YEAR:
            return monthly_df

        # duplicate forecasted records for over period dates
        period_months_for_duplicate = list(forecasted_period.previous_year)
        date_mask = monthly_df["traffic_month"].isin(
            period_months_for_duplicate[: forecasted_period.total_months - MONTHS_IN_A_YEAR]
        )
        rolling_df = monthly_df.loc[date_mask].copy()
        rolling_df["rolling"] = True

        monthly_df = pd.concat([monthly_df, rolling_df])
        monthly_df.reset_index(inplace=True)

        return monthly_df

    @classmethod
    def fill_forecasted_months(cls, df: pd.DataFrame, forecasted_period: DatePeriod) -> pd.DataFrame:
        """
        :param df:
            required df.types:
            ________________________
            traffic_month:      date
            rolling:            bool

        :param forecasted_period:
        :return: pd.DataFrame
            new df.types:
            ______________________________
            forecasted_traffic_month: date
        """

        # fill forecasted dates
        df["forecasted_traffic_month"] = df["traffic_month"].apply(lambda x: forecasted_period.get_date_by_month(x))

        rolling_month = df["rolling"] == True, "forecasted_traffic_month"  # noqa: E712

        df.loc[rolling_month] = df.loc[rolling_month].apply(lambda x: x + relativedelta(years=1))

        return df
