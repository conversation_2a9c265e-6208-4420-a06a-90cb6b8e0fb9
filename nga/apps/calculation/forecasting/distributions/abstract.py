from abc import ABC, abstractmethod

from nga.apps.calculation.forecasting.dto import ForecastDistributionRecord, MonthlyForecastRecord
from nga.apps.forecasts.domain import ForecastRule


class AbstractForecastDistributionModel(ABC):
    @abstractmethod
    def calculate(
        self,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        forecast_rule: ForecastRule,
    ) -> tuple[ForecastDistributionRecord, ...]:
        """Distributes forecasted volume between pair of operators (traffic record lvl)."""
