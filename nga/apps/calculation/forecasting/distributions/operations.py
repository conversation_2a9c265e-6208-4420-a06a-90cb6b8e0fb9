from dataclasses import asdict

import numpy as np
import pandas as pd

from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.consts import (
    CALCULATION_COLUMNS,
    EMPTY_OPERATOR_KEY_VALUE_FOR_DF,
    MONTHLY_OPERATORS_COMBINATION_COLUMNS,
    OPERATORS_COMBINATION_COLUMNS,
    OPERATORS_OPTIONAL_KEY_COLUMNS,
)
from nga.apps.calculation.forecasting.dto import ForecastDistributionRecord, MonthlyForecastRecord
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum


def replace_empty_operator_keys_with_default(df: pd.DataFrame) -> pd.DataFrame:
    """
    Replaces None, np.nan with EMPTY_OPERATOR_KEY_VALUE_FOR_DF = -1

    :param df: pd.DataFrame
        required columns:
        ____________________________________
        call_destination:   Optional[int64]
        called_country_id:  Optional[int64]
        is_premium:         Optional[boolean]
        traffic_segment_id: Optional[int64]

    :return: pd.DataFrame
    """

    with pd.option_context("future.no_silent_downcasting", True):
        df[OPERATORS_OPTIONAL_KEY_COLUMNS] = df[OPERATORS_OPTIONAL_KEY_COLUMNS].replace(
            [None, np.nan], EMPTY_OPERATOR_KEY_VALUE_FOR_DF
        )

    return df


def replace_default_empty_operator_keys_with_none(df: pd.DataFrame) -> pd.DataFrame:
    """
    Replaces EMPTY_OPERATOR_KEY_VALUE_FOR_DF = -1 with None

    :param df: pd.DataFrame
        required columns:
        ____________________________________
        call_destination:   Optional[int64]
        called_country_id:  Optional[int64]
        is_premium:         Optional[boolean]
        traffic_segment_id: Optional[int64]

    :return: pd.DataFrame
    """

    df[OPERATORS_OPTIONAL_KEY_COLUMNS] = df[OPERATORS_OPTIONAL_KEY_COLUMNS].replace(
        [EMPTY_OPERATOR_KEY_VALUE_FOR_DF], None
    )

    return df


def historical_traffic_to_df(traffic: BudgetTraffic[BudgetTrafficRecord]) -> pd.DataFrame:
    """
    Converts BudgetTraffic records to dataframe.

    :param traffic: BudgetTraffic[BudgetTraffic]
    :return: pd.DataFrame
    """

    df = pd.DataFrame((asdict(r) for r in traffic.records))

    return df


def aggregate_traffic_monthly_by_its_col_combination(traffic_df: pd.DataFrame) -> pd.DataFrame:
    """
    Aggregates input dataframe by MONTHLY_OPERATORS_COMBINATION_COLUMNS.

    Resulted DataFrame will contain next columns:
        *MONTHLY_OPERATORS_COMBINATION_COLUMNS
        *CALCULATION_COLUMNS

    :param traffic_df: pd.DataFrame
    :return: pd.DataFrame
    """

    agg_df = traffic_df.groupby(by=MONTHLY_OPERATORS_COMBINATION_COLUMNS, as_index=False)[CALCULATION_COLUMNS].apply(
        lambda x: x.sum()
    )

    return agg_df


def aggregate_traffic_by_its_col_combination(traffic_df: pd.DataFrame) -> pd.DataFrame:
    """
    Aggregates input dataframe by OPERATORS_COMBINATION_COLUMNS.

    Resulted DataFrame will contain next columns:
        *OPERATORS_COMBINATION_COLUMNS
        *CALCULATION_COLUMNS

    :param traffic_df: pd.DataFrame
    :return: pd.DataFrame
    """

    agg_df = traffic_df.groupby(by=OPERATORS_COMBINATION_COLUMNS, as_index=False)[CALCULATION_COLUMNS].apply(
        lambda x: x.sum()
    )

    return agg_df


def fill_monthly_forecast_values(
    df: pd.DataFrame,
    monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
) -> pd.DataFrame:
    """
    Adds to input dataframe new columns with values from monthly_forecast_records by merging it by traffic month.

    :param df: pd.DataFrame
        Required columns:
        * forecasted_traffic_month :date

    :param monthly_forecast_records: Collection of MonthlyForecastRecord objects

    :return: pd.DataFrame
        New added columns:
        * {CALCULATION_COLUMNS}_monthly_forecasted
            Example:
            * volume_actual_monthly_forecasted  :Decimal
            * charge_net_monthly_forecasted     :Decimal
    """

    result_df = df.merge(
        pd.DataFrame((asdict(m) for m in monthly_forecast_records)),
        left_on="forecasted_traffic_month",
        right_on="traffic_month",  # MonthlyForecastRecord.traffic_month
        suffixes=("", "_monthly_forecasted"),
    )

    return result_df


def from_distribution_df_to_records(distribution_df: pd.DataFrame) -> tuple[ForecastDistributionRecord, ...]:
    """
    Maps records from dataframe to collection of ForecastDistributionRecord objects.

    :param distribution_df:
        Required columns:
        * home_operator_id              :int
        * partner_operator_id           :int
        * traffic_segment_id            :int
        * call_destination              :Optional[int]
        * called_country_id             :Optional[int]
        * is_premium                    :Optional[bool]
        * forecasted_traffic_month      :date
        * forecasted_volume_actual      :Decimal
        * forecasted_volume_billed      :Decimal
        * forecasted_charge_net         :Decimal
        * forecasted_charge_gross       :Decimal
        * forecasted_tap_charge_net     :Decimal
        * forecasted_tap_charge_gross   :Decimal

    :return: tuple[ForecastDistributionRecord, ...]
    """

    operator_distribution_records = tuple(
        ForecastDistributionRecord(
            home_operator_id=r["home_operator_id"],
            partner_operator_id=r["partner_operator_id"],
            traffic_month=r["forecasted_traffic_month"],
            traffic_segment_id=r["traffic_segment_id"],
            call_destination=(
                CallDestinationEnum(r["call_destination"]) if r["call_destination"] is not None else None
            ),
            called_country_id=r["called_country_id"],
            is_premium=bool(r["is_premium"]) if r["is_premium"] is not None else None,
            imsi_count_type=(IMSICountTypeEnum(r["imsi_count_type"]) if r["imsi_count_type"] else None),
            volume_actual=r["forecasted_volume_actual"],
            volume_billed=r["forecasted_volume_billed"],
            charge_net=r["forecasted_charge_net"],
            charge_gross=r["forecasted_charge_gross"],
            tap_charge_net=r["forecasted_tap_charge_net"],
            tap_charge_gross=r["forecasted_tap_charge_gross"],
        )
        for r in distribution_df.to_dict("records")
    )

    return operator_distribution_records
