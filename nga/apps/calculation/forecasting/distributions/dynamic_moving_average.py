from dateutil.relativedelta import relativedelta

from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.models.utils import calculate_n_months_period
from nga.apps.forecasts.domain import ForecastRule

from .base import BaseLHMForecastDistributionModel


class DynamicMovingAverageDistributionModel(BaseLHMForecastDistributionModel):
    def get_calculation_budget_traffic(self, forecast_rule: ForecastRule) -> BudgetTraffic[BudgetTrafficRecord]:
        last_historical_month = forecast_rule.get_last_historical_month()

        traffic = self._budget_traffic_provider.get(
            budget_id=forecast_rule.budget_id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            traffic_months=[last_historical_month],
            traffic_direction=forecast_rule.traffic_direction,
            service_type=forecast_rule.service_type,
        )

        if traffic.empty:
            period = calculate_n_months_period(
                expected_period_end_date=last_historical_month - relativedelta(months=1),
                months_before_end_date=forecast_rule.get_distribution_moving_average_months(),
            )

            traffic = self._budget_traffic_provider.get(
                budget_id=forecast_rule.budget_id,
                home_operators=forecast_rule.home_operators,
                partner_operators=forecast_rule.partner.operators_ids,
                traffic_direction=forecast_rule.traffic_direction,
                service_type=forecast_rule.service_type,
                period=period,
            )

        return traffic
