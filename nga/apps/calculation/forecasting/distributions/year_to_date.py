from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.forecasts.domain import ForecastRule
from nga.core.types import DatePeriod

from .base import BaseLHMForecastDistributionModel


class YearToDateDistributionModel(BaseLHMForecastDistributionModel):
    """
    Volume for a month should be distributed between operators and call destinations based on the traffic from
    the beginning of a calendar year to the last historical month (average values).

    Logic:
        - Take the forecasted value (received after the application of a Monthly Forecast model) for a forecasted month.
        - Get traffic from the beginning of the year to the last historical month
        - Get distribution between operators, Call destination fields (Call destination, Called Country, Is Premium),
            and Segments in the traffic from the p.2
        - Distribute the value from the p.1 based on the distribution from the p.3.
    """

    def get_calculation_budget_traffic(self, forecast_rule: ForecastRule) -> BudgetTraffic[BudgetTrafficRecord]:
        last_historical_month = forecast_rule.get_last_historical_month()

        period = DatePeriod(last_historical_month.replace(month=1), last_historical_month)

        traffic = self._budget_traffic_provider.get(
            budget_id=forecast_rule.budget_id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            traffic_months=list(period),
            traffic_direction=forecast_rule.traffic_direction,
            service_type=forecast_rule.service_type,
        )

        return traffic
