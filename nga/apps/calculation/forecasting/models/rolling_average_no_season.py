import pandas as pd

from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.consts import CALCULATION_COLUMNS
from nga.apps.calculation.forecasting.dto import MonthlyForecastRecord
from nga.apps.calculation.forecasting.exceptions import MissingHistoricalTraffic
from nga.apps.calculation.forecasting.models import AbstractMonthlyForecastModel
from nga.apps.calculation.forecasting.models.utils import (
    Coefficients,
    calculate_n_months_period,
    filter_monthly_aggregation_records_by_period,
)
from nga.apps.calculation.forecasting.providers import AbstractBudgetTrafficProvider
from nga.apps.calculation.forecasting.structures import MonthlyTrafficContainer
from nga.apps.forecasts.domain import ForecastRule
from nga.apps.references.value_objects import MonthlyAggregationRecord
from nga.core.types import ONE_MONTH, Month


class RollingAverageNoSeasonMonthlyForecastModel(AbstractMonthlyForecastModel):
    def __init__(self, budget_traffic_provider: AbstractBudgetTrafficProvider) -> None:
        self._budget_traffic_provider = budget_traffic_provider

    def calculate(self, forecast_rule: ForecastRule) -> tuple[MonthlyForecastRecord, ...]:
        budget_traffic = self._get_budget_traffic(forecast_rule)

        if budget_traffic.empty is True:
            raise MissingHistoricalTraffic.from_forecast_rule_id(forecast_rule.id)

        monthly_records = MonthlyTrafficContainer.create_from_budget_traffic(budget_traffic)

        n_months = forecast_rule.get_forecast_model_moving_average_months()

        for forecast_month in forecast_rule.validated_period:
            coefficients = self._calculate_coefficients(forecast_month, monthly_records, n_months)

            forecast_record = MonthlyForecastRecord.create_from_traffic_values(coefficients, forecast_month)

            monthly_records[forecast_month] = forecast_record

        monthly_forecast_records = monthly_records.get_forecasted()

        return monthly_forecast_records

    def _get_budget_traffic(self, forecast_rule: ForecastRule) -> BudgetTraffic[MonthlyAggregationRecord]:
        """
        Loads budget traffic from storage based on forecast rule parameters and forecasted period, covering n-months
        period logic.
        """

        n_month_period = calculate_n_months_period(
            expected_period_end_date=Month.create_from_date(forecast_rule.validated_period.start_date - ONE_MONTH),
            months_before_end_date=forecast_rule.get_forecast_model_moving_average_months(),
        )

        traffic = self._budget_traffic_provider.get_monthly_aggregation(
            budget_id=forecast_rule.budget_id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            traffic_months=tuple(n_month_period),
            traffic_direction=forecast_rule.traffic_direction,
            service_type=forecast_rule.service_type,
        )

        return traffic

    @classmethod
    def _calculate_coefficients(
        cls,
        forecasted_month: Month,
        monthly_records: MonthlyTrafficContainer,
        n_months: int,
    ) -> Coefficients:

        n_month_period = calculate_n_months_period(
            expected_period_end_date=Month.create_from_date(forecasted_month - ONE_MONTH),
            months_before_end_date=n_months,
        )

        traffic_records = filter_monthly_aggregation_records_by_period(monthly_records.get_all(), n_month_period)

        records_df = pd.DataFrame(traffic_records)

        result_coef: pd.Series = records_df[CALCULATION_COLUMNS].apply(sum) / n_months

        coefficients = Coefficients(
            volume_actual=result_coef["volume_actual"],
            volume_billed=result_coef["volume_billed"],
            tap_charge_net=result_coef["tap_charge_net"],
            tap_charge_gross=result_coef["tap_charge_gross"],
            charge_net=result_coef["charge_net"],
            charge_gross=result_coef["charge_gross"],
        )

        return coefficients
