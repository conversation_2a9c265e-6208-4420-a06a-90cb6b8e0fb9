from typing import Iterable

from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.dto import MonthlyForecastRecord
from nga.apps.calculation.forecasting.dto_factories import (
    create_one_traffic_values,
    create_zero_traffic_values,
    create_zero_traffic_values_for,
)
from nga.apps.calculation.forecasting.exceptions import MissingHistoricalTraffic
from nga.apps.calculation.forecasting.models import AbstractMonthlyForecastModel
from nga.apps.calculation.forecasting.models.utils import (
    Coefficients,
    calculate_n_months_period,
    filter_monthly_aggregation_records_by_period,
)
from nga.apps.calculation.forecasting.providers import AbstractBudgetTrafficProvider
from nga.apps.calculation.forecasting.structures import MonthlyTrafficContainer
from nga.apps.calculation.utils import calculate_total_volume
from nga.apps.forecasts.domain import ForecastRule
from nga.apps.references.value_objects import MonthlyAggregationRecord, TrafficRecordValues
from nga.core.enums import VolumeTypeEnum
from nga.core.types import ONE_MONTH, ONE_YEAR_IN_MONTHS, DatePeriod, Month


class RollingAverageMonthlyForecastModel(AbstractMonthlyForecastModel):
    """
    For every forecasted month, this model uses the previous N-month traffic and compares each of them with the same
    month of the prior year. The average percentage between these months is then used as a factor and applied to the
    forecasted month (based on the same month from the prior year).

    Formula for every month (N=3):
        ([M-1_volume] / [M-13_volume] + [M-2_volume] / [M-14_volume] + [M-3_volume] / [M-15_volume]) / N * [M-12].

    Where: M-n = Forecasted month minus n months.

    Example (N = 3):

    Currently, the last Historical Month is 2024-04.

    In order to forecast volume for 2024-05, we need to take volume for 2024-04 and divide it by volume from 2023-04,
    then do the same for 2024-03 and 2024-02. Then we need to divide sum of these 3 relations by N (3).
    In result we will get a coefficient that needs to be multiplied by volume for 2023-05.

    In order to forecast volume for 2024-12, we need to take volume for 2024-11 and divide it by volume from 2023-11,
    then do the same for 2024-10 and 2024-09. Then we need to divide sum of these 3 relations by N (3).
    In result we will get a coefficient that needs to be multiplied by volume for 2023-12.

    Corner cases:
        1. All months are present from previous and current years. Usual division path

        2. All months are present from previous year and partly present from current. Extra zero traffic records are
            created to current year cover missing months from previous year

        3. Partly months are in previous year and all months in current year. Extra current year months are ignored

        4. Partly months are in previous year and partly (different) months in current year. Combination of 2 and 3
            cases non-matched records are ignored and missing records are created for current year
    """

    def __init__(self, budget_traffic_provider: AbstractBudgetTrafficProvider) -> None:
        self._budget_traffic_provider = budget_traffic_provider

    def calculate(self, forecast_rule: ForecastRule) -> tuple[MonthlyForecastRecord, ...]:
        budget_traffic = self._get_budget_traffic(forecast_rule)

        if budget_traffic.empty is True:
            raise MissingHistoricalTraffic.from_forecast_rule_id(forecast_rule.id)

        monthly_records = MonthlyTrafficContainer.create_from_budget_traffic(budget_traffic)

        n_months = forecast_rule.get_forecast_model_moving_average_months()

        for forecasted_month in forecast_rule.validated_period:
            previous_year_record = monthly_records.get(forecasted_month.previous_year_month)

            if previous_year_record is None:
                # if there is no record from previous year, then there is nothing to apply coefficients to
                continue

            coefficients = self._calculate_coefficients(forecasted_month, monthly_records, n_months)

            forecast_record = MonthlyForecastRecord.create_from_monthly_record(
                previous_year_record, traffic_month=forecasted_month.to_date()
            )

            forecast_record *= coefficients

            monthly_records[forecasted_month] = forecast_record

        monthly_forecast_records = monthly_records.get_forecasted()

        return monthly_forecast_records

    def _get_budget_traffic(self, forecast_rule: ForecastRule) -> BudgetTraffic[MonthlyAggregationRecord]:
        """
        Loads budget traffic from storage based on forecast rule parameters and forecasted period, covering n-months
        period logic.
        """

        historical_period_end_date = forecast_rule.validated_period.start_date - ONE_MONTH

        n_month_period = calculate_n_months_period(
            expected_period_end_date=Month.create_from_date(historical_period_end_date),
            months_before_end_date=forecast_rule.get_forecast_model_moving_average_months(),
        )

        traffic_period = DatePeriod(n_month_period.previous_year.start_date, historical_period_end_date)

        traffic = self._budget_traffic_provider.get_monthly_aggregation(
            budget_id=forecast_rule.budget_id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            traffic_months=tuple(traffic_period),
            traffic_direction=forecast_rule.traffic_direction,
            service_type=forecast_rule.service_type,
        )

        return traffic

    def _calculate_coefficients(
        self,
        forecasted_month: Month,
        monthly_records: MonthlyTrafficContainer,
        n_months: int,
    ) -> Coefficients:
        """
        Calculates coefficients for provided forecasted month based on traffic (historical and forecasted) from
        shifted period by n-month.
        To calculate coefficients we need to take n-month period before forecasted month, the same period from previous
        year, divide each month and calculate its average value (of each ratio by each month) based on volume_actual.
        """

        current_year_records, previous_year_records = self._get_traffic_for_coefficients_calculation(
            forecasted_month=forecasted_month,
            traffic_records=monthly_records.get_all(),
            n_months=n_months,
        )

        previous_year_is_empty = len(previous_year_records) == 0
        current_year_is_empty = len(current_year_records) == 0

        total_previous_year_volume = calculate_total_volume(previous_year_records, VolumeTypeEnum.ACTUAL)

        if (previous_year_is_empty or total_previous_year_volume == 0) and current_year_is_empty:
            return create_one_traffic_values()

        total_month_ratios = create_zero_traffic_values()

        for _current_year_record, _previous_year_record in zip(current_year_records, previous_year_records):
            total_month_ratios += _current_year_record / _previous_year_record

        # average value of last n-months ratios
        result_coefficients_values: TrafficRecordValues = total_month_ratios / len(previous_year_records)

        coefficients = Coefficients(
            volume_actual=result_coefficients_values.volume_actual,
            volume_billed=result_coefficients_values.volume_billed,
            tap_charge_net=result_coefficients_values.tap_charge_net,
            tap_charge_gross=result_coefficients_values.tap_charge_gross,
            charge_net=result_coefficients_values.charge_net,
            charge_gross=result_coefficients_values.charge_gross,
        )

        return coefficients

    @classmethod
    def _get_traffic_for_coefficients_calculation(
        cls,
        forecasted_month: Month,
        traffic_records: Iterable[MonthlyAggregationRecord],
        n_months: int,
    ) -> tuple[list[MonthlyAggregationRecord], list[MonthlyAggregationRecord]]:
        """
        Returns tuple of traffic record collections for current and previous years.

        Months that are present in previous year, and not present in current year will be created for current year with
        zero values.

        Months that are present in current year, and not present in previous year will be excluded from current year
        records.

        :param forecasted_month: Month that is used for calculating n-month period for getting budget traffic
        :param traffic_records: Traffic record collection that contains expected records
        :param n_months: number of months that is used to calculate "n-month" period
        :return: tuple of traffic record collections, where:
                * first collection is for "current year"
                * second collection is for "previous year"

                tuples must have an equal length and be ordered by month.
        """

        n_month_period = calculate_n_months_period(forecasted_month - ONE_MONTH, n_months)

        current_year_records = filter_monthly_aggregation_records_by_period(traffic_records, n_month_period)

        previous_year_records = filter_monthly_aggregation_records_by_period(
            traffic_records=traffic_records,
            period=n_month_period.previous_year,
        )

        # to calculate coefficients we need to take only non-zero volume records, other records must be ignored, and
        # from current year too
        previous_year_records = [r for r in previous_year_records if r.volume_actual > 0]

        previous_year_months = set(r.traffic_month + ONE_YEAR_IN_MONTHS for r in previous_year_records)
        current_year_months = set(r.traffic_month for r in current_year_records)

        missing_current_year_months = previous_year_months - current_year_months

        if missing_current_year_months:
            zero_records = tuple(
                create_zero_traffic_values_for(_traffic_month) for _traffic_month in missing_current_year_months
            )
            current_year_records.extend(zero_records)
            current_year_records.sort(key=lambda r: r.traffic_month)

        # if month from current year records is not present in previous year, then such record must be excluded from
        # calculation
        current_year_records = [r for r in current_year_records if r.traffic_month in previous_year_months]

        return current_year_records, previous_year_records
