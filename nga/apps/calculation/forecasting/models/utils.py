from typing import Iterable, TypeAlias

import pandas as pd
from dateutil.relativedelta import relativedelta

from nga.apps.calculation.forecasting.consts import DEFAULT_CHARGE
from nga.apps.calculation.forecasting.dto import MonthlyForecastRecord
from nga.apps.forecasts.domain import ForecastRule
from nga.apps.references.value_objects import MonthlyAggregationRecord, TrafficRecordValues
from nga.core.types import DatePeriod, Month

Coefficients: TypeAlias = TrafficRecordValues


def map_df_to_monthly_forecast_records(monthly_df: pd.DataFrame) -> tuple[MonthlyForecastRecord, ...]:
    """Maps monthly distributed forecasted values onto list[MonthlyDistributionRecord]."""

    monthly_forecast_records = tuple(
        MonthlyForecastRecord(
            traffic_month=r["forecasted_traffic_month"],
            volume_actual=r["forecasted_volume_actual"],
            volume_billed=r["forecasted_volume_billed"],
            charge_net=r["forecasted_charge_net"],
            charge_gross=r["forecasted_charge_gross"],
            tap_charge_net=r["forecasted_tap_charge_net"],
            tap_charge_gross=r["forecasted_tap_charge_gross"],
        )
        for r in monthly_df.to_dict("records")
    )

    return monthly_forecast_records


def create_budget_traffic_period_from_forecasted(forecasted_period: DatePeriod) -> DatePeriod:
    budget_traffic_period = forecasted_period.previous_year

    if budget_traffic_period.is_more_then_a_year:
        budget_traffic_period = DatePeriod.create_one_year(budget_traffic_period.start_date)

    return budget_traffic_period


def calculate_n_months_period(expected_period_end_date: Month, months_before_end_date: int) -> DatePeriod:
    """Returns period where start_date = end_date - number_of_months."""

    before_n_months_period_end_date = expected_period_end_date

    before_n_months_period_start_date = before_n_months_period_end_date - relativedelta(
        months=months_before_end_date - 1
    )

    n_months_period = DatePeriod(before_n_months_period_start_date, before_n_months_period_end_date)

    return n_months_period


def filter_monthly_aggregation_records_by_period(
    traffic_records: Iterable[MonthlyAggregationRecord],
    period: DatePeriod,
) -> list[MonthlyAggregationRecord]:
    """Filters records by provided period and sorts them by traffic_month."""

    _records = sorted(
        (r for r in traffic_records if Month.create_from_date(r.traffic_month) in period),
        key=lambda r: r.traffic_month,
    )

    return _records


def distribute_volume_equally_on_period(forecast_rule: ForecastRule) -> tuple[MonthlyForecastRecord, ...]:
    """
    Covers case when there is no historical traffic and forecast volume must be distributed between forecasted months.
    """

    forecasted_monthly_volume = forecast_rule.get_volume() / forecast_rule.validated_period.total_months

    monthly_forecast_records = tuple(
        MonthlyForecastRecord(
            traffic_month=traffic_month,
            volume_actual=forecasted_monthly_volume,
            volume_billed=forecasted_monthly_volume,
            charge_net=DEFAULT_CHARGE,
            charge_gross=DEFAULT_CHARGE,
            tap_charge_net=DEFAULT_CHARGE,
            tap_charge_gross=DEFAULT_CHARGE,
        )
        for traffic_month in forecast_rule.validated_period
    )

    return monthly_forecast_records
