from decimal import Decimal

import pandas as pd

from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.consts import CALCULATION_COLUMNS
from nga.apps.calculation.forecasting.dto import MonthlyForecastRecord
from nga.apps.calculation.forecasting.exceptions import MissingHistoricalTraffic
from nga.apps.calculation.forecasting.providers import AbstractBudgetTrafficProvider
from nga.apps.calculation.forecasting.structures import MonthlyTrafficContainer
from nga.apps.forecasts.domain import ForecastRule
from nga.apps.references.value_objects import MonthlyAggregationRecord
from nga.core.types import DatePeriod, Month
from nga.utils.calc import series_safely_divide

from .abstract import AbstractMonthlyForecastModel
from .utils import Coefficients, calculate_n_months_period, create_budget_traffic_period_from_forecasted


class MovingAverageMonthlyForecastModel(AbstractMonthlyForecastModel):
    """
    “N Months Moving Average” forecast model.
    This model uses the last N-month historical traffic and compares this with similar N-month traffic of the prior
    year. The percentage between these 2 periods is then used as a factor and applied to all forecasted months
    (based on the same month from the prior year).
    """

    def __init__(self, budget_traffic_provider: AbstractBudgetTrafficProvider) -> None:
        self._budget_traffic_provider = budget_traffic_provider

    def calculate(self, forecast_rule: ForecastRule) -> tuple[MonthlyForecastRecord, ...]:
        """Calculates monthly values based on last n historical months."""

        budget_traffic_period = create_budget_traffic_period_from_forecasted(forecast_rule.validated_period)

        budget_traffic = self.get_budget_traffic(forecast_rule, budget_traffic_period)

        if budget_traffic.empty is True:
            raise MissingHistoricalTraffic.from_forecast_rule_id(forecast_rule.id)

        coefficients = self.calculate_n_months_coefficients(forecast_rule)

        monthly_forecast_traffic_records = self.calculate_monthly_forecast_traffic_records(
            budget_traffic=budget_traffic,
            forecasted_period=forecast_rule.validated_period,
            coefficients=coefficients,
        )

        return monthly_forecast_traffic_records

    def calculate_n_months_coefficients(self, forecast_rule: ForecastRule) -> Coefficients:
        """
        Calculates percentage between last n-months and prior year n-months calculation values.

        percentage = n_months_values / prior_year_n_months_values
        """

        n_months_period = self.calculate_n_months_period(forecast_rule, forecast_rule.get_last_historical_month())

        n_months_traffic = self.get_budget_traffic(forecast_rule, n_months_period)
        n_months_traffic_df = pd.DataFrame(n_months_traffic.records, columns=CALCULATION_COLUMNS)
        n_months_traffic_totals_df = n_months_traffic_df.aggregate("sum")

        prior_year_n_months_traffic = self.get_budget_traffic(forecast_rule, n_months_period.previous_year)
        prior_year_n_months_traffic_df = pd.DataFrame(prior_year_n_months_traffic.records, columns=CALCULATION_COLUMNS)
        prior_year_n_months_traffic_totals_df = prior_year_n_months_traffic_df.aggregate("sum")

        if prior_year_n_months_traffic_totals_df["volume_actual"] == 0:
            result = pd.Series(index=CALCULATION_COLUMNS, dtype="object")
            result[CALCULATION_COLUMNS] = Decimal("1")
        else:
            # if there is no records pandas will set float(0) and it will result to division error
            n_months_traffic_totals_df.loc[lambda x: x == 0] = Decimal("0")

            result = series_safely_divide(n_months_traffic_totals_df, prior_year_n_months_traffic_totals_df)

        coefficients = Coefficients(
            volume_actual=result["volume_actual"],
            volume_billed=result["volume_billed"],
            tap_charge_net=result["tap_charge_net"],
            tap_charge_gross=result["tap_charge_gross"],
            charge_net=result["charge_net"],
            charge_gross=result["charge_gross"],
        )

        return coefficients

    @classmethod
    def calculate_monthly_forecast_traffic_records(
        cls,
        budget_traffic: BudgetTraffic[MonthlyAggregationRecord],
        forecasted_period: DatePeriod,
        coefficients: Coefficients,
    ) -> tuple[MonthlyForecastRecord, ...]:
        """Performs calculation of monthly forecast traffic records by applying N-Months coefficients."""

        monthly_records = MonthlyTrafficContainer.create_from_budget_traffic(budget_traffic)

        for forecasted_month in forecasted_period:
            prior_year_record = monthly_records.get(forecasted_month.previous_year_month)

            if prior_year_record is None:
                continue

            forecast_record = MonthlyForecastRecord.create_from_monthly_record(
                prior_year_record, traffic_month=forecasted_month
            )

            forecast_record *= coefficients

            monthly_records[forecasted_month] = forecast_record

        monthly_forecast_records = monthly_records.get_forecasted()

        return monthly_forecast_records

    @classmethod
    def calculate_n_months_period(cls, forecast_rule: ForecastRule, last_historical_month: Month) -> DatePeriod:
        """Returns period for getting n-months traffic."""

        return calculate_n_months_period(
            expected_period_end_date=last_historical_month,
            months_before_end_date=forecast_rule.get_forecast_model_moving_average_months(),
        )

    def get_budget_traffic(
        self,
        forecast_rule: ForecastRule,
        period: DatePeriod,
    ) -> BudgetTraffic[MonthlyAggregationRecord]:

        traffic = self._budget_traffic_provider.get_monthly_aggregation(
            budget_id=forecast_rule.budget_id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            traffic_months=list(period),
            traffic_direction=forecast_rule.traffic_direction,
            service_type=forecast_rule.service_type,
        )

        return traffic
