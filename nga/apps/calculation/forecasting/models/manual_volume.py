from copy import copy
from decimal import Decimal
from typing import Optional, cast

from nga.apps.budgets.domain.dto import BudgetTrafficParameters
from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.dto import MonthlyForecastRecord
from nga.apps.calculation.forecasting.dto_factories import create_zero_traffic_values, create_zero_traffic_values_for
from nga.apps.calculation.forecasting.exceptions import HistoricalVolumeExceedsForecasted
from nga.apps.calculation.forecasting.providers import AbstractBudgetAggregationProvider
from nga.apps.forecasts.domain import ForecastRule
from nga.apps.references.value_objects import MonthlyAggregationRecord, TrafficRecordValues
from nga.core.types import MONTHS_IN_A_YEAR, DatePeriod, Month

from .abstract import AbstractMonthlyForecastModel
from .utils import create_budget_traffic_period_from_forecasted, distribute_volume_equally_on_period


class ManualVolumeMonthlyForecastModel(AbstractMonthlyForecastModel):
    """
    Forecast Rule model that performs monthly values distribution basing on previous year traffic.
    If rule is for 2023-01 - 2023-04, it will calculate values basing on 2022-01 - 2022-04 historical period.
    If historical period does not exist, forecasted volume is distributed equally by number of forecasted months.
    """

    def __init__(self, budget_traffic_provider: "ManualVolumeBudgetTrafficProvider") -> None:
        self._budget_traffic_provider = budget_traffic_provider

    def calculate(self, forecast_rule: ForecastRule) -> tuple[MonthlyForecastRecord, ...]:
        """Performs calculation of monthly distribution."""

        if self._can_be_calculated_without_traffic(forecast_rule):
            return self._calculate_without_traffic(forecast_rule)

        if forecast_rule.last_historical_month is None:
            return self._calculate_without_last_historical_month(forecast_rule)

        last_historical_month: Month = forecast_rule.last_historical_month

        budget_traffic = self._budget_traffic_provider.load_full_traffic_till_forecast_rule(forecast_rule)

        traffic_for_calculation = self._budget_traffic_provider.extract_traffic_for_calculation(
            forecast_rule=forecast_rule,
            budget_traffic=budget_traffic,
        )

        forecast_rule = self.update_forecast_rule(forecast_rule, traffic_for_calculation)

        if traffic_for_calculation.no_volume_for_calculation:
            monthly_forecast_records = self._calculate_without_traffic(forecast_rule)

        else:
            if last_historical_month in forecast_rule.period:
                new_forecasted_volume = self._extract_historical_volume_from_forecasted_volume(
                    forecast_rule=forecast_rule,
                    budget_traffic=budget_traffic,
                )
                forecast_rule.set_volume(new_forecasted_volume)

            monthly_forecast_records = self._calculate_with_traffic(forecast_rule, traffic_for_calculation)

        lhm_traffic_record = self._budget_traffic_provider.extract_lhm_traffic_record(
            budget_traffic=budget_traffic,
            last_historical_month=last_historical_month,
        )

        if lhm_traffic_record is None:
            lhm_traffic_record = create_zero_traffic_values_for(last_historical_month)

        monthly_forecast_records = self._calculate_charges(monthly_forecast_records, lhm_traffic_record)

        return monthly_forecast_records

    def _can_be_calculated_without_traffic(self, forecast_rule: ForecastRule) -> bool:
        return forecast_rule.get_volume() == 0

    def _calculate_without_last_historical_month(
        self,
        forecast_rule: ForecastRule,
    ) -> tuple[MonthlyForecastRecord, ...]:
        return self._calculate_without_traffic(forecast_rule)

    def update_forecast_rule(
        self,
        forecast_rule: ForecastRule,
        budget_traffic: BudgetTraffic[MonthlyAggregationRecord],
    ) -> ForecastRule:
        return forecast_rule

    @classmethod
    def _calculate_without_traffic(cls, forecast_rule: ForecastRule) -> tuple[MonthlyForecastRecord, ...]:
        """
        Calculates forecasted monthly distribution without historical traffic.
        When there is no historical traffic, by using MANUAL_VOLUME model we distribute forecasted volume equally
        between forecasted months. For volume_billed use forecasted monthly volume. For charges - default (0).

        :param forecast_rule: ForecastRule
        :return: list[MonthlyDistributionRecord]
        """

        monthly_forecast_records = distribute_volume_equally_on_period(forecast_rule)

        return monthly_forecast_records

    def _calculate_with_traffic(
        self,
        forecast_rule: ForecastRule,
        traffic: BudgetTraffic[MonthlyAggregationRecord],
    ) -> tuple[MonthlyForecastRecord, ...]:
        """Calculate forecast records based on historical budget traffic."""

        total_forecast_volume = forecast_rule.get_volume()

        forecast_period = forecast_rule.validated_period

        if forecast_period.total_months > MONTHS_IN_A_YEAR:
            historical_traffic_map = self._roll_historical_traffic_if_period_is_more_then_a_year(forecast_rule, traffic)
        else:
            historical_traffic_map = {
                Month.create_from_date(forecast_period.get_date_by_month(r.traffic_month)): r for r in traffic.records
            }

        total_historical_values = create_zero_traffic_values()

        for record in historical_traffic_map.values():
            total_historical_values += record

        forecast_records = []

        total_forecast_values: TrafficRecordValues = total_historical_values * (
            total_forecast_volume / total_historical_values.volume_actual
        )

        for forecast_month in forecast_period:
            historical_record = historical_traffic_map.get(forecast_month)

            if historical_record is None:
                continue

            historical_shares: MonthlyAggregationRecord = historical_record / total_historical_values

            result: MonthlyAggregationRecord = total_forecast_values * historical_shares

            forecast_record = MonthlyForecastRecord.create_from_traffic_values(result, forecast_month)

            forecast_records.append(cast(MonthlyForecastRecord, forecast_record))

        return tuple(forecast_records)

    @classmethod
    def _calculate_charges(
        cls,
        monthly_forecast_records: tuple[MonthlyForecastRecord, ...],
        lhm_traffic_record: MonthlyAggregationRecord,
    ) -> tuple[MonthlyForecastRecord, ...]:
        """Calculates charges for forecast records based on rate from traffic for last historical month."""

        lhm_rate: MonthlyAggregationRecord = lhm_traffic_record / lhm_traffic_record.volume_actual

        for forecast_record in monthly_forecast_records:
            forecast_record.tap_charge_net = forecast_record.volume_actual * lhm_rate.tap_charge_net
            forecast_record.tap_charge_gross = forecast_record.volume_actual * lhm_rate.tap_charge_gross
            forecast_record.charge_net = forecast_record.volume_actual * lhm_rate.charge_net
            forecast_record.charge_gross = forecast_record.volume_actual * lhm_rate.charge_gross

        return monthly_forecast_records

    @classmethod
    def _roll_historical_traffic_if_period_is_more_then_a_year(
        cls,
        forecast_rule: ForecastRule,
        traffic: BudgetTraffic[MonthlyAggregationRecord],
    ) -> dict[Month, MonthlyAggregationRecord]:
        """
        If forecast period is more than 12 months, period is cut to 12 months period and traffic is rolled to cover
        full forecast period.

        Example:
            Forecast rule period 2024-01 -- 2025-03.
            Historical records:
                2024-01 2024-02 2024-03 2024-04 2024-05
            Historical records used for calculation:
                2024-01 2024-02 2024-03 2024-04 2024-05
                | 2024-01 2024-02 2024-03 2024-04 2024-05 (rolling applied 1st time)
                | 2024-01 2024-02 2024-03 2024-04 2024-05 (rolling applied 2nd time)
        """

        traffic_map = {}

        total_historical_records = len(traffic.records)

        idx = 0

        for forecast_month in forecast_rule.validated_period:
            if idx == total_historical_records:
                idx = 0

            historical_record = copy(traffic.records[idx])

            traffic_map[forecast_month] = historical_record

            idx += 1

        return traffic_map

    @classmethod
    def _extract_historical_volume_from_forecasted_volume(
        cls,
        forecast_rule: ForecastRule,
        budget_traffic: BudgetTraffic[MonthlyAggregationRecord],
    ) -> Decimal:
        """
        Covers case when there is historical traffic in a forecasted period. In that case we need to extract historical
        volume from forecasted volume, to make total volume for forecasted period equal to expected volume.
        """

        last_historical_month = forecast_rule.get_last_historical_month()

        historical_period_in_rule = DatePeriod(forecast_rule.period.start_date, last_historical_month)

        historical_traffic_in_rule = BudgetTraffic[MonthlyAggregationRecord](
            records=tuple(
                r
                for r in budget_traffic.records
                if Month.create_from_date(r.traffic_month) in historical_period_in_rule
            )
        )

        if historical_traffic_in_rule.volume_actual > forecast_rule.get_volume():
            raise HistoricalVolumeExceedsForecasted.from_forecast_rule_id(forecast_rule.id)

        forecasted_volume = forecast_rule.get_volume() - historical_traffic_in_rule.volume_actual

        return forecasted_volume


class ManualVolumeBudgetTrafficProvider:
    def __init__(
        self,
        budget_aggregation_provider: AbstractBudgetAggregationProvider,
    ) -> None:
        self._budget_aggregation_provider = budget_aggregation_provider

    def load_full_traffic_till_forecast_rule(
        self,
        forecast_rule: ForecastRule,
    ) -> BudgetTraffic[MonthlyAggregationRecord]:
        """Loads budget traffic monthly aggregations for the full budget period."""

        budget = forecast_rule.get_budget()

        parameters = BudgetTrafficParameters(
            snapshot_id=budget.calculation_snapshot_id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            period=DatePeriod(budget.period.start_date, forecast_rule.period.end_date),
            traffic_directions=(forecast_rule.traffic_direction,),
            service_types=(forecast_rule.service_type,),
        )

        records = self._budget_aggregation_provider.get_monthly_records(parameters)

        budget_traffic = BudgetTraffic[MonthlyAggregationRecord](records=tuple(records))

        return budget_traffic

    def extract_traffic_for_calculation(
        self,
        forecast_rule: ForecastRule,
        budget_traffic: BudgetTraffic[MonthlyAggregationRecord],
    ) -> BudgetTraffic[MonthlyAggregationRecord]:
        """Returns budget traffic based on the forecast rule traffic parameters."""

        search_period = create_budget_traffic_period_from_forecasted(forecast_rule.validated_period)

        budget = forecast_rule.get_budget()

        if search_period.total_months > MONTHS_IN_A_YEAR:
            search_period = DatePeriod.create_one_year(search_period.start_date)

        last_records: tuple[MonthlyAggregationRecord, ...] = tuple()

        while budget.period.start_date <= search_period.start_date:
            last_records = tuple(
                r for r in budget_traffic.records if Month.create_from_date(r.traffic_month) in search_period
            )

            if last_records:
                break

            search_period = search_period.previous_year

        budget_traffic = BudgetTraffic[MonthlyAggregationRecord](records=last_records)

        return budget_traffic

    def extract_lhm_traffic_record(
        self,
        budget_traffic: BudgetTraffic[MonthlyAggregationRecord],
        last_historical_month: Month,
    ) -> Optional[MonthlyAggregationRecord]:
        lhm_traffic_record = next(
            (r for r in budget_traffic.records if Month.create_from_date(r.traffic_month) == last_historical_month),
            None,
        )

        return lhm_traffic_record
