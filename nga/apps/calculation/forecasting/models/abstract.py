from abc import ABC, abstractmethod

from nga.apps.calculation.forecasting.dto import MonthlyForecastRecord
from nga.apps.forecasts.domain import ForecastRule


class AbstractMonthlyForecastModel(ABC):
    """Describes forecast monthly model interface. Calculates volumes and charges on month level."""

    @abstractmethod
    def calculate(self, forecast_rule: ForecastRule) -> tuple[MonthlyForecastRecord, ...]:
        """Performs monthly forecast calculation."""
