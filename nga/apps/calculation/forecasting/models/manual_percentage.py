from decimal import Decimal

from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.dto import MonthlyForecastRecord
from nga.apps.calculation.forecasting.exceptions import MissingHistoricalTraffic
from nga.apps.forecasts.domain import ForecastRule
from nga.apps.references.value_objects import MonthlyAggregationRecord

from .manual_volume import ManualVolumeMonthlyForecastModel


class ManualPercentageMonthlyForecastModel(ManualVolumeMonthlyForecastModel):
    """
    Works the same as MANUAL_VOLUME monthly model, but as a forecasted volume uses share from historical volume
    that is defined in Forecast Rule `volume_percentage`.
    """

    def update_forecast_rule(
        self,
        forecast_rule: ForecastRule,
        budget_traffic: BudgetTraffic[MonthlyAggregationRecord],
    ) -> ForecastRule:
        forecasted_volume = self.calculate_forecasted_volume(budget_traffic, forecast_rule.get_volume_percentage())

        forecast_rule.set_volume(forecasted_volume)

        return forecast_rule

    @classmethod
    def calculate_forecasted_volume(
        cls,
        budget_traffic: BudgetTraffic[MonthlyAggregationRecord],
        percentage: Decimal,
    ) -> Decimal:
        """Calculates total forecasted volume based on share from total historical volume."""

        volume_actual = budget_traffic.volume_actual

        forecasted_volume = volume_actual * (percentage / Decimal("100")) + volume_actual

        return forecasted_volume

    def _calculate_without_last_historical_month(
        self,
        forecast_rule: ForecastRule,
    ) -> tuple[MonthlyForecastRecord, ...]:
        # records must not be created because volume is evaluated based on historical traffic that does not exist.
        raise MissingHistoricalTraffic.from_forecast_rule_id(forecast_rule.id)

    def _can_be_calculated_without_traffic(self, forecast_rule: ForecastRule) -> bool:
        return False
