from datetime import date
from decimal import Decimal
from typing import Optional

import pandas as pd

from nga.apps.calculation.forecasting.budget_traffic import BudgetTraffic
from nga.apps.calculation.forecasting.consts import CALCULATION_COLUMNS
from nga.apps.calculation.forecasting.dto import MonthlyForecastRecord
from nga.apps.calculation.forecasting.exceptions import MissingHistoricalTraffic
from nga.apps.calculation.forecasting.providers import AbstractBudgetTrafficProvider
from nga.apps.calculation.forecasting.structures import MonthlyTrafficContainer
from nga.apps.forecasts.domain import ForecastRule
from nga.apps.references.value_objects import MonthlyAggregationRecord
from nga.core.types import DatePeriod, Month
from nga.utils.calc import series_safely_divide

from .abstract import AbstractMonthlyForecastModel
from .utils import Coefficients


class PPIMonthlyForecastModel(AbstractMonthlyForecastModel):
    """
    "Previous Period Incremental" monthly forecast model. This model uses the historical traffic for the calendar year
    and compares this with the same period from the prior year. The percentage between these 2 periods is then used
    as a factor applied.

    Formula (for every month): (
        [calendar_year_historical_volume] / [calendar_year_historical_volume_in_prior_year]
    ) * [the_month_volume_in_prior_year]

    Example:

    Currently, the last Historical Month is 2023-06.
    In order to forecast volume for 2023-07, we need to take total volume for [2023-01 - 2023-06] and divide it by
    total volume for [2022-01 - 2022-06]. In result, we will get a coefficient that needs to be multiplied by volume
    for 2022-07.
    """

    def __init__(self, budget_traffic_provider: AbstractBudgetTrafficProvider) -> None:
        self._budget_traffic_provider = budget_traffic_provider

    def calculate(self, forecast_rule: ForecastRule) -> tuple[MonthlyForecastRecord, ...]:
        """Calculates monthly forecast records by applying PPI model."""

        budget_traffic = self.get_budget_traffic(forecast_rule)

        if budget_traffic.empty:
            raise MissingHistoricalTraffic.from_forecast_rule_id(forecast_rule.id)

        coefficients = self.calculate_coefficients(forecast_rule)

        monthly_records = MonthlyTrafficContainer.create_from_budget_traffic(budget_traffic)

        for forecast_month in forecast_rule.validated_period:
            prior_year_record = monthly_records.get(forecast_month.previous_year_month)

            if prior_year_record is None:
                continue

            forecast_record = MonthlyForecastRecord.create_from_monthly_record(
                prior_year_record, traffic_month=forecast_month
            )

            forecast_record *= coefficients

            monthly_records[forecast_month] = forecast_record

        monthly_forecast_records = monthly_records.get_forecasted()

        return monthly_forecast_records

    @classmethod
    def create_ppi_period(cls, last_historical_month: Month) -> DatePeriod:
        """
        Period from the begging of last historical month year.

        if last_historical_month is 2023-06-01, period will be [2023-01-01, 2023-06-0].
        """

        return DatePeriod(date(last_historical_month.year, month=1, day=1), last_historical_month)

    def calculate_coefficients(self, forecast_rule: ForecastRule) -> Coefficients:
        """
        Calculates coefficients for PPI model. Coefficients are calculated by dividing historical traffic total values,
        onto its prior year total values.
        """

        last_historical_month = forecast_rule.get_last_historical_month()

        ppi_period = self.create_ppi_period(last_historical_month)
        ppi_traffic = self.get_budget_traffic(forecast_rule, ppi_period)

        if ppi_traffic.empty is True:
            raise MissingHistoricalTraffic.from_forecast_rule_id(forecast_rule.id)

        ppi_traffic_df = pd.DataFrame(ppi_traffic.records, columns=CALCULATION_COLUMNS)
        ppi_monthly_df = ppi_traffic_df.aggregate("sum")

        prev_last_historical_month = last_historical_month.previous_year_month
        prev_ppi_period = DatePeriod(
            date(prev_last_historical_month.year, month=1, day=1),
            prev_last_historical_month,
        )
        prev_ppi_traffic = self.get_budget_traffic(forecast_rule, prev_ppi_period)
        prev_ppi_traffic_df = pd.DataFrame(prev_ppi_traffic.records, columns=CALCULATION_COLUMNS)
        prev_ppi_monthly_df = prev_ppi_traffic_df.aggregate("sum")

        if prev_ppi_monthly_df["volume_actual"] == 0:
            coefficients_result = pd.Series(index=CALCULATION_COLUMNS, dtype="object")
            coefficients_result[CALCULATION_COLUMNS] = Decimal("1")
        else:
            coefficients_result = series_safely_divide(ppi_monthly_df, prev_ppi_monthly_df)

        coefficients = Coefficients(
            volume_actual=coefficients_result["volume_actual"],
            volume_billed=coefficients_result["volume_billed"],
            tap_charge_net=coefficients_result["tap_charge_net"],
            tap_charge_gross=coefficients_result["tap_charge_gross"],
            charge_net=coefficients_result["charge_net"],
            charge_gross=coefficients_result["charge_gross"],
        )

        return coefficients

    def get_budget_traffic(
        self,
        forecast_rule: ForecastRule,
        period: Optional[DatePeriod] = None,
    ) -> BudgetTraffic[MonthlyAggregationRecord]:
        if period is None:
            period = forecast_rule.validated_period.previous_year

        traffic = self._budget_traffic_provider.get_monthly_aggregation(
            budget_id=forecast_rule.budget_id,
            home_operators=forecast_rule.home_operators,
            partner_operators=forecast_rule.partner.operators_ids,
            traffic_months=list(period),
            traffic_direction=forecast_rule.traffic_direction,
            service_type=forecast_rule.service_type,
        )

        return traffic
