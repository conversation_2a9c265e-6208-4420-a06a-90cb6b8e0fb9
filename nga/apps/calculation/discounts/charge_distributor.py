from copy import copy
from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.enums import DiscountModelTypeEnum, TaxTypeEnum
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.charge_tax_calculator import ChargeTaxCalculator
from nga.apps.calculation.utils import (
    calculate_total_traffic_value_by_field,
    get_tap_charge_field_by_tax_type,
    get_volume_field_by_type,
)


class DiscountChargeDistributor:
    """
    Distributes provided charge value between traffic records. Distribution is based on field that evaluates based
    on Discount setup. It either volumes or charges. If traffic total value (volume or charge) is zero, discount
    charge is distributed evenly between records, otherwise proportionally to provided values.

    Tax calculation is applied to charge values during proportional distribution.
    """

    @classmethod
    def distribute_on_traffic(
        cls,
        charge: Decimal,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:
        """Distributes provided charge value between traffic records."""

        total_traffic_value = cls._calculate_total_value(discount, traffic_records)

        if total_traffic_value == 0:
            distributed_records = cls._distribute_evenly(charge, traffic_records)

        else:
            distributed_records = cls._distribute_proportionally(
                discount=discount,
                discount_charge=charge,
                traffic_records=traffic_records,
                total_traffic_value=total_traffic_value,
            )

        return distributed_records

    @classmethod
    def _calculate_total_value(cls, discount: Discount, traffic_records: Sequence[BudgetTrafficRecord]) -> Decimal:

        traffic_value_field = get_traffic_value_field(discount)

        return calculate_total_traffic_value_by_field(traffic_value_field, traffic_records)

    @classmethod
    def _distribute_evenly(
        cls,
        discount_charge: Decimal,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:

        charge_per_record = discount_charge / len(traffic_records)

        discounted_traffic_records = []

        for record in traffic_records:
            discounted_record = copy(record)

            discounted_record.charge_net = charge_per_record
            discounted_record.charge_gross = charge_per_record

            discounted_traffic_records.append(discounted_record)

        return discounted_traffic_records

    @classmethod
    def _distribute_proportionally(
        cls,
        discount: Discount,
        discount_charge: Decimal,
        traffic_records: Sequence[BudgetTrafficRecord],
        total_traffic_value: Decimal,
    ) -> list[BudgetTrafficRecord]:
        discounted_traffic = []

        traffic_value_field = get_traffic_value_field(discount)

        for traffic_record in traffic_records:
            traffic_record_value = getattr(traffic_record, traffic_value_field)

            traffic_record_discount_charge = discount_charge * (traffic_record_value / total_traffic_value)

            calculated_traffic_record = cls.apply_discount_charge(
                traffic_record=traffic_record,
                traffic_record_discount_charge=traffic_record_discount_charge,
                tax_type=discount.tax_type,
            )

            discounted_traffic.append(calculated_traffic_record)

        return discounted_traffic

    @classmethod
    def apply_discount_charge(
        cls,
        traffic_record: BudgetTrafficRecord,
        traffic_record_discount_charge: Decimal,
        tax_type: TaxTypeEnum,
    ) -> BudgetTrafficRecord:
        """Calculates charges for budget traffic record based on tax type and discount calculated charge."""

        _traffic_record = copy(traffic_record)

        tap_charge_net, tap_charge_gross = _traffic_record.tap_charge_net, _traffic_record.tap_charge_gross

        if tax_type == TaxTypeEnum.NET:
            charge_net = traffic_record_discount_charge
            charge_gross = ChargeTaxCalculator.add_tax(charge_net, tap_charge_net, tap_charge_gross)

        else:
            # TaxTypeEnum.GROSS
            charge_gross = traffic_record_discount_charge
            charge_net = ChargeTaxCalculator.subtract_tax(charge_gross, tap_charge_net, tap_charge_gross)

        _traffic_record.charge_net = charge_net
        _traffic_record.charge_gross = charge_gross

        return _traffic_record


def get_traffic_value_field(discount: Discount) -> str:
    """
    Returns field that is used for obtaining appropriate value from traffic record.
    It can be either volume field or charge. Evaluation of field is based on discount model.
    """

    if discount.model_type == DiscountModelTypeEnum.ALL_YOU_CAN_EAT:
        field = get_tap_charge_field_by_tax_type(discount.tax_type)
    else:
        field = get_volume_field_by_type(discount.volume_type)

    return field
