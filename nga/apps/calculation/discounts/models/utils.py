from collections import defaultdict
from datetime import date
from decimal import Decimal
from typing import Optional, Sequence, cast

from nga.apps.budgets.domain.models import BudgetTrafficRecord


def c(value: Optional[Decimal]) -> Decimal:
    """Casts optional decimal to non-nullable decimal."""

    return cast(Decimal, value)


def group_traffic_records_by_month(
    traffic_records: Sequence[BudgetTrafficRecord],
) -> dict[date, list[BudgetTrafficRecord]]:

    traffic_records_per_month = defaultdict(list)

    for r in traffic_records:
        traffic_records_per_month[r.traffic_month].append(r)

    return traffic_records_per_month
