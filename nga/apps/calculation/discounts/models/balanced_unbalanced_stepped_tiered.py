from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.domain.specifications.discount_setup import (
    BalancedUnbalancedSteppedTieredDiscountSetupSpecification,
)
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.charge_distributor import DiscountChargeDistributor
from nga.apps.calculation.discounts.models.abstract import AbstractBalancingDiscountModel
from nga.apps.calculation.discounts.models.formulas import calculate_stepped_tiered_charges


class BalancedUnbalancedSteppedTieredDiscountModel(AbstractBalancingDiscountModel):
    """
    Name: Balanced Unbalanced (Tier)
    Setup:
        * 1st discount parameter with the “Single Rate Effective” calculation type and “Balanced” Balance Type
        * 2nd and following discount parameters with the “Stepped/Tiered” calculation type and “Unbalanced” Balance Type
        * 2nd discount parameter should have the same Lower Bound value = 0
        * Traffic Direction is “Bi-Directional”

    Description:
        The model is applied based on volume. We need to take the volume amount by discount traffic, split it by
        balanced and unbalanced, apply a specific rate to balanced and unbalanced volume, and get the charge value
        in the result. Unbalanced traffic can be split into tiers with different rates in this case.

    Formula:
        1. Define Balanced volume: If ([TV Inbound] < [TV Outbound]) then [TV Inbound] else [TV Outbound]

        2. Define Unbalanced volume (per direction): If ([TV] > [Bal V]) then ([TV] - [Bal V]) else 0

        3. Define Unbalanced Tn volume
            if ([Unbal V] < [Unbal Tn UB])
                then ([Unbal V] - [Unbal Tn LB]) and go to Step 5
            else ([Unbal Tn UB] - [Unbal Tn LB])

        4. Define Unbalanced Tlast volume: [Unbal V] - [Unbal Tlast LB]

        5. Calculate charge: [Bal V] * [Bal DBV] + [Unbal Tn V] * [Unbal Tn DBV] + [Unbal Tlast V] * [Unbal Tlast DBV]

    Where:
        TV - Total volume by Discount traffic
        Bal V - Balanced volume by Discount traffic
        Unbal V - Unbalanced volume by Discount traffic
        Unbal Tn UB - Unbalanced Tier n Upper Bound
        Unbal Tn LB - Unbalanced Tier n Lower Bound
        Unbal Tlast LB - Unbalanced last Tier Lower Bound
        Bal DBV - Balanced Discount Basis Value
        Unbal Tn DBV - Unbalanced Tier n Discount Basis Value
        Unbal Tlast DBV - Unbalanced last Tier Discount Basis Value
    """

    def apply_discount(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:
        """Applies discount to provided traffic."""

        discount_charge = self.calculate_charge(discount, traffic_records)

        # The model is applied per direction, but to calculate its charge it requires traffic for both directions
        direction_traffic_records = [r for r in traffic_records if r.traffic_direction == discount.traffic_direction]

        discounted_traffic_records = DiscountChargeDistributor.distribute_on_traffic(
            discount=discount,
            charge=discount_charge,
            traffic_records=direction_traffic_records,
        )

        return discounted_traffic_records

    def calculate_charge(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:
        spec = BalancedUnbalancedSteppedTieredDiscountSetupSpecification()

        balanced_volume = self.calculate_balanced_volume(discount, traffic_records)

        direction_traffic_records = [r for r in traffic_records if r.traffic_direction == discount.traffic_direction]

        unbalanced_tiers = spec.get_tiers(discount)
        unbalanced_volume = self.calculate_unbalanced_volume(discount, balanced_volume, direction_traffic_records)
        unbalanced_tiers_charge = self.calculate_unbalanced_tiers_charge(unbalanced_tiers, unbalanced_volume)

        sre_balanced_basis_value = spec.get_balanced_basis_value(discount)

        discount_charge = balanced_volume * sre_balanced_basis_value + unbalanced_tiers_charge

        return discount_charge

    @classmethod
    def calculate_unbalanced_tiers_charge(cls, tiers: list[DiscountParameter], unbalanced_volume: Decimal) -> Decimal:
        tier_charges = calculate_stepped_tiered_charges(tiers, unbalanced_volume)

        discount_charge = Decimal(sum(tier_charges))

        return discount_charge
