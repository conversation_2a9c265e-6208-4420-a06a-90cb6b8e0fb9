from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import (
    PMPIWithIncrementalChargingDiscountSetupSpecification,
)
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.charge_distributor import DiscountChargeDistributor
from nga.apps.calculation.discounts.models.abstract import AbstractDiscountModel
from nga.apps.calculation.utils import calculate_total_volume
from nga.core.enums import ServiceTypeEnum


class PMPIWithIncrementalChargingDiscountModel(AbstractDiscountModel):
    """
    Description:

    The model is applied based on volume for the specific service type "Access Fee" and
    IMSI count type “Data”.

    Amount of Total Volume included in Access Fee is calculated in a way to summarize Unique Count of IMSI per each
    of the Months, and multiply it with Data MB Traffic Volume included into Access Fee (defined under Lower Bound).

    In order for Incremental Charging to be performed, it has to first check if Total Data MB Traffic Volume included
    into Access Fee was or will be achieved.

    If Total Data MB Traffic Volume (actual/forecasted) is higher than Total Data MB Traffic Volume which is included
    into Access Fee, then Incremental Rate is applied for each Data MB above that threshold.

    Both Access Fee charge for IMSI count and Incremental Charging for Data MB Traffic Volume are summarized together
    in order to get Total Discounted Charge for Data MB overall.
    """

    _spec_cls = PMPIWithIncrementalChargingDiscountSetupSpecification

    def apply_discount(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:

        # access fee charge calculation

        access_fee_traffic = tuple(r for r in traffic_records if r.service_type == ServiceTypeEnum.ACCESS_FEE)

        total_access_fee_volume = calculate_total_volume(access_fee_traffic, discount.volume_type)

        access_fee_charge = total_access_fee_volume * self._spec_cls.get_access_fee_rate(discount)

        # data charge calculation

        data_traffic = tuple(r for r in traffic_records if r.service_type == ServiceTypeEnum.DATA)

        total_data_volume = calculate_total_volume(data_traffic, discount.volume_type)

        data_volume_in_access_fee = total_access_fee_volume * self._spec_cls.get_lower_bound(discount)

        data_charge = Decimal("0")

        if total_data_volume > data_volume_in_access_fee:
            incremental_rate = self._spec_cls.get_incremental_rate(discount)

            data_charge = (total_data_volume - data_volume_in_access_fee) * incremental_rate

        # discount charges distributions

        discounted_traffic: list[BudgetTrafficRecord] = []

        if access_fee_charge > 0:
            _access_fee_traffic = DiscountChargeDistributor.distribute_on_traffic(
                access_fee_charge, discount, access_fee_traffic
            )
            discounted_traffic.extend(_access_fee_traffic)

        if data_charge > 0:
            _data_traffic = DiscountChargeDistributor.distribute_on_traffic(data_charge, discount, data_traffic)
            discounted_traffic.extend(_data_traffic)

        return discounted_traffic
