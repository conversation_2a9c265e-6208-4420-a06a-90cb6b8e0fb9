from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.models.abstract import AbstractScalarDiscountModel
from nga.apps.calculation.discounts.models.utils import c
from nga.apps.calculation.utils import calculate_total_volume


class BackToFirstDiscountModel(AbstractScalarDiscountModel):
    """
    Name:  Back to First
    Setup:
        * 1 or more discount parameters with the “Back to First” calculation type and the Lower Bound value entered
            (the Upper Bound value is empty).

    Description:
        The model is applied based on volume. We need to take the volume amount by discount traffic and apply a
        specific rate (based on a tier it falls to) to the whole volume to get the charge value in the result.

    Formula:
        1. Define Tier n volume: If ([Tn LB] < [TV by DT]) then ([TV by DT]) and go to Step 3 else 0

        2. Define Last Tier volume: [TV by DT] - [TLast LB]

        3. Calculate charge: [Tn V] * [Tn DBV] + [Tfirst V] * [Tfirst DBV]

    Where:
        TV by DT - Total volume by Discount traffic
        Tn LB - Tier n Lower Bound
        Tn UB - Tier n Upper Bound
        Tn V - Tier n Volume
        Tn DBV - Tier n Discount Basis Value
        Tfirst V - First Tier Volume
        Tfirst DBV - First Tier Discount Basis Value
    """

    def calculate_charge(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:
        """Calculates discount charge based on matched tier."""

        total_volume = calculate_total_volume(traffic_records, discount.volume_type)

        first_tier, *n_tiers = discount.parameters

        tier_charges = []

        for n_tier in reversed(n_tiers):
            if c(n_tier.lower_bound) < total_volume:
                tier_charges.append(c(n_tier.basis_value) * total_volume)
                break

        else:
            tier_charges.append(c(first_tier.basis_value) * total_volume)

        discount_charge = Decimal(sum(tier_charges))

        return discount_charge
