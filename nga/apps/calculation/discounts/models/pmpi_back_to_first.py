from decimal import Decimal
from typing import Sequence

import pandas as pd

from nga.apps.agreements.domain.models import Discount
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.models.abstract import AbstractScalarDiscountModel
from nga.apps.calculation.discounts.models.utils import c
from nga.apps.calculation.utils import get_volume_field_by_type
from nga.core.consts import TRAFFIC_MONTH_FIELD, TRAFFIC_VALUES_FIELDS


class PMPIBackToFirstDiscountModel(AbstractScalarDiscountModel):
    """
    Description:

    The model is applied based on volume for the specific service type "Access Fee".

    Access Fee is applied for all IMSIs in every Month, if defined IMSI Threshold was reached in at least one Month.

    I.e. If any month has number of IMSIs above threshold, Access Fee is applied to all IMSIs in all Months
        (number of IMSIs are aggregated across all Months and multiplied with Access Fee).

    If defined IMSI Treshold was NOT reached in at least one Month, then Access Fee is not applied in any Month.
    """

    def calculate_charge(self, discount: Discount, traffic_records: Sequence[BudgetTrafficRecord]) -> Decimal:
        traffic_df = pd.DataFrame(
            traffic_records,
            columns=[
                TRAFFIC_MONTH_FIELD,
                *TRAFFIC_VALUES_FIELDS,
            ],
        )

        traffic_df = (
            traffic_df.groupby(by=[TRAFFIC_MONTH_FIELD], group_keys=True)[list(TRAFFIC_VALUES_FIELDS)]
            .apply(lambda x: x.sum())
            .reset_index()
        )

        discount_parameter = discount.parameters[0]

        volume_field = get_volume_field_by_type(discount.volume_type)

        traffic_df["lower_bound_is_reached"] = traffic_df[volume_field].apply(
            lambda volume: volume > c(discount_parameter.lower_bound)
        )

        discount_charge = Decimal("0")

        if traffic_df["lower_bound_is_reached"].any():
            total_volume = traffic_df[volume_field].sum()

            discount_charge = total_volume * c(discount_parameter.basis_value)

        return discount_charge
