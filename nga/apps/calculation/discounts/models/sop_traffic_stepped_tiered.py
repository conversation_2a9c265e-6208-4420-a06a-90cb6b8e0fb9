from decimal import Decimal
from typing import Sequence, cast

from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.domain.specifications.discount_setup import SoPTrafficSteppedTieredDiscountSetupSpecification
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.models.abstract import AbstractScalarDiscountModel
from nga.apps.calculation.discounts.models.utils import c
from nga.apps.calculation.utils import calculate_total_volume


class SoPTrafficSteppedTieredDiscountModel(AbstractScalarDiscountModel):
    """
    Name: Send or Pay Traffic + Stepped/Tiered

    Setup:
        * 1st discount parameter with the “Send or Pay Traffic” calculation type.

        * 2nd and following discount parameters with the “Stepped/Tiered” calculation type. 2nd discount parameter
        should have the same Lower Bound value as the “Send or Pay Traffic” discount parameter.

    Description: The model is applied based on volume. SoP means that the partners will pay for
        the specified volume anyway, even if it was not reached. Above SoP, the Stepped Tiered rates will be applied.

    As an input, we need to take the total volume by Discount traffic.
    """

    def __init__(self) -> None:
        self._setup_specification = SoPTrafficSteppedTieredDiscountSetupSpecification()

    def calculate_charge(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:
        """Returns discount charge value."""

        sop_param = self._setup_specification.get_sop_parameter(discount)

        tiers = self._setup_specification.get_tiers_parameters(discount)

        sop_discount_charge_value = c(sop_param.basis_value) * c(sop_param.lower_bound)

        total_volume = calculate_total_volume(traffic_records, discount.volume_type)

        tiers_discount_charge_value = self.calculate_discount_charge_based_on_tiers(sop_param, tiers, total_volume)

        discount_charge_value = sop_discount_charge_value + tiers_discount_charge_value

        return discount_charge_value

    @classmethod
    def calculate_discount_charge_based_on_tiers(
        cls,
        sop_param: DiscountParameter,
        tiers: Sequence[DiscountParameter],
        total_volume: Decimal,
    ) -> Decimal:
        *mid_tiers, last_tier = tiers

        # Define Tier n charge
        tier_charges = []

        sop_lower_bound = c(sop_param.lower_bound)

        for tier in mid_tiers:
            lower_bound = c(tier.lower_bound)
            upper_bound = c(tier.upper_bound)
            basis_value = c(tier.basis_value)

            if sop_lower_bound >= upper_bound:
                continue

            if lower_bound < sop_lower_bound < upper_bound:
                lower_bound = sop_lower_bound

            if total_volume < lower_bound:
                tier_charges.append(Decimal("0"))

            elif lower_bound < total_volume < upper_bound:
                tier_charges.append(basis_value * (total_volume - lower_bound))

            else:
                tier_charges.append(basis_value * (upper_bound - lower_bound))

        # Define last tier charge
        else:
            lower_bound = c(last_tier.lower_bound)
            basis_value = c(last_tier.basis_value)

            if lower_bound < sop_lower_bound:
                lower_bound = sop_lower_bound

            if total_volume < lower_bound:
                tier_charges.append(Decimal("0"))

            else:
                tier_charges.append(basis_value * (total_volume - lower_bound))

        tier_charge_value = sum(tier_charges)

        return cast(Decimal, tier_charge_value)
