from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.models.abstract import AbstractScalarDiscountModel
from nga.apps.calculation.discounts.models.utils import c
from nga.apps.calculation.utils import calculate_total_volume


class SREDiscountModel(AbstractScalarDiscountModel):
    """
    Discount model - Single Rate Effective.
    Description - Model is applied to volume. Single rate will be applied to whole volume amount.
    Formula - [Total Volume by Discount traffic parameters] * Discount Basis Value.
    """

    def calculate_charge(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:
        """Calculates discount charge based on basis value rate."""

        param = discount.parameters[0]

        total_volume = calculate_total_volume(traffic_records, discount.volume_type)

        discount_charge = c(param.basis_value) * total_volume

        return discount_charge
