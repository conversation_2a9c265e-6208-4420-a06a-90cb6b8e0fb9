from decimal import Decimal
from typing import Iterator, Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import PMPIAboveThresholdDiscountSetupSpecification
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.charge_distributor import DiscountChargeDistributor
from nga.apps.calculation.discounts.models.abstract import AbstractScalarDiscountModel
from nga.apps.calculation.discounts.models.utils import group_traffic_records_by_month
from nga.apps.calculation.utils import get_volume_field_by_type


class PMPIAboveThresholdDiscountModel(AbstractScalarDiscountModel):
    """
    Name: Per Month Per IMSI - Above Threshold
    Setup:
        * 1 discount parameter with the “Per Month Per IMSI” calculation type
            and Lower Bound filled (Service type = Access Fee).

    Description: The model is applied based on volume for the specific service type "Access Fee".
        We need to take the volume amount by discount traffic per every month and if it's bigger than the threshold
        then apply a specific rate to it and get the charge value in the result.
    """

    def apply_discount(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:
        """Applies discount to traffic."""

        discounted_traffic_records = []

        monthly_discount_charges = self.calculate_monthly_discount_charges(discount, traffic_records)

        for discount_charge, monthly_traffic_records in monthly_discount_charges:
            _discounted_traffic_records = DiscountChargeDistributor.distribute_on_traffic(
                discount_charge, discount, monthly_traffic_records
            )

            discounted_traffic_records.extend(_discounted_traffic_records)

        return discounted_traffic_records

    def calculate_charge(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:

        total_discount_charge = sum(
            charge for charge, _ in self.calculate_monthly_discount_charges(discount, traffic_records)
        )

        return Decimal(total_discount_charge)

    @classmethod
    def calculate_monthly_discount_charges(
        cls,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Iterator[tuple[Decimal, Sequence[BudgetTrafficRecord]]]:

        traffic_records_per_month = group_traffic_records_by_month(traffic_records)

        volume_attr = get_volume_field_by_type(discount.volume_type)

        spec = PMPIAboveThresholdDiscountSetupSpecification()
        basis_value = spec.get_basis_value(discount)
        lower_bound = spec.get_lower_bound(discount)

        for traffic_month, traffic_month_records in traffic_records_per_month.items():
            total_volume = sum(getattr(r, volume_attr) for r in traffic_month_records)

            discount_charge = Decimal("0")

            if total_volume > lower_bound:
                discount_charge = (total_volume - lower_bound) * basis_value

            yield discount_charge, traffic_month_records
