from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.models.abstract import AbstractScalarDiscountModel
from nga.apps.calculation.discounts.models.utils import c


class AYCEDiscountModel(AbstractScalarDiscountModel):
    """
    Name: All You Can Eat.

    Description: The model is applied based on the TAP charge. A payment of a “flat” financial amount is applied
    regardless of the traffic for the specified Service Type(s).
    """

    def calculate_charge(self, discount: Discount, traffic_records: Sequence[BudgetTrafficRecord]) -> Decimal:
        """Returns total discount charge that equals to lower bound of discount parameter."""

        return c(discount.parameters[0].lower_bound)
