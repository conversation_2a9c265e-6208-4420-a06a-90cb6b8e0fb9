from decimal import Decimal
from typing import Sequence

from dependency_injector.wiring import Closing, Provide

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import SoPTrafficSREDiscountSetupSpecification
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.models.abstract import AbstractScalarDiscountModel
from nga.apps.calculation.discounts.models.sop_traffic_sre.commitment import (
    SoPTrafficSRECommitmentVolumeProviderProtocol,
)
from nga.apps.calculation.discounts.models.utils import c
from nga.apps.calculation.utils import calculate_total_volume


class SoPTrafficSREDiscountModel(AbstractScalarDiscountModel):
    """
    Name:
        Send or Pay Traffic + SRE
    Setup:
        1st discount parameter with the “Send or Pay Traffic” calculation type.
        2nd discount parameter with “Single Rate Effective” calculation type.
    Description:
        The model is applied based on volume. SoP means that the partners will pay for the specified volume anyway,
        even if it was not reached. Above SoP, the SRE rate will be applied.
    Formula:
        1. Define SoP volume: [SoP LB]
        2. Define volume above SoP: If [TV by DT] > [SoP LB] then ([TV by DT] - [SoP LB]) else 0
        3. Calculate charge: [SoP V] * [SoP DBV] + [VA SoP] * [SRE DBV]

        Where:
            TV by DT - Total volume by Discount traffic
            SoP LB - Send or Pay Lower Bound
            SoP V - Send or Pay Volume
            SoP DBV - Send or Pay Discount Basis Value
            VA SoP - Volume above Send or Pay
            SRE DBV - Single Rate Effective Discount Basis Value
    """

    def __init__(
        self,
        commitment_volume_provider: SoPTrafficSRECommitmentVolumeProviderProtocol = Closing[
            Provide["sop_traffic_sre_commitment_volume_provider"]
        ],
    ) -> None:
        super().__init__()

        self._setup_specification = SoPTrafficSREDiscountSetupSpecification()

        self._commitment_volume_provider = commitment_volume_provider

    def calculate_charge(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:
        """Returns discount charge value."""

        sop_parameter = self._setup_specification.get_sop_parameter(discount)
        sre_parameter = self._setup_specification.get_sre_parameter(discount)

        total_volume = calculate_total_volume(traffic_records, discount.volume_type)

        commitment_volume = self._commitment_volume_provider.get_commitment_volume(
            discount=discount,
            discount_total_volume=total_volume,
            # This hack (with source of budget_snapshot_id) is added due to not having direct relation with budget.
            # Discount does not know which budget it belongs to. Only traffic does.
            budget_snapshot_id=traffic_records[0].budget_snapshot_id,
        )

        volume_above_commitment = total_volume - commitment_volume if total_volume > commitment_volume else Decimal("0")

        discount_charge_value = commitment_volume * c(sop_parameter.basis_value) + volume_above_commitment * c(
            sre_parameter.basis_value
        )

        return discount_charge_value
