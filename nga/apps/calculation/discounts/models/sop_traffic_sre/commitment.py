import typing
from decimal import Decimal

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import SoPTrafficSREDiscountSetupSpecification
from nga.apps.agreements.enums import DiscountBoundTypeEnum, DiscountDirectionEnum
from nga.apps.budgets.domain.dto import BudgetTrafficParameters
from nga.apps.calculation.discounts.models.utils import c
from nga.apps.calculation.forecasting.providers import AbstractBudgetAggregationProvider
from nga.apps.calculation.utils import calculate_total_volume
from nga.apps.references.providers import AbstractOperatorProvider
from nga.core.enums import TrafficDirectionEnum
from nga.utils.calc import calculate_percentage_value_of


class SoPTrafficSRECommitmentVolumeProviderProtocol(typing.Protocol):
    def get_commitment_volume(
        self,
        discount: Discount,
        discount_total_volume: Decimal,
        budget_snapshot_id: int,
    ) -> Decimal:
        """Evaluates commitment volume for SoP Traffic SRE discount model."""


class SoPTrafficSRECommitmentVolumeProvider:
    def __init__(
        self,
        budget_aggregation_provider: AbstractBudgetAggregationProvider,
        operator_provider: AbstractOperatorProvider,
    ) -> None:
        self._setup_specification = SoPTrafficSREDiscountSetupSpecification()

        self._budget_aggregation_provider = budget_aggregation_provider

        self._operator_provider = operator_provider

    def _get_commitment_value(self, discount: Discount) -> Decimal:
        return c(self._setup_specification.get_sop_parameter(discount).lower_bound)

    def get_commitment_volume(
        self,
        discount: Discount,
        discount_total_volume: Decimal,
        budget_snapshot_id: int,
    ) -> Decimal:
        sop_parameter = self._setup_specification.get_sop_parameter(discount)

        if sop_parameter.bound_type == DiscountBoundTypeEnum.VOLUME:
            return self._get_commitment_value(discount)

        # Bound type is Market Share
        if discount.direction == DiscountDirectionEnum.OUTBOUND:
            return self._calculate_market_share_volume_for_outbound(discount, budget_snapshot_id)

        elif discount.direction == DiscountDirectionEnum.INBOUND:
            return self._calculate_market_share_volume_for_inbound(discount, discount_total_volume)

        raise ValueError("Invalid setup")

    def _calculate_market_share_volume_for_outbound(
        self,
        discount: Discount,
        budget_snapshot_id: int,
    ) -> Decimal:
        """
        Calculate market share volume.
        Market share volume - is a volume that defined as a market share percentage taken from total partner countries
        volume within a budget.
        """

        partner_operators = self._operator_provider.get_many(operators_ids=discount.partner_operators)

        traffic_parameters = BudgetTrafficParameters(
            snapshot_id=budget_snapshot_id,
            home_operators=discount.home_operators,
            partner_countries=[p.country_id for p in partner_operators],
            traffic_directions=[TrafficDirectionEnum.OUTBOUND],
            service_types=discount.service_types,
            period=discount.period,
            call_destinations=discount.call_destinations,
            called_countries=discount.called_countries,
            traffic_segments=discount.traffic_segments,
        )

        monthly_partner_country_volumes = self._budget_aggregation_provider.get_monthly_records(traffic_parameters)

        total_partner_countries_volume = calculate_total_volume(monthly_partner_country_volumes, discount.volume_type)

        market_share_percentage = c(self._setup_specification.get_sop_parameter(discount).lower_bound)

        commitment_volume = calculate_percentage_value_of(total_partner_countries_volume, market_share_percentage)

        return commitment_volume

    def _calculate_market_share_volume_for_inbound(self, discount: Discount, discount_total_volume: Decimal) -> Decimal:
        market_share_percentage = self._get_commitment_value(discount)

        return discount_total_volume * market_share_percentage / c(discount.inbound_market_share)
