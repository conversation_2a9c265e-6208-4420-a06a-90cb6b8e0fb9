from decimal import Decimal
from typing import Iterator, Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.charge_distributor import DiscountChargeDistributor
from nga.apps.calculation.discounts.models.abstract import AbstractScalarDiscountModel
from nga.apps.calculation.discounts.models.formulas import calculate_stepped_tiered_charges
from nga.apps.calculation.discounts.models.utils import group_traffic_records_by_month
from nga.apps.calculation.utils import get_volume_field_by_type


class PMPISteppedTieredDiscountModel(AbstractScalarDiscountModel):
    """
    Name: Per Month Per IMSI - Stepped/Tiered
    Setup:
        * 2 or more discount parameters with the “Per Month Per IMSI - Stepped/Tiered” calculation type
            (Service type = Access Fee).
        * Discount parameters (except for the last one) should have both Lower Bound and Upper Bound filled.
        * The first DP should have Lower Bound = 0.
        * The Lower Bound of a discount parameter should be equal to the Upper Bound of a previous discount parameter.
        * The last DP should have only Lower Bound filled.

    Description: The model is applied based on volume for the specific service type "Access Fee".
        We need to take the volume amount by discount traffic per every month, split it by tiers, apply a specific rate
        to every tier and get the charge value in the result.

    Formula:
        1. Define Tn volume per month:
            If ([TV] < [Tn UB]) then ([TV] - [Tn LB]) and go to Step 3 else ([Tn UB] - [Tn LB])

        2. Define TLast volume per month: [TV] - [TLast LB]

        3. Calculate charge per month: [Tn V] * [Tn DBV] + [TLast V] * [TLast DBV]

        Where:
            TV - Total volume by Discount traffic per month
            Tn UB - Tier n Upper Bound
            Tn LB - Tier n Lower Bound
            Tlast LB - Last Tier Lower Bound
            Tn V - Tier n Volume
            Tn DBV - Tier n Discount Basis Value
            Tlast V - Last Tier Volume
            Tlast DBV - Last Tier Discount Basis Value
    """

    def apply_discount(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:

        discounted_traffic_records = []

        monthly_discount_charges = self.calculate_monthly_discount_charges(discount, traffic_records)

        for discount_charge, monthly_traffic_records in monthly_discount_charges:

            _discounted_traffic_records = DiscountChargeDistributor.distribute_on_traffic(
                discount_charge, discount, monthly_traffic_records
            )

            discounted_traffic_records.extend(_discounted_traffic_records)

        return discounted_traffic_records

    def calculate_charge(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:

        total_discount_charge = sum(
            charge for charge, _ in self.calculate_monthly_discount_charges(discount, traffic_records)
        )

        return Decimal(total_discount_charge)

    @classmethod
    def calculate_monthly_discount_charges(
        cls,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Iterator[tuple[Decimal, Sequence[BudgetTrafficRecord]]]:

        traffic_records_per_month = group_traffic_records_by_month(traffic_records)

        volume_attr = get_volume_field_by_type(discount.volume_type)

        for traffic_month, traffic_month_records in traffic_records_per_month.items():
            total_volume = Decimal(sum(getattr(r, volume_attr) for r in traffic_month_records))

            tier_charges = calculate_stepped_tiered_charges(discount.parameters, total_volume)

            discount_charge = Decimal(sum(tier_charges))

            yield discount_charge, traffic_month_records
