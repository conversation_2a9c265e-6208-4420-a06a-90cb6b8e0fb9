from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import DiscountParameter
from nga.apps.calculation.discounts.models.utils import c


def calculate_stepped_tiered_charges(tiers: Sequence[DiscountParameter], total_volume: Decimal) -> list[Decimal]:
    *n_tiers, last_tier = tiers

    tier_charges = []

    for n_tier in n_tiers:
        if total_volume < c(n_tier.upper_bound):
            tier_charges.append(c(n_tier.basis_value) * (total_volume - c(n_tier.lower_bound)))
            break
        else:
            tier_charges.append(c(n_tier.basis_value) * (c(n_tier.upper_bound) - c(n_tier.lower_bound)))

    # calculate last tier
    else:
        tier_charges.append(c(last_tier.basis_value) * (total_volume - c(last_tier.lower_bound)))

    return tier_charges
