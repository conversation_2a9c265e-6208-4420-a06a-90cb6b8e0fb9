from abc import ABC, abstractmethod
from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.charge_distributor import DiscountChargeDistributor
from nga.apps.calculation.utils import calculate_total_volume, get_volume_field_by_type
from nga.core.enums import TrafficDirectionEnum


class AbstractDiscountModel(ABC):
    @abstractmethod
    def apply_discount(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:
        """Returns budget traffic records with applied discount."""


class AbstractScalarDiscountModel(AbstractDiscountModel, ABC):
    """Abstract discount model that produces single discount charge value for provided traffic."""

    def apply_discount(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:
        """Returns budget traffic records with applied discount."""

        discount_charge = self.calculate_charge(discount, traffic_records)

        discounted_budget_traffic_records = DiscountChargeDistributor.distribute_on_traffic(
            discount_charge, discount, traffic_records
        )

        return discounted_budget_traffic_records

    @abstractmethod
    def calculate_charge(self, discount: Discount, traffic_records: Sequence[BudgetTrafficRecord]) -> Decimal:
        """Calculates discount charge specified by model rules."""


class AbstractBalancingDiscountModel(AbstractScalarDiscountModel, ABC):
    @classmethod
    def calculate_balanced_volume(cls, discount: Discount, traffic_records: Sequence[BudgetTrafficRecord]) -> Decimal:
        """Performs calculation of balanced volume based on directions."""

        total_by_inbound, total_by_outbound = Decimal("0"), Decimal("0")

        volume_field = get_volume_field_by_type(discount.volume_type)

        for r in traffic_records:
            volume = getattr(r, volume_field)

            if r.traffic_direction == TrafficDirectionEnum.INBOUND:
                total_by_inbound += volume
            else:
                total_by_outbound += volume

        return min(total_by_inbound, total_by_outbound)

    @classmethod
    def calculate_unbalanced_volume(
        cls,
        discount: Discount,
        balanced_volume: Decimal,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:
        """Performs calculation of unbalanced volume based on balanced and traffic volumes."""

        total_volume = calculate_total_volume(traffic_records, discount.volume_type)

        unbalanced_volume = total_volume - balanced_volume if total_volume > balanced_volume else Decimal("0")

        return unbalanced_volume
