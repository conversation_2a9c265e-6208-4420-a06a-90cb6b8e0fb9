from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.models.abstract import AbstractScalarDiscountModel
from nga.apps.calculation.discounts.models.formulas import calculate_stepped_tiered_charges
from nga.apps.calculation.discounts.models.utils import c
from nga.apps.calculation.utils import calculate_total_volume


class SteppedTieredDiscountModel(AbstractScalarDiscountModel):
    """
    The model is calculated based on Volume.
    Volume value will be split by tiers and a specific rate applied to every tier.

    1. Define Tier 1 volume:
        If ([TV by DT] < [T1 UB]) then [TV by DT] and go to Step 4 else [T1 UB]

    2. Define Tier n volume:
        If ([TV by DT] < [Tn UB]) then ([TV by DT] - [Tn LB]) and go to Step 4 else ([Tn UB] - [Tn LB])

    3. Define Last Tier volume:
        [TV by DT] - [TLast LB]

    4. Calculate charge:
        [T1 V] * [T1 DBV] + [Tn V] * [Tn DBV] + [TLast V] * [TLast DBV]

    Where:
        TV by DT - Total volume by Discount traffic
        T1 UB - Tier 1 Upper Bound
        T1 V - Tier 1 Volume
        T1 DBV - Tier 1 Discount Basis Value
        Tn UB - Tier n Upper Bound
        Tn V - Tier n Volume
        Tn DBV - Tier n Discount Basis Value
        TLast LB - Last Tier Lower Bound
        TLast V - Last Tier Volume
        TLast DBV - Last Tier Discount Basis Value
    """

    def calculate_charge(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:
        """Calculates discount charge based on tiers."""

        first_tier, *n_tiers = discount.parameters

        tier_charges = []

        total_volume = calculate_total_volume(traffic_records, discount.volume_type)

        # calculate first tier
        if total_volume < c(first_tier.upper_bound):
            tier_charges.append(c(first_tier.basis_value) * total_volume)

        else:
            tier_charges.append(c(first_tier.basis_value) * c(first_tier.upper_bound))

            middle_tier_charges = calculate_stepped_tiered_charges(n_tiers, total_volume)

            tier_charges.extend(middle_tier_charges)

        discount_charge = Decimal(sum(tier_charges))

        return discount_charge
