from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import PMPIDiscountSetupSpecification
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.models.abstract import AbstractScalarDiscountModel
from nga.apps.calculation.utils import calculate_total_volume


class PMPIDiscountModel(AbstractScalarDiscountModel):
    def calculate_charge(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:
        """Calculates discount charge based on imsi parameter."""

        total_volume = calculate_total_volume(traffic_records, discount.volume_type)

        spec = PMPIDiscountSetupSpecification()

        return total_volume * spec.get_basis_value(discount)
