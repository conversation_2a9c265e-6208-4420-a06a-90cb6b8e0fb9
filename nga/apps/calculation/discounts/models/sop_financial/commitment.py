from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import SoPFinancialDiscountSetupSpecification
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.utils import calculate_total_premium_charge

from .result import SubDiscountResults


class SoPCommitmentCalculator:
    @classmethod
    def calculate(
        cls,
        discount: Discount,
        sub_discount_results: SubDiscountResults,
        discount_traffic_records: Sequence[BudgetTrafficRecord],
    ) -> SubDiscountResults:

        commitment_charge = SoPFinancialDiscountSetupSpecification.get_lower_bound(discount)

        charge_before_commitment = sub_discount_results.initial_total_charge

        if charge_before_commitment == 0:
            sub_discount_results = cls.apply_equal_rate(sub_discount_results, commitment_charge)
        else:
            if discount.exclude_premium and discount.include_premium_in_commitment:
                # In this case sub-discounts have been calculated based on non-premium traffic,
                # but when we check whether commitment is reached or not, we need to consider charge that is based on
                # both premium and non-premium traffic

                premium_charge = calculate_total_premium_charge(discount, discount_traffic_records)

                charge_before_commitment += premium_charge

            if charge_before_commitment < commitment_charge:  # commitment is not reached
                sub_discount_results = cls.reach_to_commitment(sub_discount_results, commitment_charge)

            elif charge_before_commitment >= commitment_charge:  # commitment is reached
                sub_discount_results = cls.apply_above_commitment_rate(sub_discount_results, commitment_charge)

        return sub_discount_results

    @classmethod
    def apply_equal_rate(
        cls,
        sub_discount_results: SubDiscountResults,
        commitment_charge: Decimal,
    ) -> SubDiscountResults:
        """Distributes commitment_charge between sub-discounts."""

        equal_share = Decimal(1 / len(sub_discount_results.discount.sub_discounts))

        for sub_discount_result in sub_discount_results:
            sub_discount_result.charge = commitment_charge * equal_share

        return sub_discount_results

    @classmethod
    def reach_to_commitment(
        cls,
        sub_discount_results: SubDiscountResults,
        commitment_charge: Decimal,
    ) -> SubDiscountResults:
        """Reaches sum of discount charges up to commitment charge."""

        for sub_discount_result in sub_discount_results:
            sub_discount_result.charge = commitment_charge * (
                sub_discount_result.charge / sub_discount_results.initial_total_charge
            )

        return sub_discount_results

    @classmethod
    def apply_above_commitment_rate(
        cls,
        sub_discount_results: SubDiscountResults,
        commitment_charge: Decimal,
    ) -> SubDiscountResults:
        """Applies above commitment rate to volume that is above commitment."""

        for sub_discount_result in sub_discount_results:
            above_commitment_rate = sub_discount_result.sub_discount.above_commitment_rate

            if above_commitment_rate is None:
                continue

            distribution_of_sop = commitment_charge * (
                sub_discount_result.charge / sub_discount_results.initial_total_charge
            )

            average_rate = sub_discount_result.charge / sub_discount_result.volume

            volume_inside_commitment = distribution_of_sop / average_rate

            volume_above_commitment = Decimal("0")

            if distribution_of_sop < sub_discount_result.charge:
                volume_above_commitment = sub_discount_result.volume - volume_inside_commitment

            sub_discount_result.charge = distribution_of_sop + volume_above_commitment * above_commitment_rate

        return sub_discount_results
