"""Module contains logic that relates to manual commitment distribution for SoP Financial discount model."""

from dataclasses import dataclass
from decimal import Decimal
from typing import Optional, Sequence, TypedDict
from uuid import uuid4

import numpy as np
import pandas as pd

from nga.apps.agreements.domain.models import Discount, DiscountTrafficParameters
from nga.apps.agreements.domain.models.discount import CommitmentDistributionParameter
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.charge_distributor import DiscountChargeDistributor
from nga.apps.calculation.discounts.commitment_traffic_factory import CommitmentTrafficFactory
from nga.apps.calculation.discounts.models.sop_financial.result import SubDiscountResults
from nga.apps.calculation.discounts.utils import (
    calculate_total_premium_charge,
    exclude_premium,
    filter_traffic_by_discount_traffic_parameters,
)
from nga.apps.calculation.utils import calculate_total_volume
from nga.utils.calc import series_safely_divide


class CommitmentGroup(TypedDict):
    commitment_id: str
    commitment_charge: Decimal

    sub_discount_id: int
    sub_discount_charge: Decimal
    sub_discount_traffic_volume: Decimal

    above_commitment_rate: Optional[Decimal]

    group_id: str
    group_home_operators: Sequence[int]
    group_partner_operators: Sequence[int]
    group_traffic_volume: Decimal
    group_traffic_premium_charge: Decimal


@dataclass
class CommitmentGroupResult:
    sub_discount: Discount

    traffic_parameters: DiscountTrafficParameters

    charge: Decimal


class SoPCommitmentDistributionCalculator:
    """
    Manual commitment distribution calculation. The main idea behind this concept is providing manual adjustments
    for commitment value between operators.
    The result charge by sub-discounts should split into groups (sub-discount + commitment distribution parameter)
    of operators from the commitment distribution. Then commitment should be applied inside every group separately.
    If the group result is lower than the group SoP value, it should be adjusted to meet the SoP.
    If there is a separate rate above SoP, it should be applied to volume above the commitment.
    """

    def __init__(self, commitment_traffic_factory: CommitmentTrafficFactory) -> None:

        self._commitment_traffic_factory = commitment_traffic_factory

    def calculate(
        self,
        *,
        discount: Discount,
        sub_discount_results: SubDiscountResults,
        traffic_records: Sequence[BudgetTrafficRecord],
        commitment_distribution_parameters: Sequence[CommitmentDistributionParameter],
    ) -> Sequence[BudgetTrafficRecord]:
        """Apply commitment distribution to sub-discount result charges."""

        if discount.commitment_distribution_parameters is None:
            return traffic_records

        commitment_groups = self._collect_calculation_data_for_commitment_groups(
            discount=discount,
            sub_discount_results=sub_discount_results,
            traffic_records=traffic_records,
            commitment_distribution_parameters=commitment_distribution_parameters,
        )

        commitment_group_results = self._calculate_commitment_group_charges(
            discount=discount,
            commitment_groups=commitment_groups,
            commitment_distribution_parameters=commitment_distribution_parameters,
        )

        traffic_records = self._distribute_charges_on_traffic(
            discount=discount,
            traffic_records=traffic_records,
            commitment_group_results=commitment_group_results,
        )

        return traffic_records

    @classmethod
    def _collect_calculation_data_for_commitment_groups(
        cls,
        *,
        discount: Discount,
        sub_discount_results: SubDiscountResults,
        traffic_records: Sequence[BudgetTrafficRecord],
        commitment_distribution_parameters: Sequence[CommitmentDistributionParameter],
    ) -> list[CommitmentGroup]:
        """
        Prepare data for commitment distribution calculation.
        Create CommitmentGroup records which contains sub-discount result charge and traffic charges and volumes
        per each commitment distribution parameter.
        During data preparation Excl. Premium logic is applied.
        """

        commitment_groups = []

        for commitment_distribution_param in commitment_distribution_parameters:

            commitment_distribution_param_key = str(uuid4())

            for sub_discount_result in sub_discount_results:

                sub_discount = sub_discount_result.sub_discount

                traffic_parameters = DiscountTrafficParameters(
                    home_operators=commitment_distribution_param.home_operators,
                    partner_operators=commitment_distribution_param.partner_operators,
                    direction=sub_discount.direction,
                    service_types=sub_discount.service_types,
                    period=sub_discount.period,
                    call_destinations=sub_discount.call_destinations,
                    called_countries=sub_discount.called_countries,
                    traffic_segments=sub_discount.traffic_segments,
                    imsi_count_type=sub_discount.imsi_count_type,
                )

                group_traffic_records = filter_traffic_by_discount_traffic_parameters(
                    traffic_parameters=traffic_parameters,
                    traffic=traffic_records,
                )
                sub_discount_traffic_records = filter_traffic_by_discount_traffic_parameters(
                    traffic_parameters=sub_discount,
                    traffic=traffic_records,
                )

                group_traffic_premium_charge = Decimal("0")

                if discount.exclude_premium:

                    if discount.include_premium_in_commitment:
                        group_traffic_premium_charge = calculate_total_premium_charge(discount, group_traffic_records)

                    group_traffic_records = exclude_premium(group_traffic_records)

                    sub_discount_traffic_records = exclude_premium(sub_discount_traffic_records)

                group_traffic_volume = calculate_total_volume(group_traffic_records, sub_discount.volume_type)

                sub_discount_traffic_volume = calculate_total_volume(
                    traffic_records=sub_discount_traffic_records,
                    volume_type=sub_discount.volume_type,
                )

                commitment_groups.append(
                    CommitmentGroup(
                        commitment_id=commitment_distribution_param_key,
                        commitment_charge=commitment_distribution_param.charge,
                        sub_discount_id=sub_discount.id,
                        sub_discount_charge=sub_discount_result.charge,
                        sub_discount_traffic_volume=sub_discount_traffic_volume,
                        above_commitment_rate=sub_discount.above_commitment_rate,
                        group_id=str(uuid4()),
                        group_home_operators=commitment_distribution_param.home_operators,
                        group_partner_operators=commitment_distribution_param.partner_operators,
                        group_traffic_volume=group_traffic_volume,
                        group_traffic_premium_charge=group_traffic_premium_charge,
                    )
                )

        return commitment_groups

    @classmethod
    def _calculate_commitment_group_charges(
        cls,
        *,
        discount: Discount,
        commitment_groups: Sequence[CommitmentGroup],
        commitment_distribution_parameters: Sequence[CommitmentDistributionParameter],
    ) -> Sequence[CommitmentGroupResult]:
        """Calculate discount charge for each sub-discount based on commitment distribution."""

        df = pd.DataFrame(commitment_groups)
        df.set_index(keys=["group_id"], inplace=True)

        with pd.option_context("future.no_silent_downcasting", True):
            df = df.replace(to_replace={None: np.nan}).infer_objects(copy=False)

        total_commitment_distribution_parameters = len(commitment_distribution_parameters)

        # calculate charge before commitment for each group
        total_groups = total_commitment_distribution_parameters * len(discount.sub_discounts)

        df.loc[df["sub_discount_traffic_volume"] == 0, "group_charge_before_commitment"] = (
            df["sub_discount_charge"] / total_groups
        )

        non_zero_sub_discount_traffic_volume_df = df.loc[df["sub_discount_traffic_volume"] > 0]

        df.loc[df["sub_discount_traffic_volume"] > 0, "group_charge_before_commitment"] = (
            non_zero_sub_discount_traffic_volume_df["group_traffic_volume"]
            * non_zero_sub_discount_traffic_volume_df["sub_discount_charge"]
            / non_zero_sub_discount_traffic_volume_df["sub_discount_traffic_volume"]
        )

        # calculate total charge_before_commitment per commitment distribution parameter
        df = df.reset_index().set_index(keys=["commitment_id"])
        df["param_charge_before_commitment"] = df.groupby(by=["commitment_id"])["group_charge_before_commitment"].sum()

        df = df.reset_index().set_index(keys=["group_id"])

        # calculate share (how many percentage of charge_before_commitment goes per each group)
        df.loc[df["param_charge_before_commitment"] > 0, "share"] = series_safely_divide(
            dividend=df["group_charge_before_commitment"],
            divisor=df["param_charge_before_commitment"],
        )
        equal_share = Decimal(1 / total_commitment_distribution_parameters)
        df.loc[df["param_charge_before_commitment"] == 0, "share"] = equal_share

        df["group_distribution_of_sop"] = df["commitment_charge"] * df["share"]

        df["group_average_rate"] = series_safely_divide(
            dividend=df["group_charge_before_commitment"],
            divisor=df["group_traffic_volume"],
        )

        df["group_volume_inside_commitment"] = series_safely_divide(
            dividend=df["group_distribution_of_sop"],
            divisor=df["group_average_rate"],
        )

        df = df.reset_index().set_index(keys=["group_id"])

        # calculate volume_above_commitment per group
        # If premium is included to commitment check, but excluded by discount,
        # and charge_before_commitment < commitment_charge, but with premium commitment is reached,
        # we need to compensate final discount charge by excluding charge_above_commitment.
        # Then this case group_volume_above_commitment will have negative value,
        # and by adding it to group_distribution_of_sop we will get charge_before_commitment
        df["group_volume_above_commitment"] = df["group_traffic_volume"] - df["group_volume_inside_commitment"]

        # calculate charge_above_commitment when there is no above_commitment_rate in a sub-discount
        df["group_charge_above_commitment"] = df["group_volume_above_commitment"]
        df.loc[df["above_commitment_rate"].isna(), "group_charge_above_commitment"] *= df["group_average_rate"]
        df.loc[~df["above_commitment_rate"].isna(), "group_charge_above_commitment"] *= df["above_commitment_rate"]

        # calculate sub-discount charge per each group
        df["group_charge"] = df["group_distribution_of_sop"]

        commitment_reached_mask = df["param_charge_before_commitment"] >= df["commitment_charge"]

        if discount.exclude_premium and discount.include_premium_in_commitment:
            # In this case sub-discounts have been calculated based on non-premium traffic,
            # but when we check whether commitment is reached or not, we need to consider charge that is based on
            # both premium and non-premium traffic
            commitment_reached_mask = (
                df["param_charge_before_commitment"] + df["group_traffic_premium_charge"] >= df["commitment_charge"]
            )

        df.loc[commitment_reached_mask, "group_charge"] += df["group_charge_above_commitment"]

        # map final discount charges
        group_results: list[CommitmentGroupResult] = []

        sub_discounts_map = {sd.id: sd for sd in discount.sub_discounts}

        for row in df.to_dict("records"):
            sub_discount = sub_discounts_map[row["sub_discount_id"]]

            group_result = CommitmentGroupResult(
                sub_discount=sub_discount,
                traffic_parameters=DiscountTrafficParameters(
                    home_operators=row["group_home_operators"],
                    partner_operators=row["group_partner_operators"],
                    direction=sub_discount.direction,
                    service_types=sub_discount.service_types,
                    period=sub_discount.period,
                    call_destinations=sub_discount.call_destinations,
                    called_countries=sub_discount.called_countries,
                    traffic_segments=sub_discount.traffic_segments,
                    imsi_count_type=sub_discount.imsi_count_type,
                ),
                charge=row["group_charge"],
            )

            group_results.append(group_result)

        return group_results

    def _distribute_charges_on_traffic(
        self,
        *,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
        commitment_group_results: Sequence[CommitmentGroupResult],
    ) -> Sequence[BudgetTrafficRecord]:

        discounted_traffic_records = []

        if discount.exclude_premium:
            traffic_records = exclude_premium(traffic_records)

        for group_result in commitment_group_results:
            group_traffic_records = filter_traffic_by_discount_traffic_parameters(
                traffic_parameters=group_result.traffic_parameters,
                traffic=traffic_records,
            )

            if len(group_traffic_records) == 0:
                group_traffic_records = self._commitment_traffic_factory.create_from_discount_traffic_parameters(
                    discount_traffic_parameters=group_result.traffic_parameters,
                    discount=group_result.sub_discount,
                )

            calculated_traffic_records = DiscountChargeDistributor.distribute_on_traffic(
                charge=group_result.charge,
                discount=group_result.sub_discount,
                traffic_records=group_traffic_records,
            )

            discounted_traffic_records.extend(calculated_traffic_records)

        return discounted_traffic_records
