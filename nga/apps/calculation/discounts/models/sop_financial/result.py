from dataclasses import dataclass, field
from decimal import Decimal
from typing import Iterator, cast

from nga.apps.agreements.domain.models import Discount


@dataclass
class SubDiscountResult:
    """This class represents discounted charge after calculation of sub-discount."""

    sub_discount: Discount

    charge: Decimal

    volume: Decimal


@dataclass
class SubDiscountResults:
    """Collection of sub-discount calculated charges."""

    discount: Discount

    initial_total_charge: Decimal = field(init=False)

    values: list[SubDiscountResult] = field(default_factory=list)

    def __post_init__(self) -> None:
        self.initial_total_charge = self.calculate_total_charge()

    def __iter__(self) -> Iterator[SubDiscountResult]:
        for sd_result in self.values:
            yield sd_result

    def calculate_total_charge(self) -> Decimal:
        total_charge = sum(sd_result.charge for sd_result in self)

        return cast(Decimal, total_charge)
