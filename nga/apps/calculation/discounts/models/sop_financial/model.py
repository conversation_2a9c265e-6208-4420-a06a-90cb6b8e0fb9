from typing import Sequence

from nga.apps.agreements.domain.discount_model_properties import DiscountModelProperties
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import SoPFinancialDiscountSetupSpecification
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.charge_distributor import DiscountChargeDistributor
from nga.apps.calculation.discounts.model_factory.abstract import AbstractDiscountModelFactory
from nga.apps.calculation.discounts.models.abstract import AbstractDiscountModel, AbstractScalarDiscountModel
from nga.apps.calculation.discounts.utils import exclude_premium, filter_traffic_by_discount_traffic_parameters
from nga.apps.calculation.utils import calculate_total_volume

from .commitment import SoPCommitmentCalculator
from .commitment_distribution import SoPCommitmentDistributionCalculator
from .financial_threshold import FinancialThresholdCalculator
from .result import SubDiscountResult, SubDiscountResults


class SoPFinancialDiscountModel(AbstractDiscountModel):
    """
    Name: Send or Pay Financial
    Setup:
        * 1st and only discount parameter with the “Send or Pay Financial” calculation type.
        * Sub-discounts.

    Description:
        Initially, all sub-discounts should be applied. Then if the result is lower than the SoP value,
        the result should be adjusted to meet it.

        Then we need to define the SoP charge value.
        It will be the Lower Bound value from the “Send or Pay Financial“ discount parameter.

        The SoP charge value should be compared with the result (total) of the sub-discounts application.

        If the result of the sub-discounts application is bigger than the SoP charge value,
        then the result of the sub-discounts application will be the final discount calculation result.

        If the result of the sub-discounts application is lower than the SoP charge value,
        then we need to increase the sub-discount result values proportionally to get the SoP charge value.
        For this, we need to distribute the SoP charge value between sub-discount results based on their share
        in the total value. This will be the final discount calculation result.

        We need to get the result (Charge Net and Gross) per each sub-discount.
        Then it should be distributed between traffic rows of the sub-discount based on their volume.
    """

    def __init__(
        self,
        discount_model_factory: AbstractDiscountModelFactory,
        commitment_distribution_calculator: SoPCommitmentDistributionCalculator,
    ) -> None:
        self._discount_model_factory = discount_model_factory

        self._setup_specification = SoPFinancialDiscountSetupSpecification()

        self._commitment_distribution_calculator = commitment_distribution_calculator

    def apply_discount(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:
        """Returns discounted budget traffic records."""

        discounted_traffic_records: list[BudgetTrafficRecord] = []

        directed_discounts = discount.split_by_directions() if discount.is_bidirectional else (discount,)

        for directed_discount in directed_discounts:
            directed_traffic_records = self._apply_directed_discount(directed_discount, traffic_records)

            discounted_traffic_records.extend(directed_traffic_records)

        return discounted_traffic_records

    def _apply_directed_discount(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Sequence[BudgetTrafficRecord]:
        """Applies discount that has single direction (not BIDIRECTIONAL)."""

        discounted_budget_traffic_records = []

        sub_discount_results = self.calculate_sub_discount_charges(discount, traffic_records)

        sub_discount_results = FinancialThresholdCalculator.apply(discount, sub_discount_results)

        if discount.commitment_distribution_parameters is not None:
            return self._commitment_distribution_calculator.calculate(
                discount=discount,
                sub_discount_results=sub_discount_results,
                traffic_records=traffic_records,
                commitment_distribution_parameters=discount.commitment_distribution_parameters,
            )

        sub_discount_results = SoPCommitmentCalculator.calculate(discount, sub_discount_results, traffic_records)

        if discount.exclude_premium:
            traffic_records = exclude_premium(traffic_records)

        for sub_discount_result in sub_discount_results:
            discount_traffic_records = filter_traffic_by_discount_traffic_parameters(
                traffic_parameters=sub_discount_result.sub_discount,
                traffic=traffic_records,
            )

            if sub_discount_result.charge == 0 and len(discount_traffic_records) == 0:
                continue

            calculated_traffic_records = DiscountChargeDistributor.distribute_on_traffic(
                charge=sub_discount_result.charge,
                discount=discount,
                traffic_records=discount_traffic_records,
            )

            discounted_budget_traffic_records.extend(calculated_traffic_records)

        return discounted_budget_traffic_records

    def calculate_sub_discount_charges(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> SubDiscountResults:
        """Calculates discount charge value based on sub-discounts and sop coefficient."""

        sub_discount_results = []

        full_traffic_records = traffic_records

        if discount.exclude_premium:
            traffic_records = exclude_premium(full_traffic_records)

        for sub_discount in discount.sub_discounts:
            model = self.create_sub_discount_model(sub_discount)

            sb_traffic_records = self._filter_traffic_by_sub_discount(sub_discount, traffic_records)

            charge = model.calculate_charge(sub_discount, sb_traffic_records)

            sub_discount_result = SubDiscountResult(
                sub_discount=sub_discount,
                charge=charge,
                volume=calculate_total_volume(sb_traffic_records, sub_discount.volume_type),
            )

            sub_discount_results.append(sub_discount_result)

        results = SubDiscountResults(discount=discount, values=sub_discount_results)

        return results

    @classmethod
    def _filter_traffic_by_sub_discount(
        cls,
        sub_discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Sequence[BudgetTrafficRecord]:

        model_properties = DiscountModelProperties(sub_discount)

        if model_properties.is_balancing:
            discount_traffic_records = filter_traffic_by_discount_traffic_parameters(
                traffic_parameters=sub_discount,
                traffic=traffic_records,
                ignore_direction=True,
            )
        else:
            discount_traffic_records = filter_traffic_by_discount_traffic_parameters(sub_discount, traffic_records)

        return discount_traffic_records

    def create_sub_discount_model(self, sub_discount: Discount) -> AbstractScalarDiscountModel:
        discount_model = self._discount_model_factory.create_from_discount(sub_discount)

        if not isinstance(discount_model, AbstractScalarDiscountModel):
            raise ValueError("sub-discount model must be instance of AbstractScalarDiscountModel.")

        return discount_model
