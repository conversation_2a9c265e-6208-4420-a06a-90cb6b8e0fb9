from decimal import Decimal
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.specifications.discount_setup import BalancedUnbalancedSREDiscountSetupSpecification
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.charge_distributor import DiscountChargeDistributor
from nga.apps.calculation.discounts.models.abstract import AbstractBalancingDiscountModel


class BalancedUnbalancedSREDiscountModel(AbstractBalancingDiscountModel):
    """
    Name: Balanced Unbalanced SRE
    Setup:
        * 1st discount parameter with the “Single Rate Effective” calculation type and “Balanced” Balance Type.
        * 2nd discount parameter with the “Single Rate Effective” calculation type and “Unbalanced” Balance Type.
        * Traffic Direction is “Bi-Directional”.

    Description:
        The model is applied based on volume. We need to take the volume amount by discount traffic, split it by
        balanced and unbalanced, apply a specific rate to balanced and unbalanced volume, and get the charge value
        in the result.

    Formula:
        1. Define Balanced volume: If ([TV Inbound] < [TV Outbound]) then [TV Inbound] else [TV Outbound]

        2. Define Unbalanced volume (per direction): If ([TV] > [BalV]) then ([TV] - [BalV]) else 0

        3. Calculate charge: [Bal V] * [Bal DBV] + [Unbal V] * [Unbal DBV]

    Where:
        TV - Total volume by Discount traffic
        Bal V - Balanced volume by Discount traffic
        Bal DBV - Balanced Discount Basis Value
        Unbal V - Unbalanced volume by Discount traffic
        Unbal DBV - Unbalanced Discount Basis Value
    """

    def apply_discount(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> list[BudgetTrafficRecord]:
        """Applies discount to provided traffic."""

        discount_charge = self.calculate_charge(discount, traffic_records)

        # The model is applied per direction, but to calculate its charge it requires traffic for both directions
        direction_traffic_records = [r for r in traffic_records if r.traffic_direction == discount.traffic_direction]

        discounted_traffic_records = DiscountChargeDistributor.distribute_on_traffic(
            discount=discount,
            charge=discount_charge,
            traffic_records=direction_traffic_records,
        )

        return discounted_traffic_records

    def calculate_charge(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Decimal:
        balanced_volume = self.calculate_balanced_volume(discount, traffic_records)

        direction_traffic_records = [r for r in traffic_records if r.traffic_direction == discount.traffic_direction]

        unbalanced_volume = self.calculate_unbalanced_volume(discount, balanced_volume, direction_traffic_records)

        spec = BalancedUnbalancedSREDiscountSetupSpecification()
        balanced_basis_value = spec.get_balanced_basis_value(discount)
        unbalanced_basis_value = spec.get_unbalanced_basis_value(discount)

        discount_charge = balanced_volume * balanced_basis_value + (unbalanced_volume * unbalanced_basis_value)

        return discount_charge
