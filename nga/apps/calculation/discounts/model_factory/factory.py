from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.enums import DiscountModelTypeEnum
from nga.apps.calculation.discounts.commitment_traffic_factory import CommitmentTrafficFactory
from nga.apps.calculation.discounts.model_factory import AbstractDiscountModelFactory
from nga.apps.calculation.discounts.models.abstract import AbstractDiscountModel
from nga.apps.calculation.discounts.models.ayce import AYCEDiscountModel
from nga.apps.calculation.discounts.models.back_to_first import BackToFirstDiscountModel
from nga.apps.calculation.discounts.models.balanced_unbalanced_sre import BalancedUnbalancedSREDiscountModel
from nga.apps.calculation.discounts.models.balanced_unbalanced_stepped_tiered import (
    BalancedUnbalancedSteppedTieredDiscountModel,
)
from nga.apps.calculation.discounts.models.pmpi import PMPIDiscountModel
from nga.apps.calculation.discounts.models.pmpi_above_threshold import PMPIAboveThresholdDiscountModel
from nga.apps.calculation.discounts.models.pmpi_back_to_first import PMPIBackToFirstDiscountModel
from nga.apps.calculation.discounts.models.pmpi_stepped_tiered import PMPISteppedTieredDiscountModel
from nga.apps.calculation.discounts.models.pmpi_with_incremental_charging import (
    PMPIWithIncrementalChargingDiscountModel,
)
from nga.apps.calculation.discounts.models.sop_financial import SoPFinancialDiscountModel
from nga.apps.calculation.discounts.models.sop_financial.commitment_distribution import (
    SoPCommitmentDistributionCalculator,
)
from nga.apps.calculation.discounts.models.sop_traffic_sre import SoPTrafficSREDiscountModel
from nga.apps.calculation.discounts.models.sop_traffic_stepped_tiered import SoPTrafficSteppedTieredDiscountModel
from nga.apps.calculation.discounts.models.sre import SREDiscountModel
from nga.apps.calculation.discounts.models.stepped_tiered import SteppedTieredDiscountModel


class DiscountModelFactory(AbstractDiscountModelFactory):
    def __init__(self, commitment_traffic_factory: CommitmentTrafficFactory) -> None:
        self._commitment_traffic_factory = commitment_traffic_factory

    def create_from_discount(self, discount: Discount) -> AbstractDiscountModel:
        """Creates root discount model."""

        match discount.model_type:
            case DiscountModelTypeEnum.SINGLE_RATE_EFFECTIVE:
                return SREDiscountModel()

            case DiscountModelTypeEnum.STEPPED_TIERED:
                return SteppedTieredDiscountModel()

            case DiscountModelTypeEnum.SEND_OR_PAY_TRAFFIC_SINGLE_RATE_EFFECTIVE:
                return SoPTrafficSREDiscountModel()

            case DiscountModelTypeEnum.SEND_OR_PAY_TRAFFIC_STEPPED_TIERED:
                return SoPTrafficSteppedTieredDiscountModel()

            case DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL:
                return SoPFinancialDiscountModel(
                    discount_model_factory=self,
                    commitment_distribution_calculator=SoPCommitmentDistributionCalculator(
                        commitment_traffic_factory=self._commitment_traffic_factory,
                    ),
                )

            case DiscountModelTypeEnum.BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE:
                return BalancedUnbalancedSREDiscountModel()

            case DiscountModelTypeEnum.BALANCED_UNBALANCED_STEPPED_TIERED:
                return BalancedUnbalancedSteppedTieredDiscountModel()

            case DiscountModelTypeEnum.BACK_TO_FIRST:
                return BackToFirstDiscountModel()

            case DiscountModelTypeEnum.PER_MONTH_PER_IMSI:
                return PMPIDiscountModel()

            case DiscountModelTypeEnum.PER_MONTH_PER_IMSI_ABOVE_THRESHOLD:
                return PMPIAboveThresholdDiscountModel()

            case DiscountModelTypeEnum.PER_MONTH_PER_IMSI_STEPPED_TIERED:
                return PMPISteppedTieredDiscountModel()

            case DiscountModelTypeEnum.PER_MONTH_PER_IMSI_WITH_INCREMENTAL_CHARGING:
                return PMPIWithIncrementalChargingDiscountModel()

            case DiscountModelTypeEnum.PER_MONTH_PER_IMSI_BACK_TO_FIRST:
                return PMPIBackToFirstDiscountModel()

            case DiscountModelTypeEnum.ALL_YOU_CAN_EAT:
                return AYCEDiscountModel()

            case _:
                raise ValueError("There is no calculation model for provided Discount.")
