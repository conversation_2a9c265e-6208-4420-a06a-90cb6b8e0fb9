import logging
from abc import abstractmethod
from dataclasses import fields
from datetime import date
from decimal import Decimal
from typing import Iterable, Optional, TypedDict, cast

import numpy as np
import pandas as pd
from dateutil.relativedelta import relativedelta
from django.core.paginator import Paginator

from nga.apps.budgets.domain import Budget
from nga.apps.budgets.domain.dto import BudgetTrafficRecordDTO
from nga.apps.budgets.domain.factories.batch_budget_traffic import BatchBudgetTrafficFactory
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.budgets.domain.repositories import AbstractBudgetTrafficRepository
from nga.apps.references.infra.orm.models import ExternalCalculatedTrafficRecord
from nga.apps.references.providers import AbstractOperatorProvider
from nga.core.enums import (
    CallDestinationEnum,
    IMSICountTypeEnum,
    ServiceTypeEnum,
    TrafficDirectionEnum,
    TrafficTypeEnum,
)
from nga.utils.calc import series_safely_divide

logger = logging.getLogger(__name__)


class AbstractExternalDiscountCalculationService:
    @abstractmethod
    def apply_discounts(self, budget: Budget) -> None:
        """Creates traffic based on external calculated traffic for budget."""


class _ECRResultRecord(TypedDict):
    home_operator_id: int
    partner_operator_id: int

    traffic_type: TrafficTypeEnum
    traffic_month: date
    traffic_direction: TrafficDirectionEnum
    traffic_segment_id: int

    service_type: ServiceTypeEnum

    call_destination: Optional[CallDestinationEnum]
    called_country_id: Optional[int]
    is_premium: Optional[bool]

    imsi_count_type: Optional[IMSICountTypeEnum]

    volume_actual: Decimal
    volume_billed: Decimal

    charge_net: Decimal
    charge_gross: Decimal

    tap_charge_net: Decimal
    tap_charge_gross: Decimal


class IOTRONDiscountCalculationService(AbstractExternalDiscountCalculationService):
    _log_fmt = "[iotron-traffic-calculation] [budget_id={budget_id}] {msg}"

    _ecr_page_size = 25000

    def __init__(
        self,
        budget_traffic_repository: AbstractBudgetTrafficRepository,
        operator_provider: AbstractOperatorProvider,
    ) -> None:
        # TODO: maybe we can avoid loading all pmns by scanning ECR records for unique hpmns + ppmns
        self.operator_countries_map: dict[int, int] = {op.id: op.country_id for op in operator_provider.get_many()}

        self._budget_traffic_repository = budget_traffic_repository

    def apply_discounts(self, budget: Budget) -> None:
        self._log(budget, "IOTRON calculation results distribution started")

        self._apply_discounts_to_budget_traffic(budget)

        self._log(budget, "IOTRON calculation results distribution finished. Master Budget Traffic Records are created")

    def _apply_discounts_to_budget_traffic(self, budget: Budget) -> None:
        ecr_records = self._load_ecr_records(budget)

        traffic_factory = BatchBudgetTrafficFactory(self._budget_traffic_repository)

        paginator = Paginator(ecr_records, per_page=self._ecr_page_size)

        for page in range(1, paginator.num_pages + 1):
            qs = paginator.page(page).object_list

            ecr_df = pd.DataFrame(qs)
            ecr_df = _optimize_external_traffic_df(ecr_df)

            if ecr_df.empty is True:
                continue

            discounted_traffic_records = self._apply_ecr_discount_to_traffic(ecr_df, budget)

            dtos = self._map_traffic_records_df_to_btr(discounted_traffic_records, budget.calculation_snapshot_id)

            traffic_factory.add_records(dtos)

        traffic_factory.commit()

    def _load_ecr_records(self, budget: Budget) -> Iterable[_ECRResultRecord]:
        # TODO: Extract loading of records?
        #  By extracting a separate traffic loader we will have database agnostic service

        ecr_records = (
            ExternalCalculatedTrafficRecord.objects.filter(home_operator_id__in=budget.home_operators)
            .exclude(traffic_type=TrafficTypeEnum.HISTORICAL, traffic_month__gt=budget.last_historical_month)
            .values(
                "home_operator_id",
                "partner_operator_id",
                "traffic_month",
                "traffic_type",
                "traffic_direction",
                "traffic_segment_id",
                "service_type",
                "call_destination",
                "volume_actual",
                "volume_billed",
                "charge_net",
                "charge_gross",
                "tap_charge_net",
                "tap_charge_gross",
            )
            .order_by("home_operator_id")
        )

        return ecr_records

    def _apply_ecr_discount_to_traffic(self, ecr_df: pd.DataFrame, budget: Budget) -> list[_ECRResultRecord]:
        """
        Calculation results come from IOTRON without call destination distribution.
        NGA needs charge_net and charge_gross with CD distribution based on historical traffic.
        """

        ecr_df = self._fill_called_country_and_premium(ecr_df, budget)

        ecr_df.replace({np.nan: None}, inplace=True)

        historical_df = self._get_historical_records(ecr_df, call_destinations=None, budget=budget)

        if historical_df.empty:
            return ecr_df.to_dict("records")

        return self._recalculate_traffic_values(ecr_df, historical_df, budget)

    def _fill_called_country_and_premium(self, ecr_df: pd.DataFrame, budget: Budget) -> pd.DataFrame:
        """Sets called_country_id and is_premium for every ECR record in a dataframe."""

        historical_df = self._get_historical_records(
            ecr_df,
            call_destinations=[CallDestinationEnum.HOME, CallDestinationEnum.LOCAL],
            budget=budget,
        )

        for idx, ecr_row in ecr_df.iterrows():
            if pd.isna(ecr_row["call_destination"]):
                continue

            non_international_cd = ecr_row["call_destination"] != CallDestinationEnum.INTERNATIONAL

            service_type_has_cd = ServiceTypeEnum.is_allowed_for_cd(ecr_row["service_type"])

            if service_type_has_cd and non_international_cd:
                called_country_id = self._evaluate_called_country(cast(_ECRResultRecord, ecr_row))

                ecr_df.loc[idx, "called_country_id"] = called_country_id

                is_premium = self._find_is_premium(cast(_ECRResultRecord, ecr_row), called_country_id, historical_df)

                ecr_df.loc[idx, "is_premium"] = is_premium

        return ecr_df

    def _evaluate_called_country(self, ecr_row: _ECRResultRecord) -> Optional[int]:
        """
        Sets called_country_id for HOME and LOCAL call destinations.

        Inbound:
            “Called Country” for “Home” should be filled with Partner PMN’s country;
            “Called Country” for “Local” should be filled with Home PMN’s country.

        Outbound:
            “Called Country” for “Home” should be filled with Home PMN’s country;
            “Called Country” for “Local” should be filled with Partner PMN’s country.
        """

        called_country_id = None

        home_country_id = self.operator_countries_map[ecr_row["home_operator_id"]]

        partner_country_id = self.operator_countries_map[ecr_row["partner_operator_id"]]

        if ecr_row["traffic_direction"] == TrafficDirectionEnum.INBOUND:
            if ecr_row["call_destination"] == CallDestinationEnum.HOME:
                called_country_id = partner_country_id

            elif ecr_row["call_destination"] == CallDestinationEnum.LOCAL:
                called_country_id = home_country_id
        else:
            if ecr_row["call_destination"] == CallDestinationEnum.HOME:
                called_country_id = home_country_id

            elif ecr_row["call_destination"] == CallDestinationEnum.LOCAL:
                called_country_id = partner_country_id

        return called_country_id

    def _find_is_premium(
        self,
        ecr_row: _ECRResultRecord,
        called_country_id: Optional[int],
        historical_traffic_df: pd.DataFrame,
    ) -> Optional[bool]:
        """
        Finds historical traffic record by parameters from ecr_row + passed called_country_id. If it does not exist
        in a previous year, it searches it until reaches budget start date.
        """

        historical_row_df = self._find_historical_traffic(
            historical_df=historical_traffic_df,
            traffic_month=ecr_row["traffic_month"],
            home_operator_id=ecr_row["home_operator_id"],
            partner_operator_id=ecr_row["partner_operator_id"],
            traffic_direction=ecr_row["traffic_direction"],
            service_type=ecr_row["service_type"],
            call_destination=ecr_row["call_destination"],
            traffic_segment_id=ecr_row["traffic_segment_id"],
            called_country_id=called_country_id,
            search_in_previous_years=True,
        )

        historical_row_df = _optimize_traffic_df(historical_row_df)

        if not historical_row_df.empty:
            is_premium_value = historical_row_df["is_premium"].array[0]

            if isinstance(is_premium_value, np.bool_):
                return is_premium_value.item()
            else:
                return is_premium_value

        return None

    def _recalculate_traffic_values(
        self,
        ecr_df: pd.DataFrame,
        historical_df: pd.DataFrame,
        budget: Budget,
    ) -> list[_ECRResultRecord]:
        """
        Distributes records based on historical monthly aggregations. Finds historical traffic for each record, and
        calculates volumes and charges fields based on historical share.
        If historical traffic does not exist - unmodified record is added to result.
        """

        result_records = []

        btr_ids_for_removal: list[int] = []

        for index, ecr_row in ecr_df.iterrows():
            search_in_previous_years = ecr_row["traffic_type"] == TrafficTypeEnum.FORECASTED

            _h_traffic_df = self._find_historical_traffic(
                historical_df=historical_df,
                traffic_month=ecr_row["traffic_month"],
                home_operator_id=ecr_row["home_operator_id"],
                partner_operator_id=ecr_row["partner_operator_id"],
                traffic_direction=ecr_row["traffic_direction"],
                service_type=ecr_row["service_type"],
                call_destination=ecr_row["call_destination"],
                traffic_segment_id=ecr_row["traffic_segment_id"],
                search_in_previous_years=search_in_previous_years,
            )

            if _h_traffic_df.empty is True:
                # if historical traffic does not exist we must save pure ECR record to Budget
                result_records.append(ecr_row)

            else:
                # if historical traffic exists we need to apply discounts from ECR record to that traffic records

                if ecr_row["traffic_type"] == TrafficTypeEnum.FORECASTED:
                    # Here we need to take historical traffic records as a basis for volumes and charges modification.
                    # In a result these traffic records will be FORECASTED (as ECR is). Historical records must be kept
                    # in a budget along with this FORECASTED copy

                    _h_traffic_df["traffic_month"] = ecr_row["traffic_month"]
                    _h_traffic_df["traffic_type"] = TrafficTypeEnum.FORECASTED.value
                else:
                    # Historical traffic records must be updated with new charges and volumes. To make this optimal, we
                    # need to remove these records from a budget, and create new with recalculated volumes and charges

                    btr_ids_for_removal.extend(tuple(_h_traffic_df["id"]))

                total_volume_actual = sum(_h_traffic_df["volume_actual"])

                charge_rate = series_safely_divide(_h_traffic_df["volume_actual"], total_volume_actual)

                # Historical traffic records can be with 0 volumes and because of this charge_rate will be Decimal("0").
                # In this case, volumes from ecr_row multiply on Decimal("0") and any volume has Decimal("0") in result.

                if (charge_rate == Decimal("0")).all().item() is True:

                    # This is a single case when for all Historical Records with volumes 0 we have to specify
                    # the same share of the ECR value
                    charge_rate = Decimal("1") / len(_h_traffic_df.index)

                _h_traffic_df["volume_actual"] = ecr_row["volume_actual"] * charge_rate
                _h_traffic_df["volume_billed"] = ecr_row["volume_billed"] * charge_rate

                _h_traffic_df["tap_charge_net"] = ecr_row["tap_charge_net"] * charge_rate
                _h_traffic_df["tap_charge_gross"] = ecr_row["tap_charge_gross"] * charge_rate

                _h_traffic_df["charge_net"] = ecr_row["charge_net"] * charge_rate
                _h_traffic_df["charge_gross"] = ecr_row["charge_gross"] * charge_rate

                result_records.extend(_h_traffic_df.to_dict("records"))

        if btr_ids_for_removal:
            self._budget_traffic_repository.delete_many(
                budget_snapshot_id=budget.calculation_snapshot_id,
                ids=btr_ids_for_removal,
            )

        return result_records

    @classmethod
    def _find_historical_traffic(
        cls,
        historical_df: pd.DataFrame,
        traffic_month: date,
        home_operator_id: int,
        partner_operator_id: int,
        traffic_direction: int,
        service_type: int,
        call_destination: Optional[int] = None,
        traffic_segment_id: Optional[int] = None,
        called_country_id: Optional[int] = None,
        search_in_previous_years: bool = False,
    ) -> pd.DataFrame:
        h_rows = pd.DataFrame()
        min_traffic_month = historical_df["traffic_month"].min()

        main_condition = (
            (historical_df["home_operator_id"] == home_operator_id)
            & (historical_df["partner_operator_id"] == partner_operator_id)
            & (historical_df["traffic_type"] == TrafficTypeEnum.HISTORICAL)
            & (historical_df["traffic_direction"] == traffic_direction)
            & (historical_df["service_type"] == service_type)
        )

        if call_destination is None:
            main_condition &= pd.isna(historical_df["call_destination"])
        else:
            main_condition &= historical_df["call_destination"] == call_destination

        if called_country_id is not None:
            main_condition &= historical_df["called_country_id"] == called_country_id

        if traffic_segment_id is None:
            main_condition &= pd.isna(historical_df["traffic_segment_id"])
        else:
            main_condition &= historical_df["traffic_segment_id"] == traffic_segment_id

        while traffic_month >= min_traffic_month:
            condition = main_condition & (historical_df["traffic_month"] == traffic_month)

            h_rows = historical_df.loc[condition]

            if h_rows.empty is False or search_in_previous_years is False:
                break

            traffic_month -= relativedelta(years=1)

        return _optimize_traffic_df(h_rows)

    def _get_historical_records(
        self,
        ecr_df: pd.DataFrame,
        call_destinations: Optional[list[CallDestinationEnum]],
        budget: Budget,
    ) -> pd.DataFrame:
        home_operators = list(ecr_df["home_operator_id"].unique())
        partner_operators = list(ecr_df["partner_operator_id"].unique())
        traffic_directions = list(ecr_df["traffic_direction"].unique())

        traffic_rows = self._budget_traffic_repository.get_many(
            snapshot_id=budget.calculation_snapshot_id,
            home_operators=home_operators,
            partner_operators=partner_operators,
            traffic_directions=traffic_directions,
            call_destinations=call_destinations,
        )

        columns = {f.name for f in fields(BudgetTrafficRecord)}
        unnecessary_columns = {"forecast_rule_id", "discount_id"}

        historical_df = pd.DataFrame(traffic_rows, columns=list(columns - unnecessary_columns))
        historical_df = _optimize_traffic_df(historical_df)

        return historical_df

    @staticmethod
    def _map_traffic_records_df_to_btr(
        result_records: list[_ECRResultRecord],
        budget_snapshot_id: int,
    ) -> tuple[BudgetTrafficRecordDTO, ...]:
        return tuple(
            BudgetTrafficRecordDTO(
                budget_snapshot_id=budget_snapshot_id,
                traffic_month=tr_record["traffic_month"],
                traffic_type=tr_record["traffic_type"],
                traffic_direction=tr_record["traffic_direction"],
                traffic_segment_id=tr_record["traffic_segment_id"],
                service_type=tr_record["service_type"],
                volume_actual=tr_record["volume_actual"],
                volume_billed=tr_record["volume_billed"],
                charge_net=tr_record["charge_net"],
                charge_gross=tr_record["charge_gross"],
                home_operator_id=tr_record["home_operator_id"],
                partner_operator_id=tr_record["partner_operator_id"],
                call_destination=tr_record["call_destination"],
                called_country_id=tr_record.get("called_country_id"),
                is_premium=tr_record.get("is_premium"),
                tap_charge_net=tr_record["tap_charge_net"],
                tap_charge_gross=tr_record["tap_charge_gross"],
                imsi_count_type=tr_record.get("imsi_count_type"),
                forecast_rule_id=None,
                discount_id=None,
            )
            for tr_record in result_records
        )

    def _log(self, budget: Budget, msg: str) -> None:
        logger.info(self._log_fmt.format(budget_id=budget.id, msg=msg))


def _optimize_external_traffic_df(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty:
        return df

    df = df.astype(
        {
            "traffic_type": pd.UInt8Dtype(),
            "traffic_direction": pd.UInt8Dtype(),
            "traffic_segment_id": pd.UInt16Dtype(),
            "service_type": pd.UInt8Dtype(),
            "home_operator_id": pd.UInt16Dtype(),
            "partner_operator_id": pd.UInt16Dtype(),
            "call_destination": pd.UInt8Dtype(),
        },
        copy=False,
    )

    df.replace({pd.NA: None, np.nan: None}, inplace=True)

    return df


def _optimize_traffic_df(df: pd.DataFrame) -> pd.DataFrame:
    if df.empty:
        return df

    df = df.astype(
        {
            "traffic_type": pd.UInt8Dtype(),
            "traffic_direction": pd.UInt8Dtype(),
            "traffic_segment_id": pd.UInt16Dtype(),
            "service_type": pd.UInt8Dtype(),
            "home_operator_id": pd.UInt16Dtype(),
            "partner_operator_id": pd.UInt16Dtype(),
            "budget_snapshot_id": pd.UInt16Dtype(),
            "call_destination": pd.UInt8Dtype(),
            "called_country_id": pd.UInt16Dtype(),
            "is_premium": pd.BooleanDtype(),
            "imsi_count_type": pd.UInt8Dtype(),
        },
        copy=False,
    )

    df.replace({pd.NA: None, np.nan: None}, inplace=True)

    return df
