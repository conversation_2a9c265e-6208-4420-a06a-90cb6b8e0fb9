"""
This module implements logic that is required for creating zero values traffic in a case when discount does not have
traffic before the calculation. Such discount must support commitment logic.
"""

import itertools
from decimal import Decimal
from typing import Optional, Sequence, TypeAlias, cast

from nga.apps.agreements.domain.models import Discount, DiscountTrafficParameters
from nga.apps.budgets.domain.dto import BudgetTrafficRecordDTO
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.budgets.domain.repositories import AbstractBudgetTrafficRepository
from nga.apps.calculation.discounts.utils import from_discount_direction_to_traffic_directions
from nga.core.enums import (
    CallDestinationEnum,
    IMSICountTypeEnum,
    ServiceTypeEnum,
    TrafficDirectionEnum,
    TrafficTypeEnum,
)
from nga.core.types import Month

_BudgetTrafficParameters: TypeAlias = tuple[
    int,  # home_operator_id
    int,  # partner_operator_id
    TrafficDirectionEnum,
    ServiceTypeEnum,
    Month,  # traffic_month
    Optional[CallDestinationEnum],
    Optional[int],  # called_country_id
    Optional[int],  # traffic_segment_id
    Optional[IMSICountTypeEnum],
]


class CommitmentTrafficFactory:
    def __init__(
        self,
        budget_traffic_repository: AbstractBudgetTrafficRepository,
    ) -> None:
        self._budget_traffic_repository = budget_traffic_repository

    def create_from_discount(self, discount: Discount) -> Sequence[BudgetTrafficRecord]:
        if discount.has_sub_discounts:
            budget_traffic_parameters: list[_BudgetTrafficParameters] = []

            for sub_discount in discount.sub_discounts:
                sub_discount_parameters_combinations = self._create_budget_traffic_parameters(sub_discount)

                budget_traffic_parameters.extend(sub_discount_parameters_combinations)
        else:
            budget_traffic_parameters = self._create_budget_traffic_parameters(discount)

        budget_traffic_records = self._create_budget_traffic_records(
            discount=discount,
            discount_traffic_parameters=discount,
            budget_traffic_parameters=budget_traffic_parameters,
        )

        return budget_traffic_records

    def create_from_discount_traffic_parameters(
        self,
        discount_traffic_parameters: DiscountTrafficParameters,
        discount: Discount,
    ) -> tuple[BudgetTrafficRecord, ...]:

        budget_traffic_parameters = self._create_budget_traffic_parameters(discount_traffic_parameters)

        budget_traffic_records = self._create_budget_traffic_records(
            discount=discount,
            discount_traffic_parameters=discount_traffic_parameters,
            budget_traffic_parameters=budget_traffic_parameters,
        )

        return budget_traffic_records

    @classmethod
    def _create_budget_traffic_parameters(
        cls,
        traffic_parameters: DiscountTrafficParameters,
    ) -> list[_BudgetTrafficParameters]:
        """
        Creates unique combinations of budget traffic parameters based on discount traffic parameters
        (budget traffic parameters hold only single values while discount traffic parameters hold multiple).
        """

        values = [
            traffic_parameters.home_operators,
            traffic_parameters.partner_operators,
            from_discount_direction_to_traffic_directions(traffic_parameters.direction),
            traffic_parameters.service_types,
            list(traffic_parameters.period),
            traffic_parameters.call_destinations or [None],
            traffic_parameters.called_countries or [None],
            traffic_parameters.traffic_segments or [None],
            [traffic_parameters.imsi_count_type],
        ]

        combinations = cast(list[_BudgetTrafficParameters], list(itertools.product(*values)))

        return combinations

    def _create_budget_traffic_records(
        self,
        discount: Discount,
        discount_traffic_parameters: DiscountTrafficParameters,
        budget_traffic_parameters: list[_BudgetTrafficParameters],
    ) -> tuple[BudgetTrafficRecord, ...]:

        budget = discount.get_budget()

        budget_traffic_record_dtos = []

        for parameters in budget_traffic_parameters:
            (
                home_operator_id,
                partner_operator_id,
                traffic_direction,
                service_type,
                traffic_month,
                call_destination,
                called_country_id,
                traffic_segment_id,
                imsi_count_type,
            ) = parameters

            traffic_type = (
                TrafficTypeEnum.HISTORICAL
                if traffic_month <= budget.last_historical_month
                else TrafficTypeEnum.FORECASTED
            )

            if call_destination is None:
                call_destination = service_type.get_default_cd()

            dto = BudgetTrafficRecordDTO(
                budget_snapshot_id=budget.calculation_snapshot_id,
                home_operator_id=home_operator_id,
                partner_operator_id=partner_operator_id,
                traffic_type=traffic_type,
                traffic_direction=traffic_direction,
                traffic_month=traffic_month.to_date(),
                service_type=service_type,
                call_destination=call_destination,
                called_country_id=called_country_id,
                is_premium=None,
                traffic_segment_id=traffic_segment_id,
                imsi_count_type=imsi_count_type,
                forecast_rule_id=None,
                discount_id=discount.id,
                volume_actual=Decimal("0"),
                volume_billed=Decimal("0"),
                charge_net=Decimal("0"),
                charge_gross=Decimal("0"),
                tap_charge_net=Decimal("0"),
                tap_charge_gross=Decimal("0"),
            )

            budget_traffic_record_dtos.append(dto)

        self._budget_traffic_repository.create_many(budget_traffic_record_dtos)

        budget_traffic_records = self._budget_traffic_repository.get_many(
            budget.calculation_snapshot_id,
            home_operators=discount_traffic_parameters.home_operators,
            partner_operators=discount_traffic_parameters.partner_operators,
            period=discount_traffic_parameters.period,
            traffic_directions=from_discount_direction_to_traffic_directions(discount_traffic_parameters.direction),
            service_types=discount_traffic_parameters.service_types,
            call_destinations=discount_traffic_parameters.call_destinations,
            called_countries=discount_traffic_parameters.called_countries,
            traffic_segments=discount_traffic_parameters.traffic_segments,
        )

        return tuple(budget_traffic_records)
