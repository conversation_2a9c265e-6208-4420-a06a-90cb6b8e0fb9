from abc import ABC, abstractmethod
from typing import Sequence

from nga.apps.agreements.domain.models import Discount
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.exchange_rate_store import ExchangeRateStore
from nga.apps.references.exceptions import ExchangeRateDoesNotExist
from nga.apps.references.providers import AbstractExchangeRateProvider


class AbstractTrafficCurrencyConvertor(ABC):
    @abstractmethod
    def from_currency_to_xdr(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Sequence[BudgetTrafficRecord]:
        """Applies exchange rates of provided currency to charges and tap charges to convert to XDR."""


class TrafficCurrencyConvertor(AbstractTrafficCurrencyConvertor):
    def __init__(self, exchange_rate_provider: AbstractExchangeRateProvider) -> None:
        self._exchange_rate_provider = exchange_rate_provider

    def from_currency_to_xdr(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Sequence[BudgetTrafficRecord]:
        """
        Applies exchange rates of provided currency to charges and tap charges to convert to XDR.
        Now, this module is used only for discount calculation, and for discounting we don't need to calculate
        tap charges. Tap charges are not calculated by this method.
        """

        budget = discount.get_budget()

        exchange_rate_store = ExchangeRateStore.init(
            budget=budget,
            period=discount.period,
            currency_codes=[discount.currency_code],
            exchange_rate_provider=self._exchange_rate_provider,
        )

        for record in traffic_records:

            exchange_rate_value = exchange_rate_store.get_value(discount.currency_code, record)

            if exchange_rate_value is None:
                raise ExchangeRateDoesNotExist(discount.currency_code, record.traffic_month)

            record.charge_net /= exchange_rate_value
            record.charge_gross /= exchange_rate_value

        return traffic_records
