from decimal import Decimal
from typing import Iterable, Sequence

from nga.apps.agreements.domain.models import Discount, DiscountTrafficParameters
from nga.apps.agreements.enums import DiscountDirectionEnum
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.utils import calculate_total_traffic_value_by_field, get_tap_charge_field_by_tax_type
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum


def filter_traffic_by_discount_traffic_parameters(
    traffic_parameters: DiscountTrafficParameters,
    traffic: Iterable[BudgetTrafficRecord],
    ignore_direction: bool = False,
) -> tuple[BudgetTrafficRecord, ...]:
    """Filter collection of traffic records by Discount traffic parameters."""

    discount_traffic_months = list(traffic_parameters.period)

    result = (
        r
        for r in traffic
        if r.home_operator_id in traffic_parameters.home_operators
        and r.partner_operator_id in traffic_parameters.partner_operators
        and r.service_type in traffic_parameters.service_types
        and r.traffic_month in discount_traffic_months
    )

    if ignore_direction is False:
        if traffic_parameters.direction == DiscountDirectionEnum.INBOUND:
            result = (r for r in result if r.traffic_direction == TrafficDirectionEnum.INBOUND)

        elif traffic_parameters.direction == DiscountDirectionEnum.OUTBOUND:
            result = (r for r in result if r.traffic_direction == TrafficDirectionEnum.OUTBOUND)

    if traffic_parameters.call_destinations:
        result = (r for r in result if r.call_destination in traffic_parameters.call_destinations)

    if traffic_parameters.called_countries:
        result = (r for r in result if r.called_country_id in traffic_parameters.called_countries)

    if traffic_parameters.traffic_segments:
        result = (r for r in result if r.traffic_segment_id in traffic_parameters.traffic_segments)

    if ServiceTypeEnum.ACCESS_FEE in traffic_parameters.service_types and traffic_parameters.imsi_count_type:
        # if ACCESS_FEE service type is set, and imsi_count_type is set then, we need to filter records by
        #  imsi_count_type only for those records which contain ACCESS_FEE service type. For records with different
        #  service type, imsi_count_type is ignored

        result = (
            r
            for r in result
            if (
                r.imsi_count_type == traffic_parameters.imsi_count_type and r.service_type == ServiceTypeEnum.ACCESS_FEE
            )
            or r.service_type != ServiceTypeEnum.ACCESS_FEE
        )

    return tuple(result)


def from_discount_direction_to_traffic_directions(
    discount_direction: DiscountDirectionEnum,
) -> Sequence[TrafficDirectionEnum]:
    traffic_directions = []

    match discount_direction:
        case DiscountDirectionEnum.INBOUND:
            traffic_directions = [TrafficDirectionEnum.INBOUND]

        case DiscountDirectionEnum.OUTBOUND:
            traffic_directions = [TrafficDirectionEnum.OUTBOUND]

        case DiscountDirectionEnum.BIDIRECTIONAL:
            traffic_directions = [TrafficDirectionEnum.INBOUND, TrafficDirectionEnum.OUTBOUND]

    return traffic_directions


def exclude_premium(traffic_records: Sequence[BudgetTrafficRecord]) -> tuple[BudgetTrafficRecord, ...]:
    return tuple(r for r in traffic_records if r.is_premium is not True)


def only_premium(traffic_records: Sequence[BudgetTrafficRecord]) -> tuple[BudgetTrafficRecord, ...]:
    return tuple(r for r in traffic_records if r.is_premium is True)


def calculate_total_premium_charge(discount: Discount, traffic_records: Sequence[BudgetTrafficRecord]) -> Decimal:
    premium_traffic_records = only_premium(traffic_records)

    charge_field = get_tap_charge_field_by_tax_type(discount.tax_type)

    premium_charge = calculate_total_traffic_value_by_field(charge_field, premium_traffic_records)

    return premium_charge
