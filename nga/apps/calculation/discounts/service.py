from abc import ABC, abstractmethod
from typing import Sequence

from nga.apps.agreements.domain.discount_model_properties import DiscountModelProperties
from nga.apps.agreements.domain.models import Discount
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.commitment_traffic_factory import CommitmentTrafficFactory
from nga.apps.calculation.discounts.model_factory.abstract import AbstractDiscountModelFactory
from nga.apps.calculation.discounts.models.abstract import AbstractDiscountModel
from nga.apps.calculation.discounts.traffic_currency_convertor import AbstractTrafficCurrencyConvertor


class AbstractDiscountCalculationService(ABC):
    @abstractmethod
    def apply_discount_to_traffic(
        self,
        discount: Discount,
        budget_snapshot_id: int,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Sequence[BudgetTrafficRecord]:
        """Applies agreement discount charges to budget traffic."""


class DiscountCalculationService(AbstractDiscountCalculationService):
    def __init__(
        self,
        discount_calculation_model_factory: AbstractDiscountModelFactory,
        traffic_currency_convertor: AbstractTrafficCurrencyConvertor,
        commitment_traffic_factory: CommitmentTrafficFactory,
    ) -> None:

        self._discount_calculation_model_factory = discount_calculation_model_factory

        self._traffic_currency_convertor = traffic_currency_convertor

        self._commitment_traffic_factory = commitment_traffic_factory

    def apply_discount_to_traffic(
        self,
        discount: Discount,
        budget_snapshot_id: int,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Sequence[BudgetTrafficRecord]:
        """Calculates discount charges and distributes in onto specified traffic."""

        discount_model = self._discount_calculation_model_factory.create_from_discount(discount)

        traffic_records = self._exclude_premium_if_discount_setup(discount, traffic_records)

        discount_model_properties = DiscountModelProperties(discount)

        if discount_model_properties.has_commitment and not traffic_records:
            traffic_records = self._commitment_traffic_factory.create_from_discount(discount)

        if discount_model_properties.is_bidirectional:
            split_traffic_by_direction = True

            if discount_model_properties.supports_sub_discounts or discount_model_properties.is_balancing:
                split_traffic_by_direction = False

            discounted_traffic_records = self.apply_for_both_directions(
                discount=discount,
                discount_model=discount_model,
                traffic_records=traffic_records,
                split_traffic_by_direction=split_traffic_by_direction,
            )
        else:
            discounted_traffic_records = self.apply_once(discount, discount_model, traffic_records)

        discounted_traffic_records = self._traffic_currency_convertor.from_currency_to_xdr(
            discount=discount,
            traffic_records=discounted_traffic_records,
        )

        return discounted_traffic_records

    @classmethod
    def _exclude_premium_if_discount_setup(
        cls,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Sequence[BudgetTrafficRecord]:
        """
        Apply "exclude premium" logic to traffic that will be used for calculation.

        Traffic without premium records is passed to model, when model is not nested and does not have commitment,
        and with having include_premium=False.

        Specific case:
            With having include_premium=False, traffic with is_premium=True can be passed to model only
            when model is SopFinancial and include_premium_in_commitment=True.
        """

        discount_model_properties = DiscountModelProperties(discount)

        if discount.exclude_premium:

            if discount_model_properties.is_sop_financial and discount.include_premium_in_commitment:
                return traffic_records

            traffic_records = [r for r in traffic_records if not r.is_premium]

        return traffic_records

    @classmethod
    def apply_for_both_directions(
        cls,
        discount: Discount,
        discount_model: AbstractDiscountModel,
        traffic_records: Sequence[BudgetTrafficRecord],
        split_traffic_by_direction: bool = True,
    ) -> Sequence[BudgetTrafficRecord]:
        """
        Applies model to INBOUND and OUTBOUND traffic.
        If `split_traffic_by_direction` traffic is filtered by each traffic direction. If splitting is turned off
        traffic will not be filtered and will be passed fully for calculation for every direction.
        """

        discounted_traffic_records = []

        for directed_discount in discount.split_by_directions():
            per_direction_records = (
                tuple(r for r in traffic_records if r.traffic_direction == directed_discount.traffic_direction)
                if split_traffic_by_direction
                else traffic_records
            )

            if per_direction_records:
                direction_discounted_traffic_records = discount_model.apply_discount(
                    discount=directed_discount,
                    traffic_records=per_direction_records,
                )

                discounted_traffic_records.extend(direction_discounted_traffic_records)

        return discounted_traffic_records

    @classmethod
    def apply_once(
        cls,
        discount: Discount,
        discount_model: AbstractDiscountModel,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> Sequence[BudgetTrafficRecord]:
        """Applies discount model to initially provided traffic."""

        if not len(traffic_records):
            return []

        discounted_traffic_records = discount_model.apply_discount(discount, traffic_records)

        return discounted_traffic_records
