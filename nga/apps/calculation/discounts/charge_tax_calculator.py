from decimal import Decimal


class ChargeTaxCalculator:
    @classmethod
    def add_tax(cls, charge: Decimal, tap_charge_net: Decimal, tap_charge_gross: Decimal) -> Decimal:
        if tap_charge_net == 0 or tap_charge_gross == 0:
            return charge

        with_tax_charge = charge * tap_charge_gross / tap_charge_net

        return with_tax_charge

    @classmethod
    def subtract_tax(cls, charge: Decimal, tap_charge_net: Decimal, tap_charge_gross: Decimal) -> Decimal:
        if tap_charge_net == 0 or tap_charge_gross == 0:
            return charge

        without_tax_charge = charge / tap_charge_gross * tap_charge_net

        return without_tax_charge
