import typing
from typing import Sequence

from nga.apps.agreements.domain.discount_model_properties import DiscountModelProperties
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.value_objects import DiscountQualifyingRule
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.calculation.discounts.qualifying_result import DiscountQualifyingResult
from nga.apps.calculation.discounts.utils import from_discount_direction_to_traffic_directions
from nga.apps.calculation.utils import calculate_total_volume


class DiscountQualifyingServiceProtocol(typing.Protocol):
    """
    Qualifying is a process of discount validation. If the discount is qualified then it is allowed to be used in the
    calculation, otherwise the discount is ignored.
    """

    def qualify(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> tuple[Discount, DiscountQualifyingResult]:
        """Qualifies discount by provided qualifying rule in the agreement traffic."""


class DiscountQualifyingService:
    def qualify(
        self,
        discount: Discount,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> tuple[Discount, DiscountQualifyingResult]:
        """Qualifies discount by provided qualifying rule in the agreement traffic."""

        result = DiscountQualifyingResult()

        model_properties = DiscountModelProperties(discount=discount)

        if discount.qualifying_rule is not None:
            result = self._qualify_discount(discount.qualifying_rule, traffic_records)

        elif model_properties.is_sop_financial:
            discount = self._qualify_sub_discounts(discount, traffic_records)

            result.succeeded = True

        return discount, result

    def _qualify_discount(
        self,
        qualifying_rule: DiscountQualifyingRule,
        traffic_records: Sequence[BudgetTrafficRecord],
    ) -> DiscountQualifyingResult:
        """Verifies whether volume by discount is within qualifying tier."""

        qualifying_traffic_directions = from_discount_direction_to_traffic_directions(qualifying_rule.direction)

        qualifying_traffic_records = [
            r
            for r in traffic_records
            if r.service_type in qualifying_rule.service_types and r.traffic_direction in qualifying_traffic_directions
        ]

        total_volume = calculate_total_volume(qualifying_traffic_records, qualifying_rule.volume_type)

        result = DiscountQualifyingResult()

        if qualifying_rule.is_volume_within_bounds(total_volume):
            result.succeeded = True

        return result

    def _qualify_sub_discounts(self, discount: Discount, traffic_records: Sequence[BudgetTrafficRecord]) -> Discount:
        qualified_sub_discounts = []

        for sd in discount.sub_discounts:

            if sd.qualifying_rule is None:
                continue

            result = self._qualify_discount(sd.qualifying_rule, traffic_records)

            if result.succeeded:
                qualified_sub_discounts.append(sd)

        if len(qualified_sub_discounts) > 0:
            discount.sub_discounts = tuple(qualified_sub_discounts)

        return discount
