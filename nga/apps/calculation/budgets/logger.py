import logging
from abc import ABC, abstractmethod

from nga.apps.budgets.domain import BudgetCalculation

logger = logging.getLogger(__name__)


class AbstractBudgetCalculationLogger(ABC):
    @abstractmethod
    def log(self, calculation: BudgetCalculation, msg: str) -> None:
        """Logs messages over budget calculation."""


class PythonBudgetCalculationLogger(AbstractBudgetCalculationLogger):
    def log(self, calculation: BudgetCalculation, msg: str) -> None:
        logger.info(f"[budget-calculation] [budget_id={calculation.budget_id}, calculation_id={calculation.id}] {msg}")
