import itertools
import logging
import traceback
from typing import Optional

from celery import chain, group
from celery.worker.request import Request
from dependency_injector.wiring import Provide, inject
from django.contrib.postgres.aggregates import ArrayAgg
from mediatr import Mediator

from nga.apps.budgets.commands import LogBudgetCalculationCommand
from nga.apps.budgets.infra.orm.models import BudgetCalculation
from nga.apps.calculation.budgets.steps import (
    ApplyAgreementDiscountsStep,
    ApplyExternalDiscountsStep,
    ApplyForecastRulesStep,
    FailBudgetCalculationStep,
    FinishBudgetCalculationStep,
    FullCalculationFinishingTrafficSyncStep,
    FullCalculationTrafficSyncStep,
    OnlyModifiedAgreementsFinishingTrafficSyncStep,
    ResetDiscountStep,
    SingleAgreementFinishingTrafficSyncStep,
    StartBudgetCalculationStep,
    StartFinishingBudgetCalculationStep,
)
from nga.apps.forecasts.infra.orm.models import ForecastRule
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum
from nga.infra.celery import celery_app

logger = logging.getLogger(__name__)


@celery_app.task
def start_calculation_task(calculation_id: int) -> int:

    step = StartBudgetCalculationStep()
    step.execute(int(calculation_id))

    return calculation_id


@celery_app.task
def full_initial_traffic_sync_task(calculation_id: int) -> int:

    step = FullCalculationTrafficSyncStep()
    step.execute(int(calculation_id))

    return calculation_id


@celery_app.task
def reset_traffic_discount_task(calculation_id: int) -> int:

    step = ResetDiscountStep()
    step.execute(int(calculation_id))

    return calculation_id


def create_forecasting_task(calculation_id: int) -> chain:

    calculation = BudgetCalculation.objects.get(id=calculation_id)

    traffic_param_pairs = ForecastRule.objects.filter(budget__id=calculation.budget_snapshot.budget_id).aggregate(
        service_types=ArrayAgg("service_type", distinct=True, default=[]),
        traffic_directions=ArrayAgg("traffic_direction", distinct=True, default=[]),
    )

    directed_service_types = itertools.product(
        traffic_param_pairs["traffic_directions"],
        traffic_param_pairs["service_types"],
    )

    forecasting_group = group(
        *(
            calculate_forecast_rules_task.s(traffic_direction=traffic_direction, service_type=service_type)
            for traffic_direction, service_type in directed_service_types
        )
    )

    return start_forecasting_task.s() | forecasting_group | finish_forecasting_task.si(calculation_id=calculation_id)


@celery_app.task
def start_forecasting_task(calculation_id: int) -> int:
    step = ApplyForecastRulesStep()
    step.start(int(calculation_id))

    return calculation_id


@celery_app.task
def calculate_forecast_rules_task(
    calculation_id: int,
    traffic_direction: int,
    service_type: int,
) -> None:

    step = ApplyForecastRulesStep()
    step.apply_forecast_rules(
        int(calculation_id),
        service_type=ServiceTypeEnum(service_type),
        traffic_direction=TrafficDirectionEnum(traffic_direction),
    )


@celery_app.task
def finish_forecasting_task(calculation_id: int) -> int:
    step = ApplyForecastRulesStep()
    step.finish(int(calculation_id))

    return calculation_id


def create_discounting_task(calculation_id: int) -> chain:
    step = ApplyAgreementDiscountsStep()

    budget_agreements = step.get_budget_agreements(calculation_id)

    apply_discounts_task_group = group(
        *(
            apply_discounts_task.s(budget_agreement_ids=id_batch)
            for id_batch in itertools.batched((ba.id for ba in budget_agreements), 10)
        )
    )

    return start_discounting_task.s() | apply_discounts_task_group | finish_discounting_task.si(calculation_id)


@celery_app.task
def start_discounting_task(calculation_id: int) -> int:
    step = ApplyAgreementDiscountsStep()
    step.start(int(calculation_id))

    return calculation_id


@celery_app.task
def apply_discounts_task(calculation_id: int, budget_agreement_ids: list[int]) -> None:
    step = ApplyAgreementDiscountsStep()
    step.apply_discounts(calculation_id, budget_agreement_ids)


@celery_app.task
def finish_discounting_task(calculation_id: int) -> int:
    step = ApplyAgreementDiscountsStep()
    step.finish(int(calculation_id))

    return calculation_id


@celery_app.task
def start_calculation_finishing_task(calculation_id: int) -> int:
    step = StartFinishingBudgetCalculationStep()
    step.execute(int(calculation_id))

    return calculation_id


@celery_app.task
def full_finishing_traffic_sync_task(calculation_id: int) -> int:

    step = FullCalculationFinishingTrafficSyncStep()
    step.execute(int(calculation_id))

    return calculation_id


@celery_app.task
@inject
def fail_calculation_task(
    request: Request,
    exc: Exception,
    tb: Optional[str],
    calculation_id: int,
    mediator: Mediator = Provide["mediator"],
) -> None:

    fail_step = FailBudgetCalculationStep()
    fail_step.execute(calculation_id)

    log_cmd = LogBudgetCalculationCommand(
        calculation_id=calculation_id,
        message=str(exc),
        traceback=traceback.format_exc(),
    )
    mediator.send(log_cmd)


@celery_app.task
def modified_agreements_finishing_traffic_sync_task(calculation_id: int) -> int:

    step = OnlyModifiedAgreementsFinishingTrafficSyncStep()
    step.execute(int(calculation_id))

    return calculation_id


@celery_app.task
def single_agreement_finishing_traffic_sync_task(calculation_id: int) -> int:

    step = SingleAgreementFinishingTrafficSyncStep()
    step.execute(int(calculation_id))

    return calculation_id


@celery_app.task
def finish_calculation_task(calculation_id: int) -> None:
    step = FinishBudgetCalculationStep()
    step.execute(int(calculation_id))


@celery_app.task
def apply_external_discounts_task(calculation_id: int) -> int:
    step = ApplyExternalDiscountsStep()
    step.execute(int(calculation_id))

    return calculation_id
