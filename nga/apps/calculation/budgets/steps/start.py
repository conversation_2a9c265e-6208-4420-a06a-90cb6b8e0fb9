from dependency_injector.wiring import Provide, inject
from mediatr import Mediator

from nga.apps.budgets.commands import BudgetMessageTypeEnum, UpdateBudgetCalculationStatusCommand
from nga.apps.calculation.budgets.logger import AbstractBudgetCalculationLogger


class StartBudgetCalculationStep:
    """Step is responsible for budget calculation startup."""

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._mediator = mediator
        self._calculation_logger = calculation_logger

    def execute(self, calculation_id: int) -> None:
        command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation_id,
            message_type=BudgetMessageTypeEnum.BUDGET_CALCULATION_STARTED,
        )

        calculation = self._mediator.send(command)

        self._calculation_logger.log(calculation, "Calculation has been started")
