import traceback

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.commands import BudgetMessageTypeEnum, UpdateBudgetCalculationStatusCommand
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.calculation.budgets.logger import AbstractBudgetCalculationLogger
from nga.apps.calculation.budgets.snapshot_traffic_synchronizer import AbstractSnapshotTrafficSynchronizer


class FailBudgetCalculationStep:
    """Step is responsible for budget calculation shutdown after failure during calculation."""

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        snapshot_traffic_synchronizer: AbstractSnapshotTrafficSynchronizer = Closing[
            Provide["snapshot_traffic_synchronizer"]
        ],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._mediator = mediator
        self._budget_repository = budget_repository
        self._snapshot_traffic_synchronizer = snapshot_traffic_synchronizer
        self._calculation_logger = calculation_logger

    def execute(self, calculation_id: int) -> None:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)

        self._calculation_logger.log(calculation, "Calculation is failing")

        budget = self._budget_repository.get_by_id(calculation.budget_id)

        try:
            self._snapshot_traffic_synchronizer.sync_calculation_with_active(budget)
        except Exception:
            traceback.print_exc()
            self._calculation_logger.log(calculation, "Traffic synchronization failed")

        fail_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation.id,
            message_type=BudgetMessageTypeEnum.BUDGET_CALCULATION_FAILED,
        )
        calculation = self._mediator.send(fail_command)

        self._calculation_logger.log(calculation, "Calculation has been failed")
