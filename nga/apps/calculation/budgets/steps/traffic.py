from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.commands import BudgetMessageTypeEnum, UpdateBudgetCalculationStatusCommand
from nga.apps.budgets.domain import BudgetCalculation
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.calculation.budgets.historical_traffic_synchronizer import AbstractHistoricalTrafficSynchronizer
from nga.apps.calculation.budgets.logger import AbstractBudgetCalculationLogger
from nga.core.types import DatePeriod


class FullCalculationTrafficSyncStep:
    """
    Step is responsible for clearing budget (traffic) and filling it from scratch with historical traffic.
    """

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
        historical_traffic_synchronizer: AbstractHistoricalTrafficSynchronizer = Closing[
            Provide["historical_traffic_synchronizer"]
        ],
    ) -> None:
        self._mediator = mediator
        self._budget_repository = budget_repository
        self._historical_traffic_synchronizer = historical_traffic_synchronizer
        self._calculation_logger = calculation_logger

    def execute(self, calculation_id: int) -> None:
        start_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation_id,
            message_type=BudgetMessageTypeEnum.HISTORICAL_TRAFFIC_SYNC_STARTED,
        )
        calculation: BudgetCalculation = self._mediator.send(start_command)

        budget = self._budget_repository.get_by_id(calculation.budget_id)

        fill_period = budget.historical_period

        if calculation.traffic_lhm is not None:
            fill_period = DatePeriod(fill_period.start_date, calculation.traffic_lhm)

        self._historical_traffic_synchronizer.fill_budget(budget, fill_period)

        finish_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation.id,
            message_type=BudgetMessageTypeEnum.HISTORICAL_TRAFFIC_SYNC_FINISHED,
        )
        calculation = self._mediator.send(finish_command)

        self._calculation_logger.log(calculation, "Budget traffic synchronization finished")
