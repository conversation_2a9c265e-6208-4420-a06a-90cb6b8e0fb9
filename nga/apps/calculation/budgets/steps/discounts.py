import traceback
from typing import Sequence

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository, AbstractDiscountRepository
from nga.apps.agreements.enums import AgreementCalculationStatusEnum
from nga.apps.budgets.commands import (
    BudgetMessageTypeEnum,
    LogBudgetCalculationCommand,
    UpdateBudgetCalculationStatusCommand,
)
from nga.apps.budgets.domain import BudgetCalculation
from nga.apps.budgets.domain.models import Budget, BudgetTrafficRecord
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository, AbstractBudgetTrafficRepository
from nga.apps.budgets.enums import BudgetCalculationTypeEnum
from nga.apps.calculation.budgets.logger import AbstractBudgetCalculationLogger
from nga.apps.calculation.discounts import AbstractDiscountCalculationService, DiscountQualifyingServiceProtocol
from nga.apps.calculation.discounts.utils import filter_traffic_by_discount_traffic_parameters
from nga.core.exceptions import BaseNGAException
from nga.utils.dt import get_current_datetime_utc


class DiscountCalculationError(BaseNGAException):
    @classmethod
    def create(cls, budget_agreement_id: int, discount_id: int, msg: str) -> "DiscountCalculationError":
        discount_error_msg = f"Discount [id={discount_id}]: {msg}"

        error_msg = f"BudgetAgreement [id={budget_agreement_id}] calculation failed with {discount_error_msg}."

        return cls(message=error_msg)


class ApplyAgreementDiscountsStep:
    """This step applies agreement discounts to simulation budget traffic."""

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_traffic_repository: AbstractBudgetTrafficRepository = Closing[Provide["budget_traffic_repository"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        discount_repository: AbstractDiscountRepository = Closing[Provide["discount_repository"]],
        discount_calculation_service: AbstractDiscountCalculationService = Closing[
            Provide["discount_calculation_service"],
        ],
        discount_qualifying_service: DiscountQualifyingServiceProtocol = Provide["discount_qualifying_service"],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._mediator = mediator

        self._budget_repository = budget_repository
        self._budget_traffic_repository = budget_traffic_repository
        self._budget_agreement_repository = budget_agreement_repository

        self._discount_repository = discount_repository
        self._discount_calculation_service = discount_calculation_service
        self._discount_qualifying_service = discount_qualifying_service

        self._calculation_logger = calculation_logger

    def start(self, calculation_id: int) -> BudgetCalculation:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)

        self._calculation_logger.log(calculation, "Agreement Discounts application is starting")

        set_start_status_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation.id,
            message_type=BudgetMessageTypeEnum.AGREEMENTS_APPLICATION_STARTED,
        )
        calculation = self._mediator.send(set_start_status_command)

        return calculation

    def finish(self, calculation_id: int) -> BudgetCalculation:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)

        set_finish_status_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation.id,
            message_type=BudgetMessageTypeEnum.AGREEMENTS_APPLICATION_FINISHED,
        )
        calculation = self._mediator.send(set_finish_status_command)

        self._calculation_logger.log(calculation, "Agreement Discounts application finished")

        return calculation

    def apply_discounts(self, calculation_id: int, budget_agreement_ids: list[int]) -> None:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)

        budget_agreements = self._budget_agreement_repository.get_many(budget_agreement_ids=budget_agreement_ids)

        budget = self._budget_repository.get_by_id(calculation.budget_id)

        for budget_agreement in budget_agreements:
            try:
                discounted_traffic = self.apply_agreement_discounts_to_budget_traffic(
                    budget=budget,
                    budget_agreement=budget_agreement,
                    budget_snapshot_id=calculation.budget_snapshot_id,
                )

                budget_agreement.applied_at = get_current_datetime_utc()

                budget_agreement.calculation_status = AgreementCalculationStatusEnum.APPLIED

                self._budget_agreement_repository.save(budget_agreement)

                self._budget_traffic_repository.update_many(discounted_traffic)

            except Exception as e:
                error_message = str(e)

                log_cmd = LogBudgetCalculationCommand(calculation.id, error_message, traceback.format_exc())

                self._mediator.send(log_cmd)

                budget_agreement.calculation_status = AgreementCalculationStatusEnum.FAILED

                if calculation.type == BudgetCalculationTypeEnum.SINGLE_AGREEMENT:
                    self._budget_agreement_repository.update_many((budget_agreement,))
                    raise e

    def get_budget_agreements(self, calculation_id: int) -> Sequence[BudgetAgreement]:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)

        if calculation.is_full:
            budget_agreements = self._budget_agreement_repository.get_many(
                budget_id=calculation.budget_id,
                only_active=True,
            )

        elif calculation.type == BudgetCalculationTypeEnum.ONLY_MODIFIED_AGREEMENTS:
            budget_agreements = self._budget_agreement_repository.get_many(
                budget_id=calculation.budget_id,
                only_active=True,
                only_modified=True,
            )

        else:
            # SINGLE_AGREEMENT
            budget_agreement_id = calculation.get_budget_agreement_id()

            budget_agreement = self._budget_agreement_repository.get_by_id(budget_agreement_id)

            if budget_agreement.is_active is False:
                raise BaseNGAException(f"BudgetAgreement [id={budget_agreement_id}] is not active")

            budget_agreements = (budget_agreement,)

        return budget_agreements

    def apply_agreement_discounts_to_budget_traffic(
        self,
        budget: Budget,
        budget_agreement: BudgetAgreement,
        budget_snapshot_id: int,
    ) -> Sequence[BudgetTrafficRecord]:
        """Applies agreement discounts to provided budget traffic records."""

        discounts = self._discount_repository.get_many(agreement_id=budget_agreement.agreement_id)

        for discount in discounts:
            discount.set_budget(budget)

        budget_agreement_traffic = self._load_traffic(budget_snapshot_id, budget_agreement)

        discounted_traffic_records: list[BudgetTrafficRecord] = []

        for discount in discounts:

            if discount.needs_to_be_qualified:
                discount, result = self._discount_qualifying_service.qualify(discount, budget_agreement_traffic)

                if result.succeeded is False:
                    continue

            _discount_traffic = filter_traffic_by_discount_traffic_parameters(discount, budget_agreement_traffic)

            try:
                _discounted_traffic = self._discount_calculation_service.apply_discount_to_traffic(
                    discount=discount,
                    budget_snapshot_id=budget_snapshot_id,
                    traffic_records=_discount_traffic,
                )
            except Exception as e:
                raise DiscountCalculationError.create(budget_agreement.id, discount.id, msg=str(e))

            discounted_traffic_records.extend(_discounted_traffic)

        return discounted_traffic_records

    def _load_traffic(
        self,
        budget_snapshot_id: int,
        budget_agreement: BudgetAgreement,
    ) -> tuple[BudgetTrafficRecord, ...]:

        budget_agreement_traffic = self._budget_traffic_repository.get_many(
            snapshot_id=budget_snapshot_id,
            home_operators=budget_agreement.home_operators,
            partner_operators=budget_agreement.partner_operators,
            traffic_months=list(budget_agreement.period),
            include_satellite=budget_agreement.include_satellite,
        )

        return tuple(budget_agreement_traffic)
