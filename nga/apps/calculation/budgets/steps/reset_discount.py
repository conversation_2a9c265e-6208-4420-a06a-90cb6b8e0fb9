from typing import cast

from dependency_injector.wiring import Closing, Provide, inject

from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository, AbstractBudgetTrafficRepository
from nga.apps.budgets.enums import BudgetCalculationTypeEnum
from nga.apps.calculation.budgets.logger import AbstractBudgetCalculationLogger


class ResetDiscountStep:
    """
    This step is needed when calculation is run without historical traffic synchronization. Calculation is performed on
    traffic that is a result of previous calculation and it can have applied discounts.
    The goal of this step is to reset charges that have been applied during previous calculations if calculation is run
    without full traffic synchronization.
    """

    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_traffic_repository: AbstractBudgetTrafficRepository = Closing[Provide["budget_traffic_repository"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._budget_repository = budget_repository

        self._budget_traffic_repository = budget_traffic_repository

        self._budget_agreement_repository = budget_agreement_repository

        self._calculation_logger = calculation_logger

    def execute(self, calculation_id: int) -> None:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)

        self._calculation_logger.log(calculation, "Reset discount step started")

        match calculation.type:
            case BudgetCalculationTypeEnum.SINGLE_AGREEMENT:
                budget_agreement_id = cast(int, calculation.budget_agreement_id)

                budget_agreement = self._budget_agreement_repository.get_by_id(budget_agreement_id)

                self._budget_traffic_repository.reset_discount(
                    calculation.budget_snapshot_id,
                    home_operators=budget_agreement.home_operators,
                    partner_operators=budget_agreement.partner_operators,
                    period=budget_agreement.period,
                )

            case BudgetCalculationTypeEnum.ONLY_MODIFIED_AGREEMENTS:
                budget_agreements = self._budget_agreement_repository.get_many(
                    budget_id=calculation.budget_id,
                    only_active=True,
                    only_modified=True,
                )

                for budget_agreement in budget_agreements:
                    self._budget_traffic_repository.reset_discount(
                        calculation.budget_snapshot_id,
                        home_operators=budget_agreement.home_operators,
                        partner_operators=budget_agreement.partner_operators,
                        period=budget_agreement.period,
                    )

            case _:
                self._budget_traffic_repository.reset_discount(calculation.budget_snapshot_id)

        self._calculation_logger.log(calculation, "Reset discount step finished")
