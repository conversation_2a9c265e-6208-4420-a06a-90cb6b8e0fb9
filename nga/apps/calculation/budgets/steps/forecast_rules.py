from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.commands import (
    BudgetMessageTypeEnum,
    LogBudgetCalculationCommand,
    UpdateBudgetCalculationStatusCommand,
)
from nga.apps.budgets.domain import BudgetCalculation
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository, AbstractBudgetTrafficRepository
from nga.apps.budgets.enums import BudgetCalculationTypeEnum
from nga.apps.calculation.budgets.logger import AbstractBudgetCalculationLogger
from nga.apps.calculation.forecasting.context import ForecastingContext
from nga.apps.calculation.forecasting.exceptions import ForecastRuleIsNotCalculable
from nga.apps.calculation.forecasting.services import AbstractForecastingService
from nga.apps.forecasts.domain import AbstractForecastRuleRepository
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum, TrafficTypeEnum


class ApplyForecastRulesStep:
    """Step is responsible for creating new budget traffic based on Forecast Rules."""

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_traffic_repository: AbstractBudgetTrafficRepository = Closing[Provide["budget_traffic_repository"]],
        forecast_rule_repository: AbstractForecastRuleRepository = Closing[Provide["forecast_rule_repository"]],
        forecasting_service: AbstractForecastingService = Closing[Provide["forecasting_service"]],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._mediator = mediator

        self._budget_repository = budget_repository
        self._budget_traffic_repository = budget_traffic_repository

        self._forecast_rule_repository = forecast_rule_repository
        self._forecasting_service = forecasting_service

        self._calculation_logger = calculation_logger

    def start(self, calculation_id: int) -> None:
        start_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation_id,
            message_type=BudgetMessageTypeEnum.FORECAST_RULES_APPLICATION_STARTED,
        )
        calculation = self._mediator.send(start_command)

        self._calculation_logger.log(calculation, "Forecast rules application started")

        if calculation.type == BudgetCalculationTypeEnum.FULL_WITHOUT_TRAFFIC_UPDATE:
            self.clear_budget_traffic_for_full_without_update(calculation)

    def finish(self, calculation_id: int) -> None:
        finish_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation_id,
            message_type=BudgetMessageTypeEnum.FORECAST_RULES_APPLICATION_FINISHED,
        )
        calculation = self._mediator.send(finish_command)

        self._calculation_logger.log(calculation, "Forecast rules application finished")

    def clear_budget_traffic_for_full_without_update(self, calculation: BudgetCalculation) -> None:
        self._budget_traffic_repository.delete_many(
            calculation.budget_snapshot_id,
            traffic_type=TrafficTypeEnum.FORECASTED,
        )

        self._calculation_logger.log(calculation, "Forecasted traffic has been cleared")

    def apply_forecast_rules(
        self,
        calculation_id: int,
        service_type: ServiceTypeEnum,
        traffic_direction: TrafficDirectionEnum,
    ) -> None:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)
        budget = self._budget_repository.get_by_id(calculation.budget_id)

        forecast_rules = tuple(
            self._forecast_rule_repository.get_many(
                budget.id,
                service_type=service_type,
                traffic_direction=traffic_direction,
                sort_by_period=True,
            )
        )

        context = ForecastingContext(budget=budget, budget_calculation=calculation)

        try:
            self._forecasting_service.evaluate_rules(forecast_rules, context)

        except ForecastRuleIsNotCalculable as e:
            self._mediator.send(LogBudgetCalculationCommand(calculation.id, e.message, traceback=None))
