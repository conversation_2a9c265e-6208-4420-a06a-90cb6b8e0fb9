from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.commands import BudgetMessageTypeEnum, UpdateBudgetCalculationStatusCommand
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.calculation.budgets.logger import AbstractBudgetCalculationLogger


class StartFinishingBudgetCalculationStep:
    """Step is responsible for budget calculation shutdown."""

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._mediator = mediator
        self._budget_repository = budget_repository
        self._calculation_logger = calculation_logger

    def execute(self, calculation_id: int) -> None:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)

        self._calculation_logger.log(calculation, "Finishing calculation process")

        start_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation.id,
            message_type=BudgetMessageTypeEnum.BUDGET_CALCULATION_IS_FINISHING,
        )

        self._mediator.send(start_command)


class FinishBudgetCalculationStep:
    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._mediator = mediator
        self._budget_repository = budget_repository
        self._calculation_logger = calculation_logger

    def execute(self, calculation_id: int) -> None:
        finish_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation_id,
            message_type=BudgetMessageTypeEnum.BUDGET_CALCULATION_FINISHED,
        )
        calculation = self._mediator.send(finish_command)

        self._calculation_logger.log(calculation, "Calculation has been finished")
