from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.commands import BudgetMessageTypeEnum, UpdateBudgetCalculationStatusCommand
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.calculation.budgets.logger import AbstractBudgetCalculationLogger
from nga.apps.calculation.discounts import AbstractExternalDiscountCalculationService


class ApplyExternalDiscountsStep:
    """Applies discounts to Master budget traffic from external systems."""

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        external_discount_calculation_service: AbstractExternalDiscountCalculationService = Closing[
            Provide["external_discount_calculation_service"]
        ],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._mediator = mediator
        self._budget_repository = budget_repository
        self._external_discount_calculation_service = external_discount_calculation_service
        self._calculation_logger = calculation_logger

    def execute(self, calculation_id: int) -> None:
        start_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation_id,
            message_type=BudgetMessageTypeEnum.MASTER_BUDGET_COMPONENTS_CALCULATION_STARTED,
        )
        calculation = self._mediator.send(start_command)

        budget = self._budget_repository.get_by_id(calculation.budget_id)

        self._external_discount_calculation_service.apply_discounts(budget)

        finish_command = UpdateBudgetCalculationStatusCommand(
            calculation_id=calculation.id,
            message_type=BudgetMessageTypeEnum.MASTER_BUDGET_COMPONENTS_CALCULATION_FINISHED,
        )
        calculation = self._mediator.send(finish_command)

        self._calculation_logger.log(calculation, "Calculation based on external traffic finished")
