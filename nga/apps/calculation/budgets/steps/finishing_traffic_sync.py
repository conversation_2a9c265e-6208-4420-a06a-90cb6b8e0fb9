from dependency_injector.wiring import Closing, Provide, inject

from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.providers.budget_agreement import AbstractBudgetAgreementProvider
from nga.apps.calculation.budgets.logger import AbstractBudgetCalculationLogger
from nga.apps.calculation.budgets.snapshot_traffic_synchronizer import AbstractSnapshotTrafficSynchronizer


class FullCalculationFinishingTrafficSyncStep:
    """
    Step is responsible for synchronizing CALCULATION snapshot with ACTIVE. All records that has been modified/created
    in CALCULATION snapshot will be reflected in ACTIVE.
    """

    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        snapshot_traffic_synchronizer: AbstractSnapshotTrafficSynchronizer = Closing[
            Provide["snapshot_traffic_synchronizer"]
        ],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._budget_repository = budget_repository
        self._snapshot_traffic_synchronizer = snapshot_traffic_synchronizer
        self._calculation_logger = calculation_logger

    def execute(self, calculation_id: int) -> None:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)

        budget = self._budget_repository.get_by_id(calculation.budget_id)

        self._snapshot_traffic_synchronizer.sync_active_with_calculation(budget)


class OnlyModifiedAgreementsFinishingTrafficSyncStep:
    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_agreement_provider: AbstractBudgetAgreementProvider = Closing[Provide["budget_agreement_provider"]],
        snapshot_traffic_synchronizer: AbstractSnapshotTrafficSynchronizer = Closing[
            Provide["snapshot_traffic_synchronizer"]
        ],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._budget_repository = budget_repository
        self._budget_agreement_provider = budget_agreement_provider
        self._snapshot_traffic_synchronizer = snapshot_traffic_synchronizer
        self._calculation_logger = calculation_logger

    def execute(self, calculation_id: int) -> None:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)

        budget = self._budget_repository.get_by_id(calculation.budget_id)

        budget_agreements = self._budget_agreement_provider.get_many(budget_id=budget.id)

        self._snapshot_traffic_synchronizer.sync_agreements_traffic_with_active(budget, budget_agreements)


class SingleAgreementFinishingTrafficSyncStep:
    @inject
    def __init__(
        self,
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
        budget_agreement_provider: AbstractBudgetAgreementProvider = Closing[Provide["budget_agreement_provider"]],
        snapshot_traffic_synchronizer: AbstractSnapshotTrafficSynchronizer = Closing[
            Provide["snapshot_traffic_synchronizer"]
        ],
        calculation_logger: AbstractBudgetCalculationLogger = Provide["budget_calculation_logger"],
    ) -> None:
        self._budget_repository = budget_repository
        self._budget_agreement_provider = budget_agreement_provider
        self._snapshot_traffic_synchronizer = snapshot_traffic_synchronizer
        self._calculation_logger = calculation_logger

    def execute(self, calculation_id: int) -> None:
        calculation = self._budget_repository.get_calculation_by_id(calculation_id)

        budget = self._budget_repository.get_by_id(calculation.budget_id)

        budget_agreement = self._budget_agreement_provider.get_by_id(calculation.get_budget_agreement_id())

        self._snapshot_traffic_synchronizer.sync_agreements_traffic_with_active(budget, [budget_agreement])
