from dataclasses import dataclass
from typing import Optional

from nga.apps.budgets.enums import BudgetCalculationTypeEnum
from nga.core.types import Month


@dataclass
class BudgetCalculationOptions:
    calculation_type: BudgetCalculationTypeEnum

    budget_agreement_id: Optional[int]

    user_id: Optional[int]

    budget_lhm: Month

    traffic_lhm: Optional[Month]

    distribution_lhm: Optional[Month]
