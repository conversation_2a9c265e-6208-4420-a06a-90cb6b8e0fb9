import logging
from abc import ABC, abstractmethod
from typing import Sequence

from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.budgets.domain import Budget
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository, AbstractBudgetTrafficRepository


class AbstractSnapshotTrafficSynchronizer(ABC):
    @abstractmethod
    def sync_active_with_calculation(self, budget: Budget) -> None:
        """
        Synchronizes active snapshot with calculation snapshot. During synchronization sets calculation snapshot as
        budget active snapshot. After sync is finished sets updated active snapshot as budget's active snapshot.
        """

    @abstractmethod
    def sync_calculation_with_active(self, budget: Budget) -> None:
        """Synchronizes calculation snapshot with active snapshot."""

    @abstractmethod
    def sync_agreements_traffic_with_active(
        self,
        budget: Budget,
        budget_agreements: Sequence[BudgetAgreement],
    ) -> None:
        """Synchronizes active snapshot with traffic covered by provided agreement from calculation snapshot."""


logger = logging.getLogger(__name__)


class SnapshotTrafficSynchronizer(AbstractSnapshotTrafficSynchronizer):
    _log_tag = "[budget-traffic-sync]"

    def __init__(
        self,
        budget_traffic_repository: AbstractBudgetTrafficRepository,
        budget_repository: AbstractBudgetRepository,
    ) -> None:
        """Init service dependencies."""

        self._budget_traffic_repository = budget_traffic_repository
        self._budget_repository = budget_repository

    def sync_snapshots(self, source_snapshot_id: int, target_snapshot_id: int) -> None:
        """Copies traffic between snapshots."""

        log_msg_fmt = f"{self._log_tag} [source_snapshot={source_snapshot_id}, target_snapshot={target_snapshot_id}]"

        logger.info(f"{log_msg_fmt} Snapshots sync started")

        self._budget_traffic_repository.delete_many(target_snapshot_id)
        logger.info(f"{log_msg_fmt} Target snapshot records removed")

        self._budget_traffic_repository.copy_many(source_snapshot_id, target_snapshot_id)
        logger.info(f"{log_msg_fmt} Source records are copied to target snapshot")

        logger.info(f"{log_msg_fmt} Snapshots sync finished")

    def sync_active_with_calculation(self, budget: Budget) -> None:
        """
        Synchronizes active snapshot with calculation snapshot. During synchronization sets calculation snapshot as
        budget active snapshot. After sync is finished sets updated active snapshot as budget's active snapshot.
        """

        active_snapshot_id = budget.active_snapshot_id
        budget.active_snapshot_id = budget.calculation_snapshot_id
        self._budget_repository.save(budget)

        log_fmt = f"{self._log_tag} [budget={budget.id}]"

        logger.info(f"{log_fmt} Replaced budget active snapshot with calculation")

        self.sync_snapshots(source_snapshot_id=budget.calculation_snapshot_id, target_snapshot_id=active_snapshot_id)

        budget.active_snapshot_id = active_snapshot_id
        self._budget_repository.save(budget)

        logger.info(f"{log_fmt} Set synchronized snapshot as active")

    def sync_calculation_with_active(self, budget: Budget) -> None:
        """Synchronizes calculation snapshot with active snapshot."""

        self.sync_snapshots(
            source_snapshot_id=budget.active_snapshot_id,
            target_snapshot_id=budget.calculation_snapshot_id,
        )

    def sync_agreements_traffic_with_active(
        self,
        budget: Budget,
        budget_agreements: Sequence[BudgetAgreement],
    ) -> None:
        """Synchronizes active snapshot with traffic covered by provided agreements from calculation snapshot."""

        active_snapshot_id = budget.active_snapshot_id
        budget.active_snapshot_id = budget.calculation_snapshot_id

        self._budget_repository.save(budget)

        log_fmt = f"{self._log_tag} [budget={budget.id}]"

        logger.info(f"{log_fmt} Replaced budget active snapshot with calculation")

        for budget_agreement in budget_agreements:
            self._budget_traffic_repository.delete_many(
                budget_snapshot_id=active_snapshot_id,
                home_operators=budget_agreement.home_operators,
                partner_operators=budget_agreement.partner_operators,
                traffic_months=list(budget_agreement.period),
            )

            self._budget_traffic_repository.copy_many(
                source_snapshot_id=budget.calculation_snapshot_id,
                target_snapshot_id=active_snapshot_id,
                home_operators=budget_agreement.home_operators,
                partner_operators=budget_agreement.partner_operators,
                traffic_months=list(budget_agreement.period),
            )

        logger.info(f"{log_fmt} Agreements traffic has been copied to active snapshot")

        budget.active_snapshot_id = active_snapshot_id

        self._budget_repository.save(budget)

        logger.info(f"{log_fmt} Set synchronized snapshot as active")
