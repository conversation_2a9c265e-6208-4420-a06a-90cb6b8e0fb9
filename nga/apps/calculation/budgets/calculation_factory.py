from nga.apps.budgets.domain import Budget, BudgetCalculation
from nga.apps.budgets.domain.exceptions import BudgetCalculationError
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.enums import BudgetCalculationTypeEnum
from nga.apps.calculation.budgets.dto import BudgetCalculationOptions


class BudgetCalculationFactory:
    def __init__(self, budget_repository: AbstractBudgetRepository) -> None:
        self._budget_repository = budget_repository

    def create(self, budget: Budget, options: BudgetCalculationOptions) -> BudgetCalculation:
        if budget.is_master and options.calculation_type != BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE:
            raise BudgetCalculationError("Master calculation must be only FULL.")

        if not budget.is_master and options.calculation_type == BudgetCalculationTypeEnum.SINGLE_AGREEMENT:
            if options.budget_agreement_id is None:
                raise BudgetCalculationError(
                    "`budget_agreement_id` must be provided for single agreement calculation type."
                )

        calculation = self._budget_repository.create_calculation(
            budget_id=budget.id,
            calculation_type=options.calculation_type,
            created_by_user_id=options.user_id,
            budget_agreement_id=options.budget_agreement_id,
            budget_lhm=options.budget_lhm,
            traffic_lhm=options.traffic_lhm if options.traffic_lhm else options.budget_lhm,
            distribution_lhm=options.distribution_lhm if options.distribution_lhm else options.budget_lhm,
        )

        return calculation
