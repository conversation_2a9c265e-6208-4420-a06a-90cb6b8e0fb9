from celery import Task, chain
from kombu.exceptions import OperationalError

from nga.apps.budgets.domain import BudgetCalculation
from nga.apps.budgets.domain.exceptions import BudgetCalculationError
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.enums import BudgetCalculationTypeEnum, BudgetTypeEnum
from nga.apps.calculation.budgets.runner import AbstractBudgetCalculationRunner
from nga.apps.calculation.budgets.tasks import (
    apply_external_discounts_task,
    create_discounting_task,
    create_forecasting_task,
    fail_calculation_task,
    finish_calculation_task,
    full_finishing_traffic_sync_task,
    full_initial_traffic_sync_task,
    modified_agreements_finishing_traffic_sync_task,
    reset_traffic_discount_task,
    single_agreement_finishing_traffic_sync_task,
    start_calculation_finishing_task,
    start_calculation_task,
)


class CeleryBudgetCalculationRunner(AbstractBudgetCalculationRunner):
    def __init__(self, budget_repository: AbstractBudgetRepository) -> None:
        self._budget_repository = budget_repository

    def run(self, calculation: BudgetCalculation) -> BudgetCalculation:
        try:
            calculation_workflow = self.build_calculation_workflow(calculation)
            calculation_workflow.delay(calculation.id)

        except OperationalError as e:
            raise BudgetCalculationError(f"Unable to start calculation. System error. {str(e)}")

        return calculation

    def build_calculation_workflow(self, calculation: BudgetCalculation) -> chain:
        calculation_flow = (
            start_calculation_task.s()
            | self.get_prepare_traffic_task(calculation).s()
            | self._build_budget_components_flow(calculation)
            | start_calculation_finishing_task.s()
            | self.get_finishing_traffic_sync_task(calculation).s()
            | finish_calculation_task.s()
        )

        calculation_flow = calculation_flow.on_error(fail_calculation_task.s(calculation.id))

        return calculation_flow

    @classmethod
    def get_prepare_traffic_task(cls, calculation: BudgetCalculation) -> Task:
        if calculation.type == BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE:
            prepare_traffic_task = full_initial_traffic_sync_task
        else:
            prepare_traffic_task = reset_traffic_discount_task

        return prepare_traffic_task

    def _build_budget_components_flow(self, calculation: BudgetCalculation) -> chain:

        budget = self._budget_repository.get_by_id(calculation.budget_id)

        workflow = chain()

        if budget.type == BudgetTypeEnum.MASTER:
            workflow |= apply_external_discounts_task.s()

        else:
            if calculation.is_full:
                workflow |= create_forecasting_task(calculation.id)

            workflow |= create_discounting_task(calculation.id)

        return workflow

    @classmethod
    def get_finishing_traffic_sync_task(cls, calculation: BudgetCalculation) -> Task:
        match calculation.type:
            case BudgetCalculationTypeEnum.ONLY_MODIFIED_AGREEMENTS:
                return modified_agreements_finishing_traffic_sync_task

            case BudgetCalculationTypeEnum.SINGLE_AGREEMENT:
                return single_agreement_finishing_traffic_sync_task

            case _:
                return full_finishing_traffic_sync_task
