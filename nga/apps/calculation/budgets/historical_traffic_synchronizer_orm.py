import logging

from django.db import connection

from nga.apps.budgets.domain import Budget
from nga.apps.budgets.domain.repositories import AbstractBudgetTrafficRepository
from nga.apps.budgets.infra.orm.models import BudgetTrafficRecord
from nga.apps.calculation.budgets.historical_traffic_synchronizer import AbstractHistoricalTrafficSynchronizer
from nga.apps.references.infra.orm.models import IMSICountRecord, MonthlyAggregatedTrafficRecord
from nga.core.enums import IMSICountTypeEnum, ServiceTypeEnum, TrafficTypeEnum
from nga.core.types import DatePeriod

logger = logging.getLogger(__name__)


class HistoricalTrafficORMSynchronizer(AbstractHistoricalTrafficSynchronizer):
    _log_tag = "[budget-historical-traffic-sync]"

    def __init__(self, budget_traffic_repository: AbstractBudgetTrafficRepository) -> None:
        """Init service dependencies."""

        self._budget_traffic_repository = budget_traffic_repository

    def fill_budget(self, budget: Budget, period: DatePeriod) -> None:
        """Synchronizes historical traffic records from monthly aggregations with budget calculation snapshot."""

        log_msg_fmt = f"{self._log_tag} [budget_id={budget.id}]"

        logger.info(f"{log_msg_fmt} Synchronization has been started")

        self._budget_traffic_repository.delete_many(budget_snapshot_id=budget.calculation_snapshot_id)

        self._fill_budget_with_historical_traffic(budget, period)
        self._fill_budget_with_imsi_count(budget, period)

        logger.info(f"{log_msg_fmt} Synchronization has been finished")

    @classmethod
    def _fill_budget_with_historical_traffic(cls, budget: Budget, period: DatePeriod) -> None:
        target_model_meta = BudgetTrafficRecord._meta
        source_model_meta = MonthlyAggregatedTrafficRecord._meta

        manual_fields = {"id", "created_at", "updated_at", "budget_snapshot_id"}

        fields = set(f.get_attname_column()[1] for f in source_model_meta.fields) - manual_fields
        str_fields = ",".join(fields)

        query = f"""
        INSERT INTO {target_model_meta.db_table} (
            budget_snapshot_id,
            created_at,
            updated_at,
            {str_fields}
        )
        SELECT
            %(target_snapshot_id)s as budget_snapshot_id,
            NOW() AT TIME ZONE ('utc'),
            NOW() AT TIME ZONE ('utc'),
            {str_fields}
        FROM {source_model_meta.db_table}
        WHERE home_operator_id = ANY(%(home_operators)s)
            AND traffic_month BETWEEN %(start_date)s AND %(end_date)s
        """

        query_arguments = {
            "target_snapshot_id": budget.calculation_snapshot_id,
            "home_operators": list(budget.home_operators),
            "start_date": period.start_date,
            "end_date": period.end_date,
        }

        with connection.cursor() as cursor:
            cursor.execute(query, query_arguments)

    @classmethod
    def _fill_budget_with_imsi_count(cls, budget: Budget, period: DatePeriod) -> None:
        target_model_meta = BudgetTrafficRecord._meta
        source_model_meta = IMSICountRecord._meta

        # As IMSI count record contains two count fields (count and data_count) that record must be split onto two
        #  Budget traffic records. One record with NO_DATA IMSI count type with volumes calculated by
        #  [count - data_count] formula. And the second record with DATA IMSI count type with volumes equal to
        #  [data_count] value

        # This query creates NO_DATA IMSI count type Budget traffic records
        no_data_query = f"""
        INSERT INTO {target_model_meta.db_table} (
            home_operator_id,
            partner_operator_id,
            traffic_month,
            traffic_direction,
            traffic_segment_id,
            budget_snapshot_id,
            created_at,
            updated_at,
            traffic_type,
            service_type,
            imsi_count_type,
            volume_actual,
            volume_billed,
            tap_charge_net,
            tap_charge_gross,
            charge_net,
            charge_gross
        )
        SELECT
            home_operator_id,
            partner_operator_id,
            traffic_month,
            traffic_direction,
            traffic_segment_id,
            %(target_snapshot_id)s as budget_snapshot_id,
            NOW() AT TIME ZONE ('utc'),
            NOW() AT TIME ZONE ('utc'),
            %(traffic_type)s as traffic_type,
            %(service_type)s as service_type,
            {IMSICountTypeEnum.NO_DATA.value} as imsi_count_type,
            count - data_count as volume_actual,
            count - data_count as volume_billed,
            0 as tap_charge_net,
            0 as tap_charge_gross,
            0 as charge_net,
            0 as charge_gross
        FROM {source_model_meta.db_table}
        WHERE home_operator_id = ANY(%(home_operators)s)
            AND traffic_month BETWEEN %(start_date)s AND %(end_date)s
        """

        # This query creates DATA IMSI count type Budget traffic records. We create DATA IMSI count type records only
        #  for IMSI records that have data_count > 0, in order not to create many zero volume budget traffic records
        data_query = f"""
        INSERT INTO {target_model_meta.db_table} (
            home_operator_id,
            partner_operator_id,
            traffic_month,
            traffic_direction,
            traffic_segment_id,
            budget_snapshot_id,
            created_at,
            updated_at,
            traffic_type,
            service_type,
            imsi_count_type,
            volume_actual,
            volume_billed,
            tap_charge_net,
            tap_charge_gross,
            charge_net,
            charge_gross
        )
        SELECT
            home_operator_id,
            partner_operator_id,
            traffic_month,
            traffic_direction,
            traffic_segment_id,
            %(target_snapshot_id)s as budget_snapshot_id,
            NOW() AT TIME ZONE ('utc'),
            NOW() AT TIME ZONE ('utc'),
            %(traffic_type)s as traffic_type,
            %(service_type)s as service_type,
            {IMSICountTypeEnum.DATA.value} as imsi_count_type,
            data_count as volume_actual,
            data_count as volume_billed,
            0 as tap_charge_net,
            0 as tap_charge_gross,
            0 as charge_net,
            0 as charge_gross
        FROM {source_model_meta.db_table}
        WHERE home_operator_id = ANY(%(home_operators)s)
            AND traffic_month BETWEEN %(start_date)s AND %(end_date)s
            AND data_count > 0
        """

        query_arguments = {
            "target_snapshot_id": budget.calculation_snapshot_id,
            "traffic_type": TrafficTypeEnum.HISTORICAL,
            "service_type": ServiceTypeEnum.ACCESS_FEE,
            "home_operators": list(budget.home_operators),
            "start_date": period.start_date,
            "end_date": period.end_date,
        }

        with connection.cursor() as cursor:
            cursor.execute(no_data_query, query_arguments)
            cursor.execute(data_query, query_arguments)
