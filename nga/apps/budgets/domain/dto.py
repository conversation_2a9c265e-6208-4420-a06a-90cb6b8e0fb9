from dataclasses import dataclass
from datetime import date, datetime
from typing import Collection, Optional, Sequence

from nga.apps.references.value_objects import TrafficRecord
from nga.core.enums import (
    CallDestinationEnum,
    IMSICountTypeEnum,
    ServiceTypeEnum,
    TrafficDirectionEnum,
    TrafficTypeEnum,
)
from nga.core.types import DatePeriod, Month


@dataclass(kw_only=True)
class BudgetParametersFilters:
    home_operators: Optional[Sequence[int]] = None
    partner_operators: Optional[Sequence[int]] = None
    partner_countries: Optional[Sequence[int]] = None

    period: Optional[DatePeriod] = None


@dataclass
class BudgetTrafficParameters:
    snapshot_id: int

    home_operators: Optional[Collection[int]] = None
    partner_operators: Optional[Collection[int]] = None
    partner_countries: Optional[Collection[int]] = None

    period: Optional[DatePeriod] = None

    traffic_types: Optional[Collection[TrafficTypeEnum]] = None
    traffic_months: Optional[Collection[date]] = None
    traffic_directions: Optional[Collection[TrafficDirectionEnum]] = None
    traffic_segments: Optional[Collection[int]] = None

    imsi_count_types: Optional[Collection[IMSICountTypeEnum]] = None

    service_types: Optional[Collection[ServiceTypeEnum]] = None

    call_destinations: Optional[Collection[CallDestinationEnum]] = None
    called_countries: Optional[Collection[int]] = None

    premium: Optional[Collection[Optional[bool]]] = None

    include_forecasted: Optional[bool] = None
    forecast_rule_ids: Optional[Collection[int]] = None

    discount_ids: Optional[Collection[int]] = None


@dataclass(kw_only=True)
class BudgetTrafficRecordDTO(TrafficRecord):
    budget_snapshot_id: int

    forecast_rule_id: Optional[int]

    discount_id: Optional[int]

    imsi_count_type: Optional[IMSICountTypeEnum]


@dataclass
class BudgetComponentsQuantity:
    budget_id: int
    agreements: int
    active_agreements: int
    forecast_rules: int
    iot_rates: int


@dataclass
class BudgetComponentsState:
    budget_id: int

    historical_traffic_last_modified_at: Optional[datetime]
    forecast_rules_last_modified_at: Optional[datetime]

    budget_traffic_synchronized_at: Optional[datetime]
    forecast_rules_applied_at: Optional[datetime]

    agreements_last_modified_at: Optional[datetime]

    budget_lhm: Optional[Month]
    historical_traffic_lhm: Optional[Month]
    forecasted_traffic_lhm: Optional[Month]
    distribution_lhm: Optional[Month]
