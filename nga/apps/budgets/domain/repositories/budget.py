from abc import ABC, abstractmethod
from datetime import date
from typing import Optional, Sequence

from nga.apps.budgets.domain import Budget, BudgetCalculation
from nga.apps.budgets.enums import BudgetCalculationTypeEnum, BudgetTypeEnum
from nga.core.types import DatePeriod, Month


class AbstractBudgetRepository(ABC):
    @abstractmethod
    def get_by_id(self, budget_id: int) -> Budget:
        """Returns budget by provided ID."""

    @abstractmethod
    def get_by_snapshot_id(self, budget_snapshot_id: int) -> Budget:
        """Returns budget by provided snapshot ID."""

    @abstractmethod
    def get_many(
        self,
        home_operators: Optional[Sequence[int]] = None,
        month_included_in_period: Optional[date] = None,
    ) -> tuple[Budget, ...]:
        """Returns collection of Budgets objects."""

    @abstractmethod
    def get_master(self) -> Budget:
        """Returns master budget."""

    @abstractmethod
    def create(
        self,
        name: str,
        period: DatePeriod,
        type: BudgetTypeEnum,
        home_operators: list[int],
        last_historical_month: Month,
        *,
        user_id: Optional[int],
        description: Optional[str],
    ) -> Budget:
        """Creates and persists Budget instance with provided parameters."""

    @abstractmethod
    def save(self, budget: Budget) -> Budget:
        """Persists budget instance to storage."""

    @abstractmethod
    def get_last_calculation(self, budget_id: int) -> BudgetCalculation:
        """Returns last budget calculation record."""

    @abstractmethod
    def get_last_agreement_calculation(self, budget_id: int, budget_agreement_id: int) -> BudgetCalculation:
        """Returns last budget SINGLE_AGREEMENT calculation for provided budget_agreement_id."""

    @abstractmethod
    def get_last_full_calculation(self, budget_id: int) -> BudgetCalculation:
        """Return last budget calculation with any FULL calculation type."""

    @abstractmethod
    def get_last_full_traffic_update_calculation(self, budget_id: int) -> BudgetCalculation:
        """Return last budget calculation with FULL_TRAFFIC_UPDATE calculation type."""

    @abstractmethod
    def create_calculation(
        self,
        budget_id: int,
        calculation_type: BudgetCalculationTypeEnum,
        created_by_user_id: Optional[int],
        budget_agreement_id: Optional[int],
        budget_lhm: Month,
        traffic_lhm: Optional[Month],
        distribution_lhm: Optional[Month],
    ) -> BudgetCalculation:
        """Creates budget calculation record."""

    @abstractmethod
    def save_calculation(self, calculation: BudgetCalculation) -> BudgetCalculation:
        """Persists calculation instance to storage."""

    @abstractmethod
    def get_calculation_by_id(self, calculation_id: int) -> BudgetCalculation:
        """Returns budget calculation instance by provided ID."""

    @abstractmethod
    def set_for_delete(self, budget_id: int) -> None:
        """Marks budget that it is going to be removed."""

    @abstractmethod
    def delete_by_id(self, budget_id: int) -> None:
        """Removes budget and its components from the storage."""
