from dataclasses import dataclass, field
from datetime import date, datetime
from enum import StrEnum
from typing import Optional, Sequence

from mediatr.mediator import GenericQuery

from nga.apps.budgets.domain import Budget
from nga.apps.budgets.enums import BudgetCalculationTypeEnum, BudgetTypeEnum
from nga.core.types import DatePeriod, Month
from nga.utils.dt import get_current_datetime_utc


@dataclass
class LogBudgetCalculationCommand(GenericQuery):
    calculation_id: int
    message: str
    traceback: Optional[str]


@dataclass
class DeleteBudgetCommitmentTrafficCommand(GenericQuery):
    budget_id: int

    discount_ids: Sequence[int]


@dataclass
class ResetBudgetTrafficDiscountCommand(GenericQuery):
    budget_id: int

    home_operators: Sequence[int]
    partner_operators: Sequence[int]
    period: DatePeriod


@dataclass
class UpdateBudgetAfterForecastRulesModifiedCommand(GenericQuery):
    budget_id: int
    updated_at: datetime = field(default_factory=get_current_datetime_utc)


@dataclass
class UpdateBudgetAfterImportMonthlyTrafficRecordsCommand(GenericQuery):
    budget_id: int
    updated_at: datetime = field(default_factory=get_current_datetime_utc)


@dataclass
class UpdateBudgetAfterAgreementsModifiedCommand(GenericQuery):
    budget_id: int
    updated_at: datetime = field(default_factory=get_current_datetime_utc)


@dataclass
class UpdateBudgetCalculationStatusCommand(GenericQuery):
    calculation_id: int

    message_type: "BudgetMessageTypeEnum"

    dt: datetime = field(default_factory=get_current_datetime_utc)


@dataclass
class NotifyBudgetChannelCommand(GenericQuery):
    budget_id: int
    message_type: "BudgetMessageTypeEnum"
    dt: datetime


class BudgetMessageTypeEnum(StrEnum):
    BUDGET_CALCULATION_STARTED = "BUDGET_CALCULATION_STARTED"
    BUDGET_CALCULATION_IS_FINISHING = "BUDGET_CALCULATION_IS_FINISHING"
    BUDGET_CALCULATION_FINISHED = "BUDGET_CALCULATION_FINISHED"
    BUDGET_CALCULATION_FAILED = "BUDGET_CALCULATION_FAILED"

    HISTORICAL_TRAFFIC_SYNC_STARTED = "HISTORICAL_TRAFFIC_SYNC_STARTED"
    HISTORICAL_TRAFFIC_SYNC_FINISHED = "HISTORICAL_TRAFFIC_SYNC_FINISHED"

    FORECAST_RULES_APPLICATION_STARTED = "FORECAST_RULES_APPLICATION_STARTED"
    FORECAST_RULES_APPLICATION_FINISHED = "FORECAST_RULES_APPLICATION_FINISHED"

    AGREEMENTS_APPLICATION_STARTED = "AGREEMENTS_APPLICATION_STARTED"
    AGREEMENTS_APPLICATION_FINISHED = "AGREEMENTS_APPLICATION_FINISHED"

    MASTER_BUDGET_COMPONENTS_CALCULATION_STARTED = "MASTER_BUDGET_COMPONENTS_CALCULATION_STARTED"
    MASTER_BUDGET_COMPONENTS_CALCULATION_FINISHED = "MASTER_BUDGET_COMPONENTS_CALCULATION_FINISHED"


@dataclass
class CreateBudgetCommand(GenericQuery):
    name: str
    description: Optional[str]

    type: BudgetTypeEnum

    home_operators: list[int]

    start_date: date
    end_date: date

    last_historical_month: Month

    user_id: Optional[int]

    run_calculation: bool


@dataclass
class RunBudgetCalculationCommand(GenericQuery):
    budget_id: int

    user_id: Optional[int]

    calculation_type: BudgetCalculationTypeEnum

    budget_agreement_id: Optional[int]

    traffic_lhm: Optional[Month]

    distribution_lhm: Optional[Month]


@dataclass
class DeleteBudgetCommand(GenericQuery):
    budget: Budget


@dataclass
class FillBudgetWithAgreementsCommand(GenericQuery):
    budget_id: int
    budget_agreement_ids: list[int]
