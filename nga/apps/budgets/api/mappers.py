from abc import ABC, abstractmethod

from nga.apps.budgets.api.schemas import BudgetCalculationRetrieveSchema, BudgetParametersSchema
from nga.apps.budgets.domain import Budget, BudgetCalculation
from nga.apps.references.providers import AbstractOperatorProvider
from nga.apps.users.provider import AbstractUserProvider

__all__ = [
    "AbstractBudgetParametersSchemaMapper",
    "BudgetCalculationMapper",
    "BudgetParametersSchemaMapper",
]


class BudgetCalculationMapper:
    def __init__(self, user_provider: AbstractUserProvider) -> None:
        self._user_provider = user_provider

    def map_to_schema(self, calculation: BudgetCalculation) -> BudgetCalculationRetrieveSchema:
        created_by = None

        if calculation.created_by_user_id:
            created_by = self._user_provider.get_by_id(calculation.created_by_user_id)

        schema = BudgetCalculationRetrieveSchema(
            id=calculation.id,
            budget_id=calculation.budget_id,
            budget_agreement_id=calculation.budget_agreement_id,
            type=calculation.type,
            status=calculation.status,
            created_by=created_by,
            created_at=calculation.created_at,
            finished_at=calculation.finished_at,
            traffic_synchronized_at=calculation.traffic_synchronized_at,
            forecast_rules_applied_at=calculation.forecast_rules_applied_at,
            budget_lhm=calculation.budget_lhm,
            traffic_lhm=calculation.traffic_lhm,
            distribution_lhm=calculation.distribution_lhm,
        )

        return schema


class AbstractBudgetParametersSchemaMapper(ABC):
    @abstractmethod
    def map(self, budget: Budget) -> BudgetParametersSchema:
        """Maps domain budget object onto API response schema."""


class BudgetParametersSchemaMapper(AbstractBudgetParametersSchemaMapper):
    def __init__(
        self,
        operator_provider: AbstractOperatorProvider,
    ) -> None:
        self._operator_provider = operator_provider

    def map(self, budget: Budget) -> BudgetParametersSchema:
        home_operators = self._operator_provider.get_many(operators_ids=budget.home_operators)

        schema = BudgetParametersSchema(
            id=budget.id,
            name=budget.name,
            description=budget.description,
            start_date=budget.period.start_date,
            end_date=budget.period.end_date,
            last_historical_month=budget.last_historical_month,
            home_operators=home_operators,
            is_master=budget.is_master,
            type=budget.type,
            created_at=budget.created_at,
            updated_at=budget.updated_at,
        )

        return schema
