from rest_framework_dataclasses.serializers import DataclassSerializer

from nga.apps.budgets.domain.dto import BudgetComponentsQuantity, BudgetComponentsState
from nga.apps.common.serializer_fields import MonthField


class BudgetComponentsQuantitySerializer(DataclassSerializer[BudgetComponentsQuantity]):
    class Meta:
        dataclass = BudgetComponentsQuantity
        fields = (
            "budget_id",
            "agreements",
            "active_agreements",
            "forecast_rules",
            "iot_rates",
        )


class BudgetComponentsStateSerializer(DataclassSerializer[BudgetComponentsState]):
    budget_lhm = MonthField(required=False, default=None, allow_null=True)

    traffic_lhm = MonthField(required=False, default=None, allow_null=True)

    distribution_lhm = MonthField(required=False, default=None, allow_null=True)

    class Meta:
        dataclass = BudgetComponentsState
        fields = (
            "budget_id",
            "historical_traffic_last_modified_at",
            "forecast_rules_last_modified_at",
            "agreements_last_modified_at",
            "budget_traffic_synchronized_at",
            "forecast_rules_applied_at",
            "budget_lhm",
            "traffic_lhm",
            "distribution_lhm",
        )
