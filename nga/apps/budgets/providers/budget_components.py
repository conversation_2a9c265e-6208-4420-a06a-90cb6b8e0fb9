from abc import ABC, abstractmethod
from typing import Optional

from nga.apps.budgets.domain import Budget, BudgetCalculationDoesNotExist
from nga.apps.budgets.domain.dto import BudgetComponentsQuantity, BudgetComponentsState, BudgetParametersFilters
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.providers.budget_agreement import AbstractBudgetAgreementProvider
from nga.apps.budgets.providers.forecast_rule import AbstractForecastRuleProvider


class AbstractBudgetComponentsProvider(ABC):
    """
    Interface for communication with budget components.
        - Agreements
        - IoT Rates
        - Forecast Rules
    """

    @abstractmethod
    def get_quantity(
        self,
        budget_id: int,
        filters: Optional[BudgetParametersFilters] = None,
    ) -> BudgetComponentsQuantity:
        """Returns totals by budget items for requested budget."""

    @abstractmethod
    def get_state(self, budget: Budget) -> BudgetComponentsState:
        """Returns budget components state. Datetime logs when components were updated."""


class BudgetComponentsProvider(AbstractBudgetComponentsProvider):
    """
    Budget Components Provider. Provides interface for budget's:
        - Agreements
        - IoT Rates
        - Forecast Rules
    """

    def __init__(
        self,
        forecast_rule_provider: AbstractForecastRuleProvider,
        budget_agreement_provider: AbstractBudgetAgreementProvider,
        budget_repository: AbstractBudgetRepository,
    ) -> None:
        self._forecast_rule_provider = forecast_rule_provider
        self._agreements_provider = budget_agreement_provider
        self._budget_repository = budget_repository

    def get_quantity(
        self,
        budget_id: int,
        filters: Optional[BudgetParametersFilters] = None,
    ) -> BudgetComponentsQuantity:
        """Returns totals by budget items for requested budget."""

        total_forecast_rules = self._forecast_rule_provider.get_total_rules(budget_id, filters)

        total_agreements = self._agreements_provider.get_total_agreements(
            budget_id,
            budget_parameters=filters,
        )

        active_agreements = self._agreements_provider.get_total_agreements(
            budget_id,
            budget_parameters=filters,
            is_active=True,
        )

        return BudgetComponentsQuantity(
            budget_id=budget_id,
            agreements=total_agreements,
            active_agreements=active_agreements,
            iot_rates=0,
            forecast_rules=total_forecast_rules,
        )

    def get_state(self, budget: Budget) -> BudgetComponentsState:
        """Returns budget components state. Datetime logs when components were updated."""

        try:
            last_full_calculation = self._budget_repository.get_last_full_calculation(budget.id)
            forecast_rules_applied_at = last_full_calculation.forecast_rules_applied_at
            distribution_lhm = last_full_calculation.distribution_lhm
        except BudgetCalculationDoesNotExist:
            forecast_rules_applied_at = None
            distribution_lhm = None

        try:
            last_full_traffic_update_calculation = self._budget_repository.get_last_full_traffic_update_calculation(
                budget_id=budget.id
            )
            budget_traffic_synchronized_at = last_full_traffic_update_calculation.traffic_synchronized_at
            traffic_lhm = last_full_traffic_update_calculation.traffic_lhm
        except BudgetCalculationDoesNotExist:
            budget_traffic_synchronized_at = None
            traffic_lhm = None

        try:
            last_calculation = self._budget_repository.get_last_calculation(budget.id)
            budget_lhm = last_calculation.budget_lhm
        except BudgetCalculationDoesNotExist:
            budget_lhm = None

        state = BudgetComponentsState(
            budget_id=budget.id,
            historical_traffic_last_modified_at=budget.historical_traffic_modified_at,
            forecast_rules_last_modified_at=budget.forecast_rules_modified_at,
            budget_traffic_synchronized_at=budget_traffic_synchronized_at,
            forecast_rules_applied_at=forecast_rules_applied_at,
            agreements_last_modified_at=budget.agreements_last_modified_at,
            budget_lhm=budget_lhm,
            traffic_lhm=traffic_lhm,
            distribution_lhm=distribution_lhm,
        )

        return state
