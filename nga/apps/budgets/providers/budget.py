from abc import ABC, abstractmethod

from nga.apps.budgets.domain.models import Budget
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository


class AbstractBudgetProvider(ABC):
    @abstractmethod
    def get_by_id(self, budget_id: int) -> Budget:
        """Returns budget by id."""


class BudgetProvider(AbstractBudgetProvider):
    def __init__(self, budget_repository: AbstractBudgetRepository) -> None:
        self._budget_repository = budget_repository

    def get_by_id(self, budget_id: int) -> Budget:
        """Returns budget by id."""

        budget = self._budget_repository.get_by_id(budget_id)

        return budget
