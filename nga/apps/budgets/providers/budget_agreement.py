from abc import ABC, abstractmethod
from typing import Optional, Sequence

from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.budgets.domain.dto import BudgetParametersFilters


class AbstractBudgetAgreementProvider(ABC):
    # TODO: Remove this abstraction. Use AbstractBudgetAgreementRepository instead

    @abstractmethod
    def get_by_id(self, budget_agreement_id: int) -> BudgetAgreement:
        """Returns agreement by ID."""

    @abstractmethod
    def get_many(
        self,
        *,
        budget_id: Optional[int] = None,
        agreement_ids: Optional[list[int]] = None,
        only_active: Optional[bool] = None,
        only_modified: Optional[bool] = None,
    ) -> tuple[BudgetAgreement, ...]:
        """Returns agreements for specified budget."""

    @abstractmethod
    def update_many(self, budget_agreements: Sequence[BudgetAgreement]) -> None:
        """Persists collection of agreements to storage."""

    @abstractmethod
    def get_total_agreements(
        self,
        budget_id: int,
        *,
        budget_parameters: Optional[BudgetParametersFilters] = None,
        is_active: Optional[bool] = None,
    ) -> int:
        """Returns number of agreements by provided options."""
