from abc import ABC, abstractmethod
from typing import Optional

from nga.apps.budgets.domain.dto import BudgetParametersFilters


class AbstractForecastRuleProvider(ABC):
    """Interface for communication with forecasts application."""

    @abstractmethod
    def get_total_rules(
        self,
        budget_id: int,
        filters: Optional[BudgetParametersFilters] = None,
    ) -> int:
        """Returns integer that represents amount of forecast rules for specified budget."""
