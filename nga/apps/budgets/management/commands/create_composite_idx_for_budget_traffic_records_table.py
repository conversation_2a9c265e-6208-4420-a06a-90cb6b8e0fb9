from typing import Any

from django.core.management.base import BaseCommand
from django.db import connection


class Command(BaseCommand):
    help = "Creates a composite index for budget_traffic_records table"

    def handle(self, *args: Any, **options: Any) -> None:
        self.stdout.write("Index creation has been started")

        with connection.cursor() as cursor:
            cursor.execute(
                """
                CREATE INDEX IF NOT EXISTS traffic_parameters ON PUBLIC.budget_traffic_records USING btree (
                    budget_snapshot_id,
                    partner_operator_id,
                    traffic_month,
                    service_type,
                    traffic_direction
                )
                """
            )

        self.stdout.write(self.style.SUCCESS('Index "traffic_parameters" was created'))
