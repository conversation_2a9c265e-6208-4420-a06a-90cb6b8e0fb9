from dataclasses import dataclass
from typing import Optional, Sequence

from mediatr.mediator import GenericQuery

from nga.apps.budgets.enums import ChargeTypeEnum, UnitDirectionTypeEnum, UnitTypeEnum
from nga.core.enums import ServiceTypeEnum, TrafficDirectionEnum, VolumeTypeEnum
from nga.core.types import DatePeriod


@dataclass
class GetBudgetPartnerCountryOperatorSharesRequest(GenericQuery):
    budget_id: int

    home_operators: Sequence[int]
    partner_countries: Sequence[int]

    service_type: ServiceTypeEnum
    traffic_direction: TrafficDirectionEnum
    period: DatePeriod


@dataclass
class GetTrafficEvolutionRequest(GenericQuery):
    budget_id: int

    agreement_id: Optional[int]

    volume_type: VolumeTypeEnum

    period: DatePeriod

    service_type: Optional[ServiceTypeEnum]

    charge_type: ChargeTypeEnum

    currency_code: Optional[str]

    unit_type: UnitTypeEnum

    unit_direction_type: UnitDirectionTypeEnum

    include_forecasted: bool

    home_operators: Optional[Sequence[int]]
    partner_operators: Optional[Sequence[int]]
    partner_countries: Optional[Sequence[int]]
