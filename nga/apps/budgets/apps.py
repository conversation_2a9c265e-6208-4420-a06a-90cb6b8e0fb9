from django.apps import AppConfig


class BudgetsConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "nga.apps.budgets"

    def ready(self) -> None:
        # wire package for injecting dependencies

        from nga.infra.di import di_container

        di_container.wire(
            packages=[
                "nga.apps.budgets.api.consumers",
                "nga.apps.budgets.api.serializers",
                "nga.apps.budgets.api.views",
                "nga.apps.budgets.command_handlers",
                "nga.apps.budgets.request_handlers",
                "nga.apps.budgets.event_handlers",
            ],
            modules=[
                "nga.apps.budgets.infra.tasks",
            ],
        )

        from nga.apps.budgets.event_handlers import register_budget_domain_event_handlers

        register_budget_domain_event_handlers(di_container.event_dispatcher())
