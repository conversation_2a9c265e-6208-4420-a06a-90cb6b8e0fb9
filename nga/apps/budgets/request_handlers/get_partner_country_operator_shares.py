from copy import copy
from decimal import Decimal

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.analytics.countries.models import PartnerCountryOperatorShareOptions, PartnerOperatorShare
from nga.apps.budgets.analytics.countries.partner_operator_share import PartnerCountryOperatorShareProviderProtocol
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.dto import (
    BudgetPartnerCountryOperatorShares,
    PartnerCountryOperatorOverallShares,
    PartnerOperatorOverallShareValues,
)
from nga.apps.budgets.requests import GetBudgetPartnerCountryOperatorSharesRequest
from nga.core.types import DatePeriod


@Mediator.handler
class GetBudgetPartnerCountryOperatorSharesRequestHandler:
    """
    Provides partner operator volume (actual) share distribution inside countries for a Budget.

    Example (we need to know operator's share inside two countries (#1, #2) in a Budget (#1)):

        Budget #A:
            country #1:
                operator #1 - 75%
                operator #2 - 10%
                operator #3 - 15%
            country #2:
                operator #1 - 55%
                operator #2 - 45%

    Percentage value is expressed via next set of variables:
     - lhm_share: distribution share for budget's last historical month
     - actual share: distribution share for specified period
     - retrospective_share: distribution share for specified period - 1 year
    """

    @inject
    def __init__(
        self,
        partner_country_share_provider: PartnerCountryOperatorShareProviderProtocol = Closing[
            Provide["partner_country_operator_share_provider"]
        ],
        budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
    ) -> None:
        """Init deps."""

        self._partner_country_share_provider = partner_country_share_provider
        self._budget_repository = budget_repository

    def handle(self, request: GetBudgetPartnerCountryOperatorSharesRequest) -> BudgetPartnerCountryOperatorShares:
        budget = self._budget_repository.get_by_id(request.budget_id)

        operator_shares = []

        for partner_country_id in request.partner_countries:
            actual_options = PartnerCountryOperatorShareOptions(
                budget_id=budget.id,
                home_operators=tuple(request.home_operators),
                partner_country_id=partner_country_id,
                traffic_direction=request.traffic_direction,
                service_type=request.service_type,
                period=request.period,
            )
            actual_shares = self._partner_country_share_provider.get_shares(actual_options)

            lhm_options = copy(actual_options)
            lhm_options.period = DatePeriod.create_from_month(budget.last_historical_month)
            lhm_shares = self._partner_country_share_provider.get_shares(lhm_options)

            retrospective_options = copy(actual_options)
            retrospective_options.period = actual_options.period.previous_year
            retrospective_shares = self._partner_country_share_provider.get_shares(retrospective_options)

            operator_share_values = self._get_overall_partner_operator_shares(
                lhm_shares=lhm_shares.shares,
                actual_shares=actual_shares.shares,
                retrospective_shares=retrospective_shares.shares,
            )

            operator_shares.append(PartnerCountryOperatorOverallShares(partner_country_id, operator_share_values))

        shares = BudgetPartnerCountryOperatorShares(budget.id, tuple(operator_shares))

        return shares

    @classmethod
    def _get_overall_partner_operator_shares(
        cls,
        lhm_shares: tuple[PartnerOperatorShare, ...],
        actual_shares: tuple[PartnerOperatorShare, ...],
        retrospective_shares: tuple[PartnerOperatorShare, ...],
    ) -> tuple[PartnerOperatorOverallShareValues, ...]:
        """
        Converts collections of operator shares to single collection where result objects contain each share value.
        """

        def _map_shares_to_operator_share_map(shares: tuple[PartnerOperatorShare, ...]) -> dict[int, Decimal]:
            return {sh.operator_id: sh.share for sh in shares}

        lhm_shares_map = _map_shares_to_operator_share_map(lhm_shares)

        actual_shares_map = _map_shares_to_operator_share_map(actual_shares)

        retrospective_shares_map = _map_shares_to_operator_share_map(retrospective_shares)

        unique_partner_operator_ids = set(
            list(lhm_shares_map.keys()) + list(actual_shares_map.keys()) + list(retrospective_shares_map.keys())
        )

        values = []

        for operator_id in unique_partner_operator_ids:
            lhm_share = lhm_shares_map.get(operator_id)
            actual_share = actual_shares_map.get(operator_id)
            retrospective_share = retrospective_shares_map.get(operator_id)

            overall_share_values = PartnerOperatorOverallShareValues(
                operator_id=operator_id,
                lhm_share=lhm_share,
                actual_share=actual_share,
                retrospective_share=retrospective_share,
            )

            values.append(overall_share_values)

        return tuple(values)
