from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.analytics.traffic_evolution import AbstractTrafficEvolutionProvider, TrafficEvolutionOptions
from nga.apps.budgets.dto import TrafficEvolutionResponse
from nga.apps.budgets.requests import GetTrafficEvolutionRequest
from nga.core.enums import TrafficTypeEnum


@Mediator.handler
class GetTrafficEvolutionRequestHandler:
    @inject
    def __init__(
        self,
        traffic_evolution_provider: AbstractTrafficEvolutionProvider = Closing[Provide["traffic_evolution_provider"]],
    ) -> None:
        self._traffic_evolution_provider = traffic_evolution_provider

    def handle(self, request: GetTrafficEvolutionRequest) -> TrafficEvolutionResponse:
        options = self._map_request_to_options(request)

        traffic_evolution = self._traffic_evolution_provider.get_values(options)

        historical_records, forecasted_records = [], []

        for record in traffic_evolution.records:
            if record.traffic_type == TrafficTypeEnum.HISTORICAL:
                historical_records.append(record)
            else:
                forecasted_records.append(record)

        response = TrafficEvolutionResponse(
            budget_id=request.budget_id,
            agreement_id=request.agreement_id,
            historical_records=tuple(historical_records),
            forecasted_records=tuple(forecasted_records),
        )

        return response

    @classmethod
    def _map_request_to_options(cls, request: GetTrafficEvolutionRequest) -> TrafficEvolutionOptions:
        return TrafficEvolutionOptions(
            budget_id=request.budget_id,
            volume_type=request.volume_type,
            period=request.period,
            service_type=request.service_type,
            charge_type=request.charge_type,
            currency_code=request.currency_code,
            unit_type=request.unit_type,
            unit_direction_type=request.unit_direction_type,
            include_forecasted=request.include_forecasted,
            home_operators=request.home_operators,
            partner_operators=request.partner_operators,
            partner_countries=request.partner_countries,
        )
