from datetime import date
from typing import Optional, Sequence

from django.db.models import QuerySet
from django.db.models.functions import Coalesce
from django.db.transaction import atomic
from django.utils import timezone

from nga.apps.budgets.domain import Budget, BudgetCalculation, BudgetSnapshot
from nga.apps.budgets.domain.exceptions import (
    BudgetCalculationDoesNotExist,
    BudgetCalculationIsRunning,
    BudgetDoesNotExist,
)
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.budgets.enums import (
    BudgetCalculationStatusEnum,
    BudgetCalculationTypeEnum,
    BudgetSnapshotTypeEnum,
    BudgetTypeEnum,
)
from nga.apps.budgets.infra.orm import models
from nga.apps.common.queryset_utils import to_pk_list
from nga.core.types import DatePeriod, Month

BUDGET_SNAPSHOT_SYSTEM_NAME = "SYSTEM_SNAPSHOT"


class BudgetDjangoORMRepository(AbstractBudgetRepository):
    def get_by_id(self, budget_id: int) -> Budget:
        """Returns budget by provided id."""

        orm_budget = self._get_orm_budget_by_id(budget_id)

        return from_orm_budget_to_domain(orm_budget)

    def get_by_snapshot_id(self, budget_snapshot_id: int) -> Budget:
        orm_budget = models.BudgetSnapshot.objects.get(id=budget_snapshot_id).budget

        return from_orm_budget_to_domain(orm_budget)

    def get_many(
        self,
        home_operators: Optional[Sequence[int]] = None,
        month_included_in_period: Optional[date] = None,
    ) -> tuple[Budget, ...]:
        """Returns collection of Budgets objects."""

        qs = models.Budget.objects.exclude(_is_deleting=True)

        if home_operators is not None:
            qs = qs.filter(home_operators__pk__in=home_operators).distinct()

        if month_included_in_period is not None:
            qs = qs.annotate(finish_date=Coalesce("last_historical_month", "end_date")).filter(
                start_date__lte=month_included_in_period,
                finish_date__gte=month_included_in_period,
            )

        return tuple(from_orm_budget_to_domain(orm_budget) for orm_budget in qs)

    @classmethod
    def _get_orm_budget_by_id(cls, budget_id: int) -> models.Budget:
        try:
            orm_budget = models.Budget.objects.get(pk=budget_id)
        except models.Budget.DoesNotExist:
            raise BudgetDoesNotExist.from_budget_id(budget_id)

        return orm_budget

    def get_master(self) -> Budget:
        """Returns master budget."""

        try:
            orm_budget = models.Budget.objects.get(is_master=True)
        except models.Budget.DoesNotExist:
            raise BudgetDoesNotExist("Master budget does not exist.")

        return from_orm_budget_to_domain(orm_budget)

    def create(
        self,
        name: str,
        period: DatePeriod,
        type: BudgetTypeEnum,
        home_operators: list[int],
        last_historical_month: Month,
        *,
        user_id: Optional[int],
        description: Optional[str],
    ) -> Budget:
        """Creates and persists Budget instance with provided parameters."""

        orm_budget = models.Budget.objects.create(
            name=name,
            type=type,
            description=description,
            start_date=period.start_date,
            end_date=period.end_date,
            last_historical_month=last_historical_month.to_date(),
        )
        orm_budget.home_operators.set(home_operators)

        self.create_default_snapshots(orm_budget, user_id=user_id)

        orm_budget.refresh_from_db()

        budget = from_orm_budget_to_domain(orm_budget)

        return budget

    @classmethod
    def create_default_snapshots(cls, orm_budget: models.Budget, *, user_id: Optional[int]) -> None:
        """Creates budget ACTIVE and CALCULATION snapshots, sets active_snapshot_id for budget."""

        snapshot_params = dict(
            budget_id=orm_budget.id,
            name=BUDGET_SNAPSHOT_SYSTEM_NAME,
            created_by_id=user_id,
            created_at=timezone.now(),
        )
        active_snapshot = models.BudgetSnapshot.objects.create(type=BudgetSnapshotTypeEnum.ACTIVE, **snapshot_params)

        orm_budget.active_snapshot_id = active_snapshot.id
        orm_budget.save(update_fields=["active_snapshot_id"])

        models.BudgetSnapshot.objects.create(type=BudgetSnapshotTypeEnum.CALCULATION, **snapshot_params)

    def save(self, budget: Budget) -> Budget:
        """Persists budget to database."""

        orm_budget = self._get_orm_budget_by_id(budget.id)

        update_fields = [
            "name",
            "description",
            "last_historical_month",
            "active_snapshot_id",
            "forecast_rules_modified_at",
            "historical_traffic_modified_at",
            "agreements_last_modified_at",
            "start_date",
            "end_date",
            "type",
        ]

        for field_name in update_fields:
            if field_name in ("start_date", "end_date"):
                period = budget.period
                setattr(orm_budget, field_name, getattr(period, field_name))
            else:
                setattr(orm_budget, field_name, getattr(budget, field_name))

        orm_budget.save(update_fields=update_fields)
        orm_budget.refresh_from_db()

        return from_orm_budget_to_domain(orm_budget)

    def get_last_calculation(self, budget_id: int) -> BudgetCalculation:
        """
        Returns last calculation record from requested budget. Last budget calculation - last created calculation
        record from budget's CALCULATION snapshot.
        """

        orm_budget = self._get_orm_budget_by_id(budget_id)

        orm_calc_snapshot = orm_budget.snapshots.get(type=BudgetSnapshotTypeEnum.CALCULATION)

        calculation = self._get_last_calculation(orm_calc_snapshot.budget_calculations.all(), budget_id)

        return calculation

    def get_last_agreement_calculation(self, budget_id: int, budget_agreement_id: int) -> BudgetCalculation:
        orm_budget = self._get_orm_budget_by_id(budget_id)

        orm_calc_snapshot = orm_budget.snapshots.get(type=BudgetSnapshotTypeEnum.CALCULATION)

        budget_calculations_qs = orm_calc_snapshot.budget_calculations.filter(
            agreement_id=budget_agreement_id,
            type=BudgetCalculationTypeEnum.SINGLE_AGREEMENT,
        )

        calculation = self._get_last_calculation(budget_calculations_qs, budget_id)

        return calculation

    @classmethod
    def _get_last_calculation(
        cls,
        qs: QuerySet[models.BudgetCalculation],
        budget_id: int,
    ) -> BudgetCalculation:
        orm_calculation = qs.order_by("-created_at").first()

        if orm_calculation is None:
            raise BudgetCalculationDoesNotExist.from_budget_id(budget_id)

        return from_orm_calculation_to_domain(orm_calculation, budget_id)

    def get_last_full_calculation(self, budget_id: int) -> BudgetCalculation:
        """Return last budget calculation with any FULL calculation type."""

        orm_calc_snapshot = models.BudgetSnapshot.objects.get(
            budget_id=budget_id,
            type=BudgetSnapshotTypeEnum.CALCULATION,
        )

        calculations_qs = orm_calc_snapshot.budget_calculations.filter(
            type__in=(
                BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE,
                BudgetCalculationTypeEnum.FULL_WITHOUT_TRAFFIC_UPDATE,
            )
        )

        calculation = self._get_last_calculation(calculations_qs, budget_id)

        return calculation

    def get_last_full_traffic_update_calculation(self, budget_id: int) -> BudgetCalculation:
        """Return last budget calculation with FULL_TRAFFIC_UPDATE calculation type."""

        orm_calc_snapshot = models.BudgetSnapshot.objects.get(
            budget_id=budget_id,
            type=BudgetSnapshotTypeEnum.CALCULATION,
        )

        calculations_qs = orm_calc_snapshot.budget_calculations.filter(
            type=BudgetCalculationTypeEnum.FULL_WITH_TRAFFIC_UPDATE
        )

        calculation = self._get_last_calculation(calculations_qs, budget_id)

        return calculation

    @classmethod
    def get_calculation_snapshot(cls, budget_id: int) -> BudgetSnapshot:
        """Returns budget CALCULATION snapshot object."""

        orm_snapshot = models.BudgetSnapshot.objects.get(
            budget_id=budget_id,
            type=BudgetSnapshotTypeEnum.CALCULATION,
        )

        return BudgetSnapshot(
            id=orm_snapshot.id,
            budget_id=orm_snapshot.budget_id,
            name=orm_snapshot.name,
            type=BudgetSnapshotTypeEnum(orm_snapshot.type),
            created_by_user_id=orm_snapshot.created_by_id,
            created_at=orm_snapshot.created_at,
            updated_at=orm_snapshot.updated_at,
        )

    @atomic
    def create_calculation(
        self,
        budget_id: int,
        calculation_type: BudgetCalculationTypeEnum,
        created_by_user_id: Optional[int],
        budget_agreement_id: Optional[int],
        budget_lhm: Month,
        traffic_lhm: Optional[Month],
        distribution_lhm: Optional[Month],
    ) -> BudgetCalculation:
        """Creates budget calculation record."""

        try:
            last_calculation = self.get_last_calculation(budget_id)

            if last_calculation.is_running:
                raise BudgetCalculationIsRunning()

        except BudgetCalculationDoesNotExist:
            pass

        calc_snapshot = self.get_calculation_snapshot(budget_id)

        orm_calculation = models.BudgetCalculation.objects.create(
            budget_snapshot_id=calc_snapshot.id,
            type=calculation_type.value,
            status=BudgetCalculationStatusEnum.WAITING_FOR_START,
            created_by_id=created_by_user_id,
            agreement_id=budget_agreement_id,
            budget_lhm=budget_lhm,
            traffic_lhm=traffic_lhm,
            distribution_lhm=distribution_lhm,
        )

        return from_orm_calculation_to_domain(
            orm_calculation=orm_calculation,
            budget_id=orm_calculation.budget_snapshot.budget_id,
        )

    def save_calculation(self, calculation: BudgetCalculation) -> BudgetCalculation:
        """Persists calculation instance to storage."""

        orm_calculation = self._get_orm_calculation_by_id(calculation.id)

        orm_calculation.job_id = calculation.job_id
        orm_calculation.status = calculation.status.value
        orm_calculation.finished_at = calculation.finished_at
        orm_calculation.traffic_synchronized_at = calculation.traffic_synchronized_at
        orm_calculation.forecast_rules_applied_at = calculation.forecast_rules_applied_at
        orm_calculation.agreements_applied_at = calculation.agreements_applied_at

        orm_calculation.save(
            update_fields=[
                "job_id",
                "status",
                "finished_at",
                "traffic_synchronized_at",
                "forecast_rules_applied_at",
                "agreements_applied_at",
            ]
        )

        orm_calculation.refresh_from_db()

        return from_orm_calculation_to_domain(orm_calculation, calculation.budget_id)

    def get_calculation_by_id(self, calculation_id: int) -> BudgetCalculation:
        """Returns budget calculation instance by provided ID."""

        orm_calculation = self._get_orm_calculation_by_id(calculation_id)

        return from_orm_calculation_to_domain(
            orm_calculation=orm_calculation,
            budget_id=orm_calculation.budget_snapshot.budget_id,
        )

    @classmethod
    def _get_orm_calculation_by_id(cls, calculation_id: int) -> models.BudgetCalculation:
        try:
            orm_calculation = models.BudgetCalculation.objects.get(id=calculation_id)
        except models.BudgetCalculation.DoesNotExist:
            raise BudgetCalculationDoesNotExist(f"Budget Calculation with id={calculation_id} does not exist.")

        return orm_calculation

    def set_for_delete(self, budget_id: int) -> None:
        orm_budget = self._get_orm_budget_by_id(budget_id)
        orm_budget._is_deleting = True
        orm_budget.save(update_fields=["_is_deleting"])

    def delete_by_id(self, budget_id: int) -> None:
        orm_budget = self._get_orm_budget_by_id(budget_id)
        orm_budget.delete()


# TODO: move to mappers
def from_orm_budget_to_domain(orm_budget: models.Budget) -> Budget:
    budget = Budget(
        id=orm_budget.id,
        name=orm_budget.name,
        description=orm_budget.description,
        home_operators=to_pk_list(orm_budget.home_operators.all()),
        period=DatePeriod(orm_budget.start_date, orm_budget.end_date),
        is_master=orm_budget.is_master,
        type=BudgetTypeEnum(orm_budget.type),
        active_snapshot_id=orm_budget.get_active_snapshot_id(),
        calculation_snapshot_id=orm_budget.get_calculation_snapshot_id(),
        last_historical_month=Month.create_from_date(orm_budget.last_historical_month),
        forecast_rules_modified_at=orm_budget.forecast_rules_modified_at,
        historical_traffic_modified_at=orm_budget.historical_traffic_modified_at,
        agreements_last_modified_at=orm_budget.agreements_last_modified_at,
        created_at=orm_budget.created_at,
        updated_at=orm_budget.updated_at,
    )

    return budget


def from_orm_calculation_to_domain(
    orm_calculation: models.BudgetCalculation,
    budget_id: int,
) -> BudgetCalculation:
    calculation = BudgetCalculation(
        id=orm_calculation.pk,
        budget_id=budget_id,
        budget_snapshot_id=orm_calculation.budget_snapshot_id,
        type=BudgetCalculationTypeEnum(orm_calculation.type),
        status=BudgetCalculationStatusEnum(orm_calculation.status),
        budget_agreement_id=orm_calculation.agreement_id,
        created_by_user_id=orm_calculation.created_by_id,
        created_at=orm_calculation.created_at,
        finished_at=orm_calculation.finished_at,
        traffic_synchronized_at=orm_calculation.traffic_synchronized_at,
        forecast_rules_applied_at=orm_calculation.forecast_rules_applied_at,
        agreements_applied_at=orm_calculation.agreements_applied_at,
        budget_lhm=Month.create_from_date(orm_calculation.budget_lhm) if orm_calculation.budget_lhm else None,
        traffic_lhm=Month.create_from_date(orm_calculation.traffic_lhm) if orm_calculation.traffic_lhm else None,
        distribution_lhm=(
            Month.create_from_date(orm_calculation.distribution_lhm) if orm_calculation.distribution_lhm else None
        ),
        job_id=orm_calculation.job_id,
    )

    return calculation
