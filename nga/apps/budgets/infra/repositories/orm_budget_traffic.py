from datetime import date
from typing import Any, Collection, Iterable, Optional, Sequence

from django.conf import settings
from django.db import connection
from django.db.models import F, QuerySet

from nga.apps.budgets.domain.dto import BudgetTrafficParameters, BudgetTrafficRecordDTO
from nga.apps.budgets.domain.models import BudgetTrafficRecord
from nga.apps.budgets.domain.repositories import AbstractBudgetTrafficRepository
from nga.apps.budgets.infra.orm import models
from nga.core.consts import SATELLITE_COUNTRY_CODE
from nga.core.enums import (
    CallDestinationEnum,
    IMSICountTypeEnum,
    ServiceTypeEnum,
    TrafficDirectionEnum,
    TrafficTypeEnum,
)
from nga.core.types import DatePeriod, Month


class BudgetTrafficORMRepository(AbstractBudgetTrafficRepository):
    """Django ORM AbstractBudgetTrafficRepository implementation."""

    @classmethod
    def filter_qs(
        cls,
        snapshot_id: int,
        *,
        period: Optional[DatePeriod] = None,
        traffic_types: Optional[Sequence[TrafficTypeEnum]] = None,
        traffic_months: Optional[Sequence[date]] = None,
        traffic_directions: Optional[Sequence[TrafficDirectionEnum]] = None,
        traffic_segments: Optional[Sequence[int]] = None,
        call_destinations: Optional[Sequence[CallDestinationEnum]] = None,
        service_types: Optional[Sequence[ServiceTypeEnum]] = None,
        include_forecasted: Optional[bool] = True,
        home_operators: Optional[Sequence[int]] = None,
        partner_operators: Optional[Sequence[int]] = None,
        partner_countries: Optional[Sequence[int]] = None,
        forecast_rule_ids: Optional[Sequence[int]] = None,
        discount_ids: Optional[Sequence[int]] = None,
        called_countries: Optional[Sequence[int]] = None,
    ) -> QuerySet[models.BudgetTrafficRecord]:
        """Filters BudgetTrafficRecord Django QuerySet by provided parameters."""

        parameters = BudgetTrafficParameters(
            snapshot_id=snapshot_id,
            home_operators=home_operators,
            partner_operators=partner_operators,
            partner_countries=partner_countries,
            period=period,
            service_types=service_types,
            traffic_types=traffic_types,
            traffic_months=traffic_months,
            traffic_segments=traffic_segments,
            traffic_directions=traffic_directions,
            call_destinations=call_destinations,
            called_countries=called_countries,
            include_forecasted=include_forecasted,
            forecast_rule_ids=forecast_rule_ids,
            discount_ids=discount_ids,
        )

        qs = models.BudgetTrafficRecord.objects.filter_by_parameters(parameters)

        return qs

    def get_many(
        self,
        snapshot_id: int,
        *,
        home_operators: Optional[Sequence[int]] = None,
        partner_operators: Optional[Sequence[int]] = None,
        traffic_types: Optional[Sequence[TrafficTypeEnum]] = None,
        traffic_months: Optional[Sequence[date]] = None,
        traffic_directions: Optional[Sequence[TrafficDirectionEnum]] = None,
        call_destinations: Optional[Sequence[CallDestinationEnum]] = None,
        service_types: Optional[Sequence[ServiceTypeEnum]] = None,
        period: Optional[DatePeriod] = None,
        include_forecasted: Optional[bool] = True,
        called_countries: Optional[Sequence[int]] = None,
        traffic_segments: Optional[Sequence[int]] = None,
        include_satellite: Optional[bool] = True,
    ) -> Iterable[BudgetTrafficRecord]:
        """Returns list of budget traffic records filtered by provided values."""

        records = self.filter_qs(
            snapshot_id=snapshot_id,
            home_operators=home_operators,
            partner_operators=partner_operators,
            period=period,
            service_types=service_types,
            traffic_months=traffic_months,
            traffic_types=traffic_types,
            traffic_directions=traffic_directions,
            call_destinations=call_destinations,
            include_forecasted=include_forecasted,
            called_countries=called_countries,
            traffic_segments=traffic_segments,
        ).values()

        # by default records with called country SAT are included to query results. If include_satellite is False,
        #  then those records must be excluded from results
        if include_satellite is False:
            records = records.exclude(called_country__code=SATELLITE_COUNTRY_CODE)

        for row in records:
            yield BudgetTrafficRecord(
                id=row["id"],
                budget_snapshot_id=row["budget_snapshot_id"],
                home_operator_id=row["home_operator_id"],
                partner_operator_id=row["partner_operator_id"],
                traffic_type=TrafficTypeEnum(row["traffic_type"]),
                traffic_month=row["traffic_month"],
                traffic_direction=TrafficDirectionEnum(row["traffic_direction"]),
                traffic_segment_id=row["traffic_segment_id"],
                service_type=ServiceTypeEnum(row["service_type"]),
                call_destination=(CallDestinationEnum(row["call_destination"]) if row["call_destination"] else None),
                called_country_id=row["called_country_id"],
                is_premium=row["is_premium"],
                imsi_count_type=(IMSICountTypeEnum(row["imsi_count_type"]) if row["imsi_count_type"] else None),
                volume_actual=row["volume_actual"],
                volume_billed=row["volume_billed"],
                tap_charge_net=row["tap_charge_net"],
                tap_charge_gross=row["tap_charge_gross"],
                charge_net=row["charge_net"],
                charge_gross=row["charge_gross"],
                forecast_rule_id=row["forecast_rule_id"],
                discount_id=row["discount_id"],
            )

    def create_many(self, records: Iterable[BudgetTrafficRecordDTO]) -> None:
        """Fills budget with traffic records."""

        orm_records = (
            models.BudgetTrafficRecord(
                budget_snapshot_id=r.budget_snapshot_id,
                forecast_rule_id=r.forecast_rule_id,
                discount_id=r.discount_id,
                home_operator_id=r.home_operator_id,
                partner_operator_id=r.partner_operator_id,
                traffic_type=r.traffic_type,
                traffic_month=r.traffic_month,
                traffic_direction=r.traffic_direction,
                traffic_segment_id=r.traffic_segment_id,
                service_type=r.service_type,
                call_destination=r.call_destination,
                called_country_id=r.called_country_id,
                imsi_count_type=r.imsi_count_type,
                is_premium=r.is_premium,
                volume_actual=r.volume_actual,
                volume_billed=r.volume_billed,
                tap_charge_net=r.tap_charge_net,
                tap_charge_gross=r.tap_charge_gross,
                charge_net=r.charge_net,
                charge_gross=r.charge_gross,
            )
            for r in records
        )

        models.BudgetTrafficRecord.objects.bulk_create(orm_records, batch_size=settings.DEFAULT_BATCH_SIZE)

    def delete_many(
        self,
        budget_snapshot_id: int,
        *,
        ids: Optional[Collection[int]] = None,
        home_operators: Optional[Sequence[int]] = None,
        partner_operators: Optional[Sequence[int]] = None,
        traffic_months: Optional[Sequence[Month]] = None,
        traffic_type: Optional[TrafficTypeEnum] = None,
        forecast_rule_id: Optional[int] = None,
        discount_ids: Optional[Sequence[int]] = None,
    ) -> None:
        """Delete budget traffic records by provided parameters."""

        qs = self.filter_qs(
            snapshot_id=budget_snapshot_id,
            home_operators=home_operators,
            partner_operators=partner_operators,
            traffic_months=traffic_months,
            traffic_types=[traffic_type] if traffic_type else None,
            forecast_rule_ids=[forecast_rule_id] if forecast_rule_id else None,
            discount_ids=discount_ids,
        )

        if ids:
            qs = qs.filter(pk__in=ids)

        qs.delete()

    def update_many(self, records: Collection[BudgetTrafficRecord]) -> None:
        """Updates charges for each record in a collection."""

        objs = (
            models.BudgetTrafficRecord(
                id=r.id,
                budget_snapshot_id=r.budget_snapshot_id,
                forecast_rule_id=r.forecast_rule_id,
                home_operator_id=r.home_operator_id,
                partner_operator_id=r.partner_operator_id,
                traffic_type=r.traffic_type,
                traffic_month=r.traffic_month,
                traffic_direction=r.traffic_direction,
                traffic_segment_id=r.traffic_segment_id,
                service_type=r.service_type,
                call_destination=r.call_destination,
                called_country_id=r.called_country_id,
                is_premium=r.is_premium,
                volume_actual=r.volume_actual,
                volume_billed=r.volume_billed,
                tap_charge_net=r.tap_charge_net,
                tap_charge_gross=r.tap_charge_gross,
                charge_net=r.charge_net,
                charge_gross=r.charge_gross,
            )
            for r in records
        )

        models.BudgetTrafficRecord.objects.bulk_update(
            objs=objs,
            fields=("charge_net", "charge_gross", "updated_at"),
            batch_size=settings.DEFAULT_BATCH_SIZE,
        )

    def update_or_create(self, record: BudgetTrafficRecordDTO) -> None:
        """Updates or create budget traffic records."""

        obj, created = models.BudgetTrafficRecord.objects.update_or_create(
            budget_snapshot_id=record.budget_snapshot_id,
            home_operator_id=record.home_operator_id,
            partner_operator_id=record.partner_operator_id,
            traffic_type=record.traffic_type,
            traffic_month=record.traffic_month,
            traffic_direction=record.traffic_direction,
            traffic_segment_id=record.traffic_segment_id,
            service_type=record.service_type,
            call_destination=record.call_destination,
            called_country_id=record.called_country_id,
            is_premium=record.is_premium,
            defaults={
                "forecast_rule_id": record.forecast_rule_id,
                "discount_id": record.discount_id,
                "volume_actual": record.volume_actual,
                "volume_billed": record.volume_billed,
                "tap_charge_net": record.tap_charge_net,
                "tap_charge_gross": record.tap_charge_gross,
                "charge_net": record.charge_net,
                "charge_gross": record.charge_gross,
            },
        )

    def exists(
        self,
        *,
        budget_snapshot_id: int,
        home_operators: Sequence[int],
        partner_operators: Sequence[int],
        period: DatePeriod,
        traffic_direction: TrafficDirectionEnum,
        service_type: ServiceTypeEnum,
    ) -> bool:
        """Returns booleans that marks whether traffic exists or not."""

        traffic_exists = models.BudgetTrafficRecord.objects.filter(
            budget_snapshot_id=budget_snapshot_id,
            home_operator_id__in=home_operators,
            partner_operator_id__in=partner_operators,
            traffic_month__range=period.period,
            traffic_direction=traffic_direction,
            service_type=service_type,
        ).exists()

        return traffic_exists

    def copy_many(
        self,
        source_snapshot_id: int,
        target_snapshot_id: int,
        *,
        home_operators: Optional[Sequence[int]] = None,
        partner_operators: Optional[Sequence[int]] = None,
        traffic_months: Optional[Sequence[date]] = None,
    ) -> None:
        """Copies traffic records from source snapshot to target."""

        model_meta = models.BudgetTrafficRecord._meta

        manual_value_fields = {"budget_snapshot_id"}

        auto_fields = {"id", "created_at", "updated_at"}

        fields = set(f.get_attname_column()[1] for f in model_meta.fields) - manual_value_fields - auto_fields

        str_fields = ",".join(fields)

        query = f"""
        INSERT INTO {model_meta.db_table} (
            budget_snapshot_id,
            created_at,
            updated_at,
            {str_fields}
        )
        SELECT
            %(target_snapshot_id)s as budget_snapshot_id,
            NOW() AT TIME ZONE ('utc'),
            NOW() AT TIME ZONE ('utc'),
            {str_fields}
        FROM {model_meta.db_table}
        WHERE budget_snapshot_id = %(source_snapshot_id)s
        """

        query_arguments: dict[str, Any] = {
            "target_snapshot_id": target_snapshot_id,
            "source_snapshot_id": source_snapshot_id,
        }

        if home_operators:
            query += " AND home_operator_id = ANY(%(home_operators)s)"
            query_arguments["home_operators"] = list(home_operators)

        if partner_operators:
            query += " AND partner_operator_id = ANY(%(partner_operators)s)"
            query_arguments["partner_operators"] = list(partner_operators)

        if traffic_months:
            query += " AND traffic_month = ANY(%(traffic_months)s)"
            query_arguments["traffic_months"] = list(traffic_months)

        with connection.cursor() as cursor:
            cursor.execute(query, query_arguments)

    def get_last_historical_month(self, snapshot_id: int) -> Optional[Month]:
        result = (
            models.BudgetTrafficRecord.objects.filter(
                budget_snapshot_id=snapshot_id,
                traffic_type=TrafficTypeEnum.HISTORICAL,
            )
            .order_by("-traffic_month")
            .values("traffic_month")
            .first()
        )

        return Month.create_from_date(result["traffic_month"]) if result else None

    def reset_discount(
        self,
        snapshot_id: int,
        *,
        home_operators: Optional[Sequence[int]] = None,
        partner_operators: Optional[Sequence[int]] = None,
        period: Optional[DatePeriod] = None,
    ) -> None:
        self.filter_qs(
            snapshot_id=snapshot_id,
            home_operators=home_operators,
            partner_operators=partner_operators,
            period=period,
        ).update(
            charge_net=F("tap_charge_net"),
            charge_gross=F("tap_charge_gross"),
        )
