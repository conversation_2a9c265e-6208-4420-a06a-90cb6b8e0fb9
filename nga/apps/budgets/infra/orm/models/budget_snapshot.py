from typing import Iterable

from django.db import models
from django.db.models import Q, UniqueConstraint
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from nga.apps.budgets.enums import BudgetSnapshotTypeEnum
from nga.core.enums import get_choices


class BudgetSnapshot(models.Model):
    """Database model that describes budget's saving checkpoint."""

    budget = models.ForeignKey(
        "budgets.Budget",
        verbose_name=_("Budget"),
        on_delete=models.CASCADE,
        related_name="snapshots",
        related_query_name="snapshot",
    )

    name = models.CharField(verbose_name=_("Name"), max_length=248)

    type = models.PositiveSmallIntegerField(
        _("Budget Snapshot Type"),
        choices=get_choices(BudgetSnapshotTypeEnum),
    )

    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.PROTECT,
        related_name="budget_snapshots",
        related_query_name="budget_snapshot",
        null=True,
        blank=True,
    )

    created_at = models.DateTimeField(_("Creation Date"), editable=False)
    updated_at = models.DateTimeField(_("Update Date"))

    class Meta:
        db_table = "budget_snapshots"

        constraints = [
            UniqueConstraint(
                fields=("budget_id", "type"),
                condition=Q(type=BudgetSnapshotTypeEnum.ACTIVE),
                name="allowed_only_one_active_snapshot_per_budget",
            ),
            UniqueConstraint(
                fields=("budget_id", "type"),
                condition=Q(type=BudgetSnapshotTypeEnum.CALCULATION),
                name="allowed_only_one_calculation_snapshot_per_budget",
            ),
        ]

    def __repr__(self) -> str:
        return (
            f"<{self.__class__.__name__}("
            f"id={self.id}, "
            f"name={self.name}, "
            f"budget_id={self.budget_id}, "
            f"type={self.get_type_display()}"
            f")>"
        )

    def __str__(self) -> str:
        return f"Budget #{self.budget_id} - {self.name} - {self.get_type_display()}"

    def save(
        self,
        force_insert: bool = False,
        force_update: bool = False,
        using: str | None = None,
        update_fields: Iterable[str] | None = None,
    ) -> None:
        if self.pk is None:
            self.created_at = timezone.now()

        self.updated_at = timezone.now()

        super().save(force_insert=force_insert, force_update=force_update, using=using, update_fields=update_fields)
