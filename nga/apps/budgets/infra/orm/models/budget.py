from typing import Iterable

from django.core.exceptions import ValidationError
from django.db import models
from django.db.models import Q, UniqueConstraint
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from nga.apps.budgets.enums import BudgetSnapshotTypeEnum, BudgetTypeEnum
from nga.core.enums import get_choices


class Budget(models.Model):
    name = models.CharField(_("Budget Name"), max_length=248)
    description = models.CharField(_("Budget Description"), max_length=255, null=True, blank=True)

    type = models.PositiveSmallIntegerField(
        _("Budget Type"),
        choices=get_choices(BudgetTypeEnum),
    )

    home_operators = models.ManyToManyField(
        "references.Operator",
        verbose_name=_("Budget Home Operators"),
        related_name="budgets",
        related_query_name="budget",
    )

    start_date = models.DateField(_("Budget Start Date"))
    end_date = models.DateField(_("Budget End Date"))

    created_at = models.DateTimeField(_("Budget Creation Date"), auto_now_add=True)
    updated_at = models.DateTimeField(_("Last updated date"), auto_now=True)

    is_master = models.BooleanField(_("Master"), default=False)

    active_snapshot = models.ForeignKey(
        "BudgetSnapshot",
        verbose_name=_("Active Snapshot"),
        on_delete=models.CASCADE,
        related_name="+",
        null=True,
        blank=True,
    )

    last_historical_month = models.DateField(_("Last historical month"))

    forecast_rules_modified_at = models.DateTimeField(_("Forecast Rules changed at"), null=True, blank=True)

    historical_traffic_modified_at = models.DateTimeField(_("Historical Traffic changed at"), null=True, blank=True)

    agreements_last_modified_at = models.DateTimeField(_("Agreements changed at"), null=True, blank=True)

    # service fields

    _is_deleting = models.BooleanField(null=True, blank=True)

    class Meta:
        db_table = "budgets"

        constraints = [
            UniqueConstraint(
                fields=("is_master",),
                condition=Q(is_master=True),
                name="allowed_only_one_master_budget",
            ),
            UniqueConstraint(
                fields=("type",),
                condition=Q(type=BudgetTypeEnum.MASTER),
                name="allowed_only_one_master_budget_type",
            ),
        ]

    def __str__(self) -> str:
        return f"#{self.id} - {self.name}"

    def __repr__(self) -> str:
        return (
            f"<{self.__class__.__name__}("
            f"id={self.id}, "
            f"name={self.name}, "
            f"start_date={self.start_date}, "
            f"end_date={self.end_date}"
            f")>"
        )

    def clean(self) -> None:
        if self.start_date > self.end_date:
            raise ValidationError(_("Start Date must be less then End Date"))

    def save(
        self,
        force_insert: bool = False,
        force_update: bool = False,
        using: str | None = None,
        update_fields: Iterable[str] | None = None,
    ) -> None:
        self.updated_at = timezone.now()

        super().save(force_insert=force_insert, force_update=force_update, using=using, update_fields=update_fields)

    def get_active_snapshot_id(self) -> int:
        if self.active_snapshot_id is None:
            raise ValidationError("Budget must have ACTIVE snapshot")

        return self.active_snapshot_id

    def get_calculation_snapshot_id(self) -> int:
        snapshot = self.snapshots.get(type=BudgetSnapshotTypeEnum.CALCULATION)

        return snapshot.pk
