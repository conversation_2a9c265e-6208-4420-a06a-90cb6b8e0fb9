from django.db import models
from django.db.models import Index
from django.utils.translation import gettext_lazy as _

from nga.apps.budgets.infra.orm.querysets import BudgetTrafficRecordQuerySet
from nga.apps.references.infra.orm.models import BaseMonthlyAggregatedTrafficRecord
from nga.core.enums import IMSICountTypeEnum, get_choices


class BudgetTrafficRecord(BaseMonthlyAggregatedTrafficRecord):
    """
    Budget's traffic record. Charge and Volume value in a combination of
    traffic type, traffic direction and service type for particular month.
    """

    budget_snapshot = models.ForeignKey(
        "budgets.BudgetSnapshot",
        verbose_name=_("Budget Traffic Snapshot"),
        on_delete=models.CASCADE,
        related_name="traffic_records",
        related_query_name="traffic_record",
    )

    home_operator = models.ForeignKey(
        "references.Operator",
        verbose_name=_("Home Operator"),
        on_delete=models.PROTECT,
        related_name="budget_home_traffic_records",
        related_query_name="budget_home_traffic_record",
    )

    partner_operator = models.ForeignKey(
        "references.Operator",
        verbose_name=_("Partner Operator"),
        on_delete=models.PROTECT,
        related_name="budget_partner_traffic_records",
        related_query_name="budget_partner_traffic_record",
    )

    called_country = models.ForeignKey(
        "references.Country",
        verbose_name=_("Called Country"),
        on_delete=models.PROTECT,
        related_name="incoming_budget_traffic_records",
        related_query_name="incoming_budget_traffic_record",
        null=True,
        blank=True,
    )

    traffic_segment = models.ForeignKey(
        "references.TrafficSegment",
        verbose_name=_("Traffic Segment"),
        on_delete=models.PROTECT,
        related_name="budget_traffic_records",
        related_query_name="budget_traffic_record",
        db_index=False,
        blank=True,
        null=True,
    )

    imsi_count_type = models.PositiveSmallIntegerField(
        _("IMSI Count Type"),
        choices=get_choices(IMSICountTypeEnum),
        null=True,
        blank=True,
    )

    forecast_rule = models.ForeignKey(
        "forecasts.ForecastRule",
        on_delete=models.CASCADE,
        related_name="traffic_records",
        related_query_name="traffic_record",
        null=True,
        blank=True,
    )

    discount = models.ForeignKey(
        "agreements.Discount",
        on_delete=models.CASCADE,
        related_name="traffic_records",
        related_query_name="traffic_record",
        null=True,
        blank=True,
    )

    objects = BudgetTrafficRecordQuerySet.as_manager()

    class Meta:
        db_table = "budget_traffic_records"

        indexes = [
            Index(
                fields=[
                    "budget_snapshot_id",
                    "partner_operator_id",
                    "traffic_month",
                    "service_type",
                    "traffic_direction",
                ],
                name="traffic_parameters",
            )
        ]
