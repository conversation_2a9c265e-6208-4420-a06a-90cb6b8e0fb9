from typing import Iterable

from django.db import models
from django.template.defaultfilters import truncatechars
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

from nga.apps.budgets.enums import BudgetCalculationStatusEnum, BudgetCalculationTypeEnum
from nga.core.enums import get_choices


class BudgetCalculation(models.Model):
    """Describes and logs budget calculation process."""

    budget_snapshot = models.ForeignKey(
        "budgets.BudgetSnapshot",
        on_delete=models.CASCADE,
        related_name="budget_calculations",
        related_query_name="budget_calculation",
    )

    type = models.PositiveSmallIntegerField(
        _("Budget Calculation Type"),
        choices=get_choices(BudgetCalculationTypeEnum),
    )

    status = models.PositiveSmallIntegerField(
        _("Budget Calculation Status"),
        choices=get_choices(BudgetCalculationStatusEnum),
    )

    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.PROTECT,
        related_name="budget_calculations",
        related_query_name="budget_calculation",
        null=True,
        blank=True,
    )

    agreement = models.ForeignKey(
        "agreements.BudgetAgreement",
        on_delete=models.CASCADE,
        related_name="budget_calculations",
        related_query_name="budget_calculation",
        null=True,
        blank=True,
    )

    created_at = models.DateTimeField(_("Creation Date"), default=timezone.now)

    finished_at = models.DateTimeField(_("Finish Date"), null=True, blank=True)

    traffic_synchronized_at = models.DateTimeField(_("Traffic Sync Date"), null=True, blank=True)

    forecast_rules_applied_at = models.DateTimeField(_("Forecast Rules Apply Date"), null=True, blank=True)

    agreements_applied_at = models.DateTimeField(_("Agreements Apply Date"), null=True, blank=True)

    job_id = models.CharField(_("Job ID"), max_length=36, null=True, blank=True)

    budget_lhm = models.DateField(_("Budget LHM"), null=True, blank=True)

    traffic_lhm = models.DateField(_("Traffic LHM"), null=True, blank=True)

    distribution_lhm = models.DateField(_("Distribution LHM"), null=True, blank=True)

    class Meta:
        db_table = "budget_calculations"


class BudgetCalculationLogRecord(models.Model):
    calculation = models.ForeignKey(
        "budgets.BudgetCalculation",
        on_delete=models.CASCADE,
        related_name="log_records",
        related_query_name="log_record",
    )

    message = models.CharField(max_length=256)

    traceback = models.CharField(max_length=3000, null=True, blank=True)

    created_at = models.DateTimeField(_("Creation Date"), default=timezone.now)

    class Meta:
        db_table = "budget_calculation_log_records"

    def __str__(self) -> str:
        return f"{self.__class__.__name__} #{self.id}"

    def save(
        self,
        force_insert: bool = False,
        force_update: bool = False,
        using: str | None = None,
        update_fields: Iterable[str] | None = None,
    ) -> None:

        message_field = next(f for f in self._meta.fields if f.name == "message")
        traceback_field = next(f for f in self._meta.fields if f.name == "traceback")

        truncate_dots_length = 3

        if message_field.max_length <= len(self.message):
            self.message = truncatechars(self.message, message_field.max_length)[:-truncate_dots_length]

        if self.traceback is not None and traceback_field.max_length <= len(self.traceback):
            self.traceback = truncatechars(self.traceback, traceback_field.max_length)[:-truncate_dots_length]

        return super().save(
            force_insert=force_insert,
            force_update=force_update,
            using=using,
            update_fields=update_fields,
        )
