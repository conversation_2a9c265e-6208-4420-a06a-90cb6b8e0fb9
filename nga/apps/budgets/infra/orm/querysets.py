from typing import Optional

from django.db.models import Case, Expression, F, Q, QuerySet, Sum, Value, When

from nga.apps.budgets.domain.dto import BudgetTrafficParameters
from nga.apps.budgets.enums import BudgetTypeEnum
from nga.apps.budgets.infra.orm.models import Budget
from nga.apps.common.model_fields import Money<PERSON>ield
from nga.apps.common.queryset_utils import <PERSON><PERSON><PERSON><PERSON>, join_without_fk
from nga.apps.references.exceptions import ExchangeRateDoesNotExist
from nga.apps.references.infra.orm.models import ExchangeRate
from nga.core.enums import TrafficTypeEnum


class BudgetTrafficRecordQuerySet(QuerySet):
    """
    The main purposes for this overriding queryset:
        * Encapsulate Exchange Rate application logic in the database layer
        * Extract common filter queries
        * Extract common aggregation queries
    """

    def filter_by_parameters(self, parameters: BudgetTrafficParameters) -> QuerySet:
        qs = self.filter(budget_snapshot_id=parameters.snapshot_id)

        if parameters.home_operators:
            qs = qs.filter(home_operator_id__in=parameters.home_operators)

        # partner
        if parameters.partner_operators:
            qs = qs.filter(partner_operator_id__in=parameters.partner_operators)

        if parameters.partner_countries:
            qs = qs.filter(partner_operator__country_id__in=parameters.partner_countries)

        # period - traffic month
        if parameters.period:
            qs = qs.filter(traffic_month__range=parameters.period.period)
        elif parameters.traffic_months:
            qs = qs.filter(traffic_month__in=parameters.traffic_months)

        # traffic parameters
        if parameters.service_types:
            qs = qs.filter(service_type__in=parameters.service_types)

        if parameters.traffic_types:
            qs = qs.filter(traffic_type__in=parameters.traffic_types)

        if parameters.traffic_directions:
            qs = qs.filter(traffic_direction__in=parameters.traffic_directions)

        if parameters.traffic_segments is not None:
            qs = qs.filter(traffic_segment_id__in=parameters.traffic_segments)

        if parameters.imsi_count_types is not None:
            qs = qs.filter(imsi_count_type__in=parameters.imsi_count_types)

        if parameters.include_forecasted is False:
            qs = qs.exclude(traffic_type=TrafficTypeEnum.FORECASTED.value)

        # call destinations
        if parameters.call_destinations:
            qs = qs.filter(call_destination__in=parameters.call_destinations)

        if parameters.called_countries is not None:
            qs = qs.filter(called_country_id__in=parameters.called_countries)

        if parameters.premium is not None:
            is_premium_query = Q(is_premium__in=parameters.premium)

            if None in parameters.premium:
                is_premium_query |= Q(is_premium__isnull=True)

            qs = qs.filter(is_premium_query)

        # references
        if parameters.forecast_rule_ids is not None:
            qs = qs.filter(forecast_rule_id__in=parameters.forecast_rule_ids)

        if parameters.discount_ids is not None:
            qs = qs.filter(discount_id__in=parameters.discount_ids)

        return qs

    def apply_currency_exchange_rates(self, *, budget: Budget, currency_code: Optional[str]) -> QuerySet:
        """
        If currency_code is passed applies exchange rates to charge fields.
        As a result new fields with applied rate value will be added with "converted_" prefix:
            Current fields:
            - charge_net
            - charge_gross
            - tap_charge_gross
            - tap_charge_net

            New annotated fields with applied rate value:
            - converted_charge_net
            - converted_charge_gross
            - converted_tap_charge_gross
            - converted_tap_charge_net

        :return: QuerySet[BudgetTrafficRecord]
        """

        if currency_code is None:
            return self.annotate(
                converted_charge_net=F("charge_net"),
                converted_charge_gross=F("charge_gross"),
                converted_tap_charge_net=F("tap_charge_net"),
                converted_tap_charge_gross=F("tap_charge_gross"),
            )

        qs = join_without_fk(
            qs=self,
            with_model=ExchangeRate,
            on_fields=(("traffic_month", "date"),),
            on_extra_condition=Q(currency_code=currency_code),
        )
        qs = qs.annotate(exchange_rate_value=JoinedCol(ExchangeRate, "value", output_field=MoneyField()))

        if budget.type == BudgetTypeEnum.FROZEN:
            # for the FROZEN budget type we need to take exchange rate for LHM date for all forecasted records, but
            # historical records must be calculated based on rates for appropriate months

            try:
                exchange_rate = ExchangeRate.objects.get(currency_code=currency_code, date=budget.last_historical_month)
            except ExchangeRate.DoesNotExist:
                raise ExchangeRateDoesNotExist(currency_code, budget.last_historical_month)

            lhm_exchange_rate_value = Value(exchange_rate.value)

            def apply_exchange_rate(field: F) -> Expression:
                return Case(
                    When(traffic_type=TrafficTypeEnum.FORECASTED, then=field * lhm_exchange_rate_value),
                    default=field * F("exchange_rate_value"),
                )

            qs = qs.annotate(
                converted_charge_net=apply_exchange_rate(F("charge_net")),
                converted_charge_gross=apply_exchange_rate(F("charge_gross")),
                converted_tap_charge_net=apply_exchange_rate(F("tap_charge_net")),
                converted_tap_charge_gross=apply_exchange_rate(F("tap_charge_gross")),
            )

        else:
            qs = qs.annotate(
                converted_charge_net=F("charge_net") * F("exchange_rate_value"),
                converted_charge_gross=F("charge_gross") * F("exchange_rate_value"),
                converted_tap_charge_net=F("tap_charge_net") * F("exchange_rate_value"),
                converted_tap_charge_gross=F("tap_charge_gross") * F("exchange_rate_value"),
            )

        return qs

    def aggregate_charges(self, exchange_rates_applied: bool = False) -> QuerySet:
        """
        Performs group by for charge fields by applying Sum function.
        If exchange_rates_applied is True, then fields with "converted_" prefix will be used for aggregation.

        :param exchange_rates_applied: bool - marks whether queryset has fields where exchange rates are applied
        :return: QuerySet
        """

        if exchange_rates_applied is True:
            qs = self.annotate(
                charge_net=Sum("converted_charge_net"),
                charge_gross=Sum("converted_charge_gross"),
                tap_charge_net=Sum("converted_tap_charge_net"),
                tap_charge_gross=Sum("converted_tap_charge_gross"),
            )

        else:
            qs = self.annotate(
                charge_net=Sum("charge_net"),
                charge_gross=Sum("charge_gross"),
                tap_charge_net=Sum("tap_charge_net"),
                tap_charge_gross=Sum("tap_charge_gross"),
            )

        return qs

    def aggregate_volumes(self) -> QuerySet:
        """
        Performs group by for volume fields by applying Sum function.

        :return: QuerySet
        """

        return self.annotate(
            volume_actual=Sum("volume_actual"),
            volume_billed=Sum("volume_billed"),
        )
