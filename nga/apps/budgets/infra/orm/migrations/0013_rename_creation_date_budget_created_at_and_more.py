# Generated by Django 4.1.4 on 2023-04-04 15:08

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('budgets', '0012_budget_is_master_and_more'),
    ]

    operations = [
        migrations.RenameField(
            model_name='budget',
            old_name='creation_date',
            new_name='created_at',
        ),
        migrations.AddField(
            model_name='budget',
            name='traffic_updated_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='Last traffic updated date'),
        ),
        migrations.AddField(
            model_name='budget',
            name='updated_at',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='Last updated date'),
        ),
        migrations.AddField(
            model_name='budgettrafficrecord',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='Creation date'),
        ),
        migrations.AddField(
            model_name='budgettrafficrecord',
            name='updated_at',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='Last updated date'),
        ),
    ]
