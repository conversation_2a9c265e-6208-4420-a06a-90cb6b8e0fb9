# Generated by Django 4.1.2 on 2022-10-27 09:22

from django.db import migrations, models
import django.db.models.deletion
import nga.apps.common.model_fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('references', '0001_initial_squashed'),
    ]

    operations = [
        migrations.CreateModel(
            name='Budget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=248, verbose_name='Budget Name')),
                ('start_date', models.DateField(verbose_name='Budget Start Date')),
                ('end_date', models.DateField(verbose_name='Budget End Date')),
                ('creation_date', models.DateTimeField(auto_now_add=True, verbose_name='Budget Creation Date')),
                ('home_operators', models.ManyToManyField(related_name='budgets', related_query_name='budget', to='references.operator', verbose_name='Budget Home Operators')),
            ],
        ),
        migrations.CreateModel(
            name='BudgetValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('traffic_month', models.DateField(verbose_name='Traffic Month')),
                ('traffic_type', models.PositiveSmallIntegerField(choices=[(1, 'HISTORICAL'), (2, 'FORECASTED')], db_index=True, verbose_name='Traffic Type')),
                ('traffic_direction', models.PositiveSmallIntegerField(choices=[(1, 'INBOUND'), (2, 'OUTBOUND')], db_index=True, verbose_name='Traffic Direction')),
                ('service_type', models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA')], db_index=True, verbose_name='Service Type')),
                ('number_of_events', models.PositiveSmallIntegerField(verbose_name='Number of events')),
                ('volume', nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='Volume')),
                ('charge_original', nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='Charge Original')),
                ('charge_net', nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='Charge Net')),
                ('charge_gross', nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='Charge Gross')),
                ('budget', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='values', related_query_name='values', to='budgets.budget', verbose_name='Budget')),
                ('home_operator', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='home_budget_values', related_query_name='home_budget_values', to='references.operator', verbose_name='Home Operator')),
                ('partner_operator', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='partner_budget_values', related_query_name='partner_budget_values', to='references.operator', verbose_name='Partner Operator')),
            ],
        ),
    ]
