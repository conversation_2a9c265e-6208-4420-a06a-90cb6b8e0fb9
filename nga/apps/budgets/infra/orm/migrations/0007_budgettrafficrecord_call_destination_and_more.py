# Generated by Django 4.1.4 on 2023-03-02 15:43

from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion
import nga.apps.common.model_fields


class Migration(migrations.Migration):

    dependencies = [
        ('budgets', '0006_rename_volume_budgettrafficrecord_volume_actual'),
    ]

    operations = [
        migrations.AddField(
            model_name='budgettrafficrecord',
            name='call_destination',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(1, 'HOME'), (2, 'LOCAL'), (3, 'INTERNATIONAL')], null=True, verbose_name='Call Destination'),
        ),
        migrations.AddField(
            model_name='budgettrafficrecord',
            name='called_country',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='incoming_budget_traffic_records', related_query_name='incoming_budget_traffic_record', to='references.country', verbose_name='Called Country'),
        ),
        migrations.AddField(
            model_name='budgettrafficrecord',
            name='is_premium',
            field=models.BooleanField(blank=True, null=True, verbose_name='Is premium'),
        ),
        migrations.AddField(
            model_name='budgettrafficrecord',
            name='volume_billable',
            field=nga.apps.common.model_fields.MoneyField(decimal_places=6, default=Decimal('0'), max_digits=18, verbose_name='Volume Billable'),
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='service_type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA')], verbose_name='Service Type'),
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='traffic_direction',
            field=models.PositiveSmallIntegerField(choices=[(1, 'INBOUND'), (2, 'OUTBOUND')], verbose_name='Traffic Direction'),
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='traffic_type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'HISTORICAL'), (2, 'FORECASTED'), (3, 'FORECASTED_LIVE')], verbose_name='Traffic Type'),
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='volume_actual',
            field=nga.apps.common.model_fields.MoneyField(decimal_places=6, max_digits=18, verbose_name='Volume Actual'),
        ),
    ]
