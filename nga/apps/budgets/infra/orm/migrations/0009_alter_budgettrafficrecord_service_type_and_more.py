# Generated by Django 4.1.4 on 2023-03-03 14:38

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('budgets', '0008_remove_budgettrafficrecord_number_of_events'),
    ]

    operations = [
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='service_type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'VOICE_MO'), (2, 'VOICE_MT'), (3, 'SMS_MO'), (4, 'SMS_MT'), (5, 'DATA')], db_index=True, verbose_name='Service Type'),
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='traffic_type',
            field=models.PositiveSmallIntegerField(choices=[(1, 'HISTORICAL'), (2, 'FORECASTED'), (3, 'FORECASTED_LIVE')], db_index=True, verbose_name='Traffic Type'),
        ),
    ]
