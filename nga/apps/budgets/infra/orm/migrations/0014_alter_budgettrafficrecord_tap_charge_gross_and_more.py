# Generated by Django 4.2 on 2023-05-10 11:01

from django.db import migrations
import nga.apps.common.model_fields


class Migration(migrations.Migration):
    dependencies = [
        ("budgets", "0013_rename_creation_date_budget_created_at_and_more"),
    ]

    operations = [
        migrations.AlterField(
            model_name="budgettrafficrecord",
            name="tap_charge_gross",
            field=nga.apps.common.model_fields.MoneyField(
                decimal_places=6, max_digits=18, verbose_name="TAP Charge Gross"
            ),
        ),
        migrations.AlterField(
            model_name="budgettrafficrecord",
            name="tap_charge_net",
            field=nga.apps.common.model_fields.MoneyField(
                decimal_places=6, max_digits=18, verbose_name="TAP Charge Net"
            ),
        ),
        migrations.AlterField(
            model_name="budgettrafficrecord",
            name="volume_billable",
            field=nga.apps.common.model_fields.MoneyField(
                decimal_places=6, max_digits=18, verbose_name="Volume Billable"
            ),
        ),
    ]
