# Generated by Django 4.1.4 on 2023-01-30 09:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('forecasts', '0006_alter_forecastrule_budget'),
        ('budgets', '0002_budgetvalue_forecast_rule'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='BudgetValue',
            new_name='BudgetTrafficRecord',
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='budget',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='traffic_records',
                related_query_name='traffic_record',
                to='budgets.budget',
                verbose_name='Budget'
            ),
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='forecast_rule',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='traffic_records',
                related_query_name='traffic_record',
                to='forecasts.forecastrule'
            ),
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='home_operator',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='home_traffic_records',
                related_query_name='home_traffic_record',
                to='references.operator',
                verbose_name='Home Operator'
            ),
        ),
        migrations.AlterField(
            model_name='budgettrafficrecord',
            name='partner_operator',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='partner_traffic_records',
                related_query_name='partner_traffic_record',
                to='references.operator',
                verbose_name='Partner Operator'
            ),
        ),
    ]
