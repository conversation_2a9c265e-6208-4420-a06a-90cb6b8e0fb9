from datetime import datetime

from dependency_injector.wiring import Closing, Provide, inject
from mediatr import Mediator

from nga.apps.budgets.commands import DeleteBudgetCommand
from nga.apps.budgets.domain.repositories import AbstractBudgetRepository
from nga.apps.common.consts import DATE_FORMAT
from nga.apps.references.mapped_exchange_rate_service import create_mapped_exchange_rates_after_budget_created
from nga.infra import celery_app


@celery_app.task
def create_mapped_exchange_rates_after_budget_created_task(create_till_date_str: str) -> None:
    create_till_date = datetime.strptime(create_till_date_str, DATE_FORMAT).date()

    create_mapped_exchange_rates_after_budget_created(create_till_date)


@celery_app.task
@inject
def delete_budget_task(
    budget_id: int,
    mediator: Mediator = Provide["mediator"],
    budget_repository: AbstractBudgetRepository = Closing[Provide["budget_repository"]],
) -> None:
    """Deletes budget with its components from the storage."""

    budget = budget_repository.get_by_id(budget_id=budget_id)

    delete_budget_cmd = DeleteBudgetCommand(budget=budget)

    mediator.send(delete_budget_cmd)
