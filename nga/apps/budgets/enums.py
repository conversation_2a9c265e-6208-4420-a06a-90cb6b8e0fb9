from enum import Enum, IntEnum

from nga.core.enums import TrafficDirectionEnum

__all__ = [
    "BudgetCalculationStatusEnum",
    "BudgetCalculationTypeEnum",
    "BudgetSnapshotTypeEnum",
    "BudgetTypeEnum",
    "ChargeTypeEnum",
    "UnitTypeEnum",
    "UnitDirectionTypeEnum",
    "ReportingAllowedColumnsEnum",
]


class ChargeTypeEnum(str, Enum):
    NET = "NET"
    GROSS = "GROSS"
    TAP_NET = "TAP_NET"
    TAP_GROSS = "TAP_GROSS"


class UnitTypeEnum(str, Enum):
    VOLUME = "VOLUME"
    CHARGE = "CHARGE"


class UnitDirectionTypeEnum(str, Enum):
    INBOUND = TrafficDirectionEnum.INBOUND.name
    OUTBOUND = TrafficDirectionEnum.OUTBOUND.name
    NET_POSITION = "NET_POSITION"


class BudgetSnapshotTypeEnum(IntEnum):
    ACTIVE = 1
    CALCULATION = 2
    HISTORICAL = 3


class BudgetCalculationStatusEnum(IntEnum):
    WAITING_FOR_START = 1
    STARTED = 2
    HISTORICAL_TRAFFIC_SYNCHRONIZATION = 3
    FORECAST_RULES_APPLICATION = 4
    AGREEMENTS_APPLICATION = 5
    IS_FINISHING = 6
    CALCULATED = 7
    FAILED = 8
    EXTERNAL_CALCULATION_RESULTS_APPLICATION = 9


class BudgetCalculationTypeEnum(IntEnum):
    FULL_WITH_TRAFFIC_UPDATE = 1
    FULL_WITHOUT_TRAFFIC_UPDATE = 2
    ONLY_MODIFIED_AGREEMENTS = 3
    SINGLE_AGREEMENT = 4


class BudgetTypeEnum(IntEnum):
    MASTER = 1
    FROZEN = 2
    UPDATED = 3
    FROZEN_TRAFFIC = 4

    @classmethod
    def get_editable_types(cls) -> list["BudgetTypeEnum"]:
        return list(set(cls) - {cls.MASTER})


class ReportingAllowedColumnsEnum(str, Enum):
    HOME_OPERATOR_PMN = "home_operator_pmn"
    PARTNER_OPERATOR_PMN = "partner_operator_pmn"
    PARTNER_COUNTRY = "partner_country"
    TRAFFIC_MONTH = "traffic_month"
    TRAFFIC_TYPE = "traffic_type"
    TRAFFIC_DIRECTION = "traffic_direction"
    SERVICE_TYPE = "service_type"
    CALL_DESTINATION = "call_destination"
    CALLED_COUNTRY = "called_country"
    IS_PREMIUM = "is_premium"
    TRAFFIC_SEGMENT_NAME = "traffic_segment_name"
    IMSI_COUNT_TYPE = "imsi_count_type"
    VOLUME_ACTUAL = "volume_actual"
    VOLUME_BILLED = "volume_billed"
    TAP_CHARGE_NET = "tap_charge_net"
    TAP_CHARGE_GROSS = "tap_charge_gross"
    CHARGE_NET = "charge_net"
    CHARGE_GROSS = "charge_gross"

    # Calculated fields
    DISCOUNT_NET = "discount_net"
    DISCOUNT_GROSS = "discount_gross"
    TAP_RATE_NET_VOLUME_ACTUAL = "tap_rate_net_volume_actual"
    TAP_RATE_GROSS_VOLUME_ACTUAL = "tap_rate_gross_volume_actual"
    DISCOUNTED_RATE_NET_VOLUME_ACTUAL = "discounted_rate_net_volume_actual"
    DISCOUNTED_RATE_GROSS_VOLUME_ACTUAL = "discounted_rate_gross_volume_actual"
    TAP_RATE_NET_VOLUME_BILLED = "tap_rate_net_volume_billed"
    TAP_RATE_GROSS_VOLUME_BILLED = "tap_rate_gross_volume_billed"
    DISCOUNTED_RATE_NET_VOLUME_BILLED = "discounted_rate_net_volume_billed"
    DISCOUNTED_RATE_GROSS_VOLUME_BILLED = "discounted_rate_gross_volume_billed"
