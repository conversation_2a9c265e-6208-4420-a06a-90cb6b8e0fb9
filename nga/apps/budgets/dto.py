from dataclasses import dataclass
from decimal import Decimal
from typing import Optional

from nga.apps.budgets.analytics.traffic_evolution import TrafficEvolutionRecord


# partner operator shares
@dataclass
class PartnerOperatorOverallShareValues:
    operator_id: int

    lhm_share: Optional[Decimal]
    actual_share: Optional[Decimal]
    retrospective_share: Optional[Decimal]


@dataclass
class PartnerCountryOperatorOverallShares:
    country_id: int

    operator_shares: tuple[PartnerOperatorOverallShareValues, ...]


@dataclass
class BudgetPartnerCountryOperatorShares:
    budget_id: int

    country_operator_shares: tuple[PartnerCountryOperatorOverallShares, ...]


# traffic evolution
@dataclass
class TrafficEvolutionResponse:
    budget_id: int

    agreement_id: Optional[int]

    historical_records: tuple["TrafficEvolutionRecord", ...]

    forecasted_records: tuple["TrafficEvolutionRecord", ...]
