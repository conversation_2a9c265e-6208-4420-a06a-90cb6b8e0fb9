from decimal import Decimal
from typing import Optional

from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.enums import DiscountBoundTypeEnum, DiscountCalculationTypeEnum


class DiscountFinancialThresholdFiller:
    def __init__(self, discount_repository: AbstractDiscountRepository) -> None:
        self._discount_repository = discount_repository

    def fill_financial_threshold(self, discount: Discount) -> None:
        """
        Finds all FINANCIAL_THRESHOLD discounts and:
        - Extracts lower_bound
        - Applies it to matching SEND_OR_PAY_FINANCIAL discounts with the same traffic
        - Deletes all FINANCIAL_THRESHOLD discounts afterward
        """

        for sub_discount in discount.sub_discounts:

            for discount_parameter in sub_discount.parameters:

                calculation_type_is_financial_threshold = (
                    discount_parameter.calculation_type == DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD
                )
                bound_is_financial_threshold = (
                    discount_parameter.bound_type == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD
                )

                if calculation_type_is_financial_threshold and bound_is_financial_threshold:
                    self._apply_financial_threshold_to_matching_discounts(
                        lower_bound_value=discount_parameter.lower_bound,
                        base_sub_discount=sub_discount,
                        parent_discount=discount,
                    )
                    self._discount_repository.delete_by_id(sub_discount.id)

    def _apply_financial_threshold_to_matching_discounts(
        self,
        lower_bound_value: Optional[Decimal],
        base_sub_discount: Discount,
        parent_discount: Discount,
    ) -> None:

        if self._matches_traffic_fields(base_sub_discount, parent_discount):
            parent_discount.financial_threshold = lower_bound_value
            self._discount_repository.save(parent_discount)

    def _matches_traffic_fields(self, base: Discount, other: Discount) -> bool:
        return (
            base.agreement_id == other.agreement_id
            and base.home_operators == other.home_operators
            and base.partner_operators == other.partner_operators
            and base.direction == other.direction
            and set(base.service_types) == set(other.service_types)
            and base.period == other.period
            and base.traffic_segments == other.traffic_segments
        )
