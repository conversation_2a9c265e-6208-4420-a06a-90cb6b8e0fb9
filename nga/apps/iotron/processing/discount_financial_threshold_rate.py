from decimal import Decimal
from typing import Optional

from nga.apps.agreements.domain.discount_model_type import evaluate_discount_model_type
from nga.apps.agreements.domain.mappers import from_discount_parameter_to_dto
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.enums import DiscountBoundTypeEnum, DiscountCalculationTypeEnum


class DiscountFinancialThresholdRateFiller:
    def __init__(self, discount_repository: AbstractDiscountRepository) -> None:
        self._discount_repository = discount_repository

    def fill_financial_threshold_rate(self, discount: Discount) -> None:
        """
        Finds all SEND_OR_PAY_FINANCIAL discounts and:
        - Extracts above_financial_threshold_rate
        - Applies it to matching SEND_OR_PAY_FINANCIAL discounts with the same traffic
        """
        sub_discount_ids_for_delete = []

        for sub_discount in discount.sub_discounts:

            valid_parameters = []

            for discount_parameter in sub_discount.parameters:

                calculation_type_is_stepped_tiered = (
                    discount_parameter.calculation_type == DiscountCalculationTypeEnum.STEPPED_TIERED
                )
                bound_is_financial_threshold = (
                    discount_parameter.bound_type == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD
                )

                if calculation_type_is_stepped_tiered and bound_is_financial_threshold:
                    self._apply_financial_threshold_rate_to_matching_discounts(
                        above_financial_threshold_rate=discount_parameter.basis_value,
                        base_sub_discount=sub_discount,
                        potential_similar_sub_discounts=list(discount.sub_discounts),
                    )
                else:
                    valid_parameters.append(discount_parameter)

            if len(valid_parameters) == 0:
                sub_discount_ids_for_delete.append(sub_discount.id)
                continue

            if len(valid_parameters) != len(sub_discount.parameters):
                sub_discount.parameters = tuple(valid_parameters)

                self._discount_repository.set_parameters(
                    discount_id=sub_discount.id,
                    parameters_dtos=tuple(from_discount_parameter_to_dto(p) for p in valid_parameters),
                )

                sub_discount.model_type = evaluate_discount_model_type(sub_discount)

                self._discount_repository.save(sub_discount)

        for sub_discount_id in sub_discount_ids_for_delete:
            self._discount_repository.delete_by_id(sub_discount_id)

    def _apply_financial_threshold_rate_to_matching_discounts(
        self,
        above_financial_threshold_rate: Optional[Decimal],
        base_sub_discount: Discount,
        potential_similar_sub_discounts: list[Discount],
    ) -> None:

        for sub_discount in potential_similar_sub_discounts:
            if self._matches_traffic_fields(base_sub_discount, sub_discount):
                sub_discount.above_financial_threshold_rate = above_financial_threshold_rate
                self._discount_repository.save(sub_discount)

    def _matches_traffic_fields(self, base: Discount, other: Discount) -> bool:
        return (
            base.agreement_id == other.agreement_id
            and base.home_operators == other.home_operators
            and base.partner_operators == other.partner_operators
            and base.direction == other.direction
            and set(base.service_types) == set(other.service_types)
            and base.period == other.period
            and base.traffic_segments == other.traffic_segments
        )
