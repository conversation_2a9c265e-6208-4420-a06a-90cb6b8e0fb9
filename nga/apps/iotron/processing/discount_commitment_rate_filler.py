from decimal import Decimal
from typing import Optional

from nga.apps.agreements.domain.discount_model_type import evaluate_discount_model_type
from nga.apps.agreements.domain.mappers import from_discount_parameter_to_dto
from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.domain.repositories import AbstractDiscountRepository
from nga.apps.agreements.enums import DiscountBoundTypeEnum, DiscountCalculationTypeEnum


class DiscountAboveCommitmentRateFiller:
    def __init__(self, discount_repository: AbstractDiscountRepository) -> None:
        self._discount_repository = discount_repository

    def fill_above_commitment_rate(self, discount: Discount) -> None:
        sub_discount_ids_for_delete = []

        for sub_discount in discount.sub_discounts:
            valid_parameters = []

            for discount_parameter in sub_discount.parameters:
                is_stepped_tiered = discount_parameter.calculation_type == DiscountCalculationTypeEnum.STEPPED_TIERED

                bound_is_financial = discount_parameter.bound_type == DiscountBoundTypeEnum.FINANCIAL_COMMITMENT

                if is_stepped_tiered and bound_is_financial:
                    self._set_above_commitment_date_for_similar_sub_discounts(
                        discount_parameter.basis_value,
                        sub_discount,
                        discount.sub_discounts,
                    )

                else:
                    valid_parameters.append(discount_parameter)

            if len(valid_parameters) == 0:
                sub_discount_ids_for_delete.append(sub_discount.id)
                continue

            if len(valid_parameters) != len(sub_discount.parameters):
                sub_discount.parameters = tuple(valid_parameters)

                self._discount_repository.set_parameters(
                    discount_id=sub_discount.id,
                    parameters_dtos=tuple(from_discount_parameter_to_dto(p) for p in valid_parameters),
                )

                sub_discount.model_type = evaluate_discount_model_type(sub_discount)

                self._discount_repository.save(sub_discount)

        for sub_discount_id in sub_discount_ids_for_delete:
            self._discount_repository.delete_by_id(sub_discount_id)

    def _set_above_commitment_date_for_similar_sub_discounts(
        self,
        above_commitment_rate: Optional[Decimal],
        base_sub_discount: Discount,
        potential_similar_sub_discounts: tuple[Discount, ...],
    ) -> None:
        for sub_discount in potential_similar_sub_discounts:
            if base_sub_discount.equal_by_traffic(sub_discount):
                sub_discount.above_commitment_rate = above_commitment_rate
                self._discount_repository.save(sub_discount)
