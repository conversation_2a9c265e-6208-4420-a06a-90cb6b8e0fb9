from typing import ClassVar

from admin_auto_filters.filters import AutocompleteFilter
from django.contrib import admin
from django.db.models import QuerySet
from django.db.models.base import Model
from django.urls import reverse
from more_admin_filters import MultiSelectDropdownFilter, MultiSelectFilter

from nga.apps.admin_site.autocomplete_modules import ModelAutocompleteFilter
from nga.apps.agreements.enums import DiscountDirectionEnum
from nga.apps.agreements.infra.orm import models as agreement_models
from nga.apps.budgets.infra.orm import models as budget_models
from nga.core.enums import TrafficDirectionEnum


class BaseAutocompleteFilter(AutocompleteFilter):
    ordering_fields: ClassVar[tuple[str, ...]] = ("id",)

    def get_queryset_for_field(self, model: Model, name: str) -> QuerySet:
        queryset = super().get_queryset_for_field(model, name)

        return queryset.order_by(*self.ordering_fields)


class BudgetAutocompleteFilter(BaseAutocompleteFilter):
    title = "Budget"
    field_name = "budget"


class BudgetsAutocompleteFilter(BudgetAutocompleteFilter):
    field_name = "budgets"


class BudgetSubSnapshotAutocompleteFilter(BudgetAutocompleteFilter):
    field_name = "budget"
    parameter_name = "budget_snapshot__budget"
    rel_model = budget_models.BudgetSnapshot


class BudgetSnapshotAutocompleteFilter(BaseAutocompleteFilter):
    title = "Budget Snapshot"
    field_name = "budget_snapshot"
    ordering_fields = ("budget_id",)


class HomeOperatorAutocompleteFilter(BaseAutocompleteFilter):
    title = "Home Operator"
    field_name = "home_operator"


class PartnerOperatorAutocompleteFilter(BaseAutocompleteFilter):
    title = "Partner Operator"
    field_name = "partner_operator"


class DiscountAutocompleteFilter(BaseAutocompleteFilter):
    title = "Discount"
    field_name = "discount"


class DiscountParentAutocompleteFilter(BaseAutocompleteFilter):
    title = "Parent"
    field_name = "parent"


class AgreementAutocompleteFilter(BaseAutocompleteFilter):
    title = "Agreement"
    field_name = "agreement"


class ForecastRuleFilter(BaseAutocompleteFilter):
    title = "Forecast Rule"
    field_name = "forecast_rule"


class TrafficSegmentAutocompleteFilter(BaseAutocompleteFilter):
    title = "Traffic Segment"
    field_name = "traffic_segment"


class CreatedByAutocompleteFilter(BaseAutocompleteFilter):
    title = "Created By"
    field_name = "created_by"


class ChangedByAutocompleteFilter(BaseAutocompleteFilter):
    title = "Changed By"
    field_name = "changed_by"


class MultiChoicesSelectFilter(MultiSelectFilter):
    """
    Multi select filter for all kind of fields.
    """

    def __init__(self, field, request, params, model, model_admin, field_path):  # type: ignore[no-untyped-def]
        super().__init__(field, request, params, model, model_admin, field_path)
        labels_choices_map = {label: value for value, label in field.choices}
        self.lookup_choices = [label for value, label in field.choices]

        self.used_parameters = {
            key: [labels_choices_map[value] for value in values]
            for key, values in self.used_parameters.items()  # type: ignore[has-type]
        }


class BaseMultiSelectDropdownFilter(MultiSelectDropdownFilter):
    TITLE: ClassVar[str]

    def __init__(self, field, request, params, model, model_admin, field_path):  # type: ignore[no-untyped-def]
        super().__init__(field, request, params, model, model_admin, field_path)

        self.title = self.TITLE


class HomeOperatorMultiSelectDropdownFilter(BaseMultiSelectDropdownFilter):
    TITLE = "Home Operator PMN Code"


class PartnerOperatorMultiSelectDropdownFilter(BaseMultiSelectDropdownFilter):
    TITLE = "Partner Operator PMN Code"


class CalledCountryCodeMultiSelectDropdownFilter(BaseMultiSelectDropdownFilter):
    TITLE = "Called Country Code"


class PartnerCountryCodeMultiSelectDropdownFilter(BaseMultiSelectDropdownFilter):
    TITLE = "Partner Country Code"


class AgreementModelAutocompleteFilter(ModelAutocompleteFilter):
    title = "Agreement Custom"
    parameter_name = "agreement_custom"
    rel_model = agreement_models.Agreement

    mapper_fields = [
        ("traffic_month__gte", lambda x: getattr(x, "start_date")),
        ("traffic_month__lte", lambda x: getattr(x, "end_date")),
        ("home_operator_id__in", lambda x: [h_op.id for h_op in getattr(x, "home_operators").all()]),
        ("partner_operator_id__in", lambda x: [p_op.id for p_op in getattr(x, "partner_operators").all()]),
    ]

    def get_autocomplete_url(self, request: QuerySet, model_admin: admin.ModelAdmin) -> str:
        return reverse("admin:agreement_filter")


class DiscountModelAutocompleteFilter(ModelAutocompleteFilter):
    title = "Discount Custom"
    parameter_name = "discount_custom"
    rel_model = agreement_models.Discount

    mapper_fields = [
        ("traffic_month__gte", lambda x: getattr(x, "start_date")),
        ("traffic_month__lte", lambda x: getattr(x, "end_date")),
        ("home_operator_id__in", lambda x: [h_op.id for h_op in getattr(x, "home_operators").all()]),
        ("partner_operator_id__in", lambda x: [p_op.id for p_op in getattr(x, "partner_operators").all()]),
        (
            "traffic_direction__in",
            lambda x: (
                [TrafficDirectionEnum.INBOUND.value, TrafficDirectionEnum.OUTBOUND.value]
                if getattr(x, "direction") == DiscountDirectionEnum.BIDIRECTIONAL.value
                else [getattr(x, "direction")]
            ),
        ),
        ("service_type__in", lambda x: getattr(x, "service_types")),
        ("call_destination__in", lambda x: getattr(x, "call_destinations") or []),
        ("called_country_id__in", lambda x: [c.id for c in getattr(x, "called_countries").all()]),
        ("traffic_segment_id__in", lambda x: [ts.id for ts in getattr(x, "traffic_segments").all()]),
    ]

    def get_autocomplete_url(self, request: QuerySet, model_admin: admin.ModelAdmin) -> str:
        return reverse("admin:discount_filter")
