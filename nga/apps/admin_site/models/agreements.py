from django import forms
from django.contrib import admin
from django.db.models import F, QuerySet, Value
from django.db.models.functions import Coalesce
from django.http import HttpRequest
from django.urls import reverse
from django.utils.html import format_html
from more_admin_filters import ChoicesDropdownFilter
from rangefilter.filters import DateRangeFilter

from nga.apps.admin_site.filters import (
    AgreementAutocompleteFilter,
    BudgetAutocompleteFilter,
    BudgetsAutocompleteFilter,
    CalledCountryCodeMultiSelectDropdownFilter,
    ChangedByAutocompleteFilter,
    DiscountAutocompleteFilter,
    DiscountParentAutocompleteFilter,
    HomeOperatorMultiSelectDropdownFilter,
    PartnerOperatorMultiSelectDropdownFilter,
)
from nga.apps.admin_site.form_fields import EnumMultipleChoiceField
from nga.apps.admin_site.mixins import BothOperatorsFieldsMixin
from nga.apps.admin_site.utils import truncate_operators_to_str, truncate_sequence_of_string
from nga.apps.agreements.infra.orm import models
from nga.apps.common.expressions import called_country_names, traffic_segment_names
from nga.core.enums import CallDestinationEnum, ServiceTypeEnum


class BudgetAgreementInline(admin.TabularInline):
    model = models.BudgetAgreement
    extra = 1
    autocomplete_fields = ("budget",)


class AgreementNoteInline(admin.TabularInline):
    verbose_name = "Note"

    model = models.AgreementNote

    can_delete = False

    ordering = ("-created_at",)

    readonly_fields = (
        "type",
        "user",
        "content",
        "created_at",
        "updated_at",
    )

    def has_add_permission(self, request: HttpRequest, obj: models.AgreementNote) -> bool:  # pragma: no cover
        return False


class AgreementStatusChangeLogInline(admin.TabularInline):
    model = models.AgreementStatusChangeLog

    verbose_name = "Status Change Logs"

    can_delete = False

    ordering = ("-changed_at",)

    readonly_fields = (
        "agreement_id",
        "old_status",
        "new_status",
        "changed_by__email",
        "changed_at",
    )

    def has_add_permission(self, request: HttpRequest, obj: models.AgreementNote) -> bool:  # pragma: no cover
        return False


@admin.register(models.Agreement)
class AgreementAdmin(BothOperatorsFieldsMixin, admin.ModelAdmin):
    autocomplete_fields = (
        "home_operators",
        "partner_operators",
        "parent",
        "negotiator",
    )
    list_display = (
        "pk",
        "name",
        "external_id",
        "status",
        "home_operator_list",
        "partner_operator_list",
        "start_date",
        "end_date",
        "negotiator",
        "include_satellite",
        "include_premium",
        "include_premium_in_commitment",
        "is_rolling",
        "created_at",
        "updated_at",
    )
    sortable_by = list_display
    list_display_links = ("name",)
    list_filter = (
        "status",
        ("start_date", DateRangeFilter),
        ("end_date", DateRangeFilter),
        BudgetsAutocompleteFilter,
        ("home_operators__pmn_code", HomeOperatorMultiSelectDropdownFilter),
        ("partner_operators__pmn_code", PartnerOperatorMultiSelectDropdownFilter),
        "include_satellite",
        "include_premium",
        "include_premium_in_commitment",
        "is_rolling",
    )
    search_fields = (
        "pk",
        "external_id",
        "name",
        "home_operators__pmn_code",
        "partner_operators__pmn_code",
    )
    inlines = (
        BudgetAgreementInline,
        AgreementNoteInline,
        AgreementStatusChangeLogInline,
    )

    show_facets = admin.ShowFacets.NEVER

    @classmethod
    def agreement_home_operators(cls, obj: models.Agreement) -> str:
        display_value = truncate_operators_to_str(obj.home_operators.all())
        return display_value

    @classmethod
    def agreement_partner_operators(cls, obj: models.Agreement) -> str:
        display_value = truncate_operators_to_str(obj.partner_operators.all())
        return display_value


@admin.register(models.AgreementStatusChangeLog)
class AgreementStatusChangeLogAdmin(admin.ModelAdmin):
    list_display = (
        "pk",
        "agreement_link",
        "old_status",
        "new_status",
        "changed_by_link",
        "changed_at",
    )

    list_filter = (
        AgreementAutocompleteFilter,
        ChangedByAutocompleteFilter,
    )

    ordering = ("-changed_at",)

    readonly_fields = (
        "pk",
        "agreement",
        "old_status",
        "new_status",
        "changed_by",
        "changed_at",
    )

    def agreement_link(self, obj: models.AgreementStatusChangeLog) -> str:
        link = reverse("admin:agreements_agreement_change", args=[obj.agreement_id])

        return format_html('<a href="{}">{}</a>', link, obj.agreement_id)

    def changed_by_link(self, obj: models.AgreementStatusChangeLog) -> str:
        link = reverse("admin:users_user_change", args=[obj.changed_by_id])

        return format_html('<a href="{}">{}</a>', link, obj.changed_by.email)

    def get_queryset(self, request: HttpRequest) -> QuerySet[models.AgreementStatusChangeLog]:
        qs = super().get_queryset(request)

        qs = qs.select_related("agreement", "changed_by")

        return qs


@admin.register(models.BudgetAgreement)
class BudgetAgreementAdminModel(admin.ModelAdmin):
    autocomplete_fields = (
        "budget",
        "agreement",
    )

    list_display = (
        "pk",
        "budget_id",
        "agreement_id",
        "is_active",
        "calculation_status",
        "applied_at",
        "agreement_url",
    )
    sortable_by = list_display

    list_filter = (
        BudgetAutocompleteFilter,
        AgreementAutocompleteFilter,
        "is_active",
        "calculation_status",
    )

    search_fields = (
        "pk",
        "agreement__pk",
    )

    readonly_fields = (
        "applied_at",
        "calculation_status",
    )

    show_facets = admin.ShowFacets.NEVER

    @classmethod
    def agreement_url(cls, obj: models.BudgetAgreement) -> str:
        url = reverse("admin:agreements_agreement_change", kwargs=dict(object_id=obj.agreement_id))
        return format_html(f'<a href="{url}">View Agreement</a>')


class DiscountParameterInline(admin.StackedInline):
    model = models.DiscountParameter
    extra = 1
    show_change_link = True


class DiscountAdminForm(forms.ModelForm):
    class Meta:
        model = models.Discount
        fields = "__all__"

    service_types = EnumMultipleChoiceField(enum_class=ServiceTypeEnum)
    call_destinations = EnumMultipleChoiceField(enum_class=CallDestinationEnum, required=False)
    qualifying_service_types = EnumMultipleChoiceField(enum_class=ServiceTypeEnum, required=False)


@admin.register(models.Discount)
class DiscountAdminModel(BothOperatorsFieldsMixin, admin.ModelAdmin):
    form = DiscountAdminForm

    list_display = (
        "pk",
        "agreement",
        "home_operator_list",
        "partner_operator_list",
        "direction",
        "service_type_list",
        "start_date",
        "end_date",
        "model_type",
        "currency_code",
        "tax_type",
        "volume_type",
        "settlement_method",
        "call_destination_list",
        "called_country_list",
        "traffic_segment_list",
        "imsi_count_type",
        "qualifying_direction",
        "qualifying_service_type_list",
        "qualifying_basis",
        "qualifying_lower_bound",
        "qualifying_upper_bound",
        "inbound_market_share",
    )
    sortable_by = list_display
    list_filter = (
        AgreementAutocompleteFilter,
        DiscountParentAutocompleteFilter,
        ("home_operators__pmn_code", HomeOperatorMultiSelectDropdownFilter),
        ("partner_operators__pmn_code", PartnerOperatorMultiSelectDropdownFilter),
        "direction",
        # TODO:
        #  1. service_type
        #  2. call_destinations
        #  3. qualifying_service_type
        ("start_date", DateRangeFilter),
        ("end_date", DateRangeFilter),
        ("model_type", ChoicesDropdownFilter),
        "imsi_count_type",
        "tax_type",
        "volume_type",
        "settlement_method",
        ("called_countries__code", CalledCountryCodeMultiSelectDropdownFilter),
        "qualifying_direction",
        "qualifying_basis",
    )

    list_display_links = ("pk", "agreement")

    list_per_page = 22

    autocomplete_fields = (
        "agreement",
        "parent",
        "home_operators",
        "partner_operators",
        "called_countries",
        "traffic_segments",
    )

    search_fields = (
        "pk",
        "agreement__name",
        "agreement__id",
    )

    inlines = (DiscountParameterInline,)

    readonly_fields = (
        "model_type",
        "created_at",
        "updated_at",
    )

    show_facets = admin.ShowFacets.NEVER

    def get_queryset(self, request: HttpRequest) -> QuerySet[models.Discount]:
        qs = super().get_queryset(request)

        qs = qs.annotate(traffic_segment_names=traffic_segment_names)
        qs = qs.annotate(called_country_names=called_country_names)

        # Change null values with empty list for correct field ordering
        qs = qs.annotate(qualifying_service_type_ids=Coalesce(F("qualifying_service_types"), Value([])))
        qs = qs.annotate(call_destination_ids=Coalesce(F("call_destinations"), Value([])))

        qs = qs.select_related("agreement", "parent")

        qs = qs.prefetch_related(
            "home_operators",
            "partner_operators",
            "called_countries",
            "traffic_segments",
        )

        return qs

    @admin.display(ordering="service_types")
    def service_type_list(self, obj: models.Discount) -> str:
        return ", ".join(obj.get_service_types_display())

    @admin.display(ordering="call_destination_ids")
    def call_destination_list(self, obj: models.Discount) -> str:
        return ", ".join(obj.get_call_destinations_display())

    @admin.display(ordering="called_country_names")
    def called_country_list(self, obj: models.Discount) -> str:
        called_countries: str = obj.called_country_names

        return truncate_sequence_of_string(called_countries)

    @admin.display(ordering="traffic_segment_names")
    def traffic_segment_list(self, obj: models.Discount) -> str:
        traffic_segments: str = obj.traffic_segment_names

        return truncate_sequence_of_string(traffic_segments)

    @admin.display(ordering="qualifying_service_type_ids")
    def qualifying_service_type_list(self, obj: models.Discount) -> str:
        return ", ".join(obj.get_qualifying_service_types_display())


@admin.register(models.DiscountParameter)
class DiscountParameterAdminModel(admin.ModelAdmin):
    list_display = (
        "pk",
        "discount",
        "calculation_type",
        "basis",
        "basis_value",
        "balancing",
        "bound_type",
        "lower_bound",
        "upper_bound",
        "toll_rate",
        "airtime_rate",
        "fair_usage_rate",
        "fair_usage_threshold",
        "access_fee_rate",
        "incremental_rate",
    )
    sortable_by = list_display
    list_filter = (
        DiscountAutocompleteFilter,
        "calculation_type",
        "basis",
        "balancing",
        "bound_type",
    )

    list_display_links = ("pk", "discount")

    list_per_page = 22

    autocomplete_fields = ("discount",)

    search_fields = ("pk",)

    readonly_fields = (
        "created_at",
        "updated_at",
    )

    show_facets = admin.ShowFacets.NEVER

    def get_queryset(self, request: HttpRequest) -> QuerySet[models.DiscountParameter]:
        qs = super().get_queryset(request)

        qs = qs.select_related("discount")

        return qs


@admin.register(models.AgreementNegotiator)
class AgreementNegotiatorAdmin(admin.ModelAdmin):
    list_display = ("pk", "name")
    search_fields = ("name",)
    sortable_by = list_display
