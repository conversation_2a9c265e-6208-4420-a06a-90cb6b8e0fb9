from typing import Type

from celery import chain, shared_task
from dependency_injector.wiring import Closing, Provide, inject
from import_export.resources import ModelResource

from nga.apps.import_export_data import consts
from nga.apps.import_export_data.domain.factories import ImportDataServiceFactory
from nga.apps.import_export_data.enums import EntityTypeEnum
from nga.apps.import_export_data.models import ImportDataJob
from nga.apps.import_export_data.resources.iotron import ExternalAgreementResource
from nga.apps.import_export_data.resources.traffic import (
    MonthlyAggregatedTrafficRecordIOTRONResource,
)
from nga.apps.import_export_data.tasks.data_sync import run_import_data_job
from nga.apps.import_export_data.utils import create_csv_file
from nga.apps.references.infra.orm.models import HomeOperator


def sync_iotron_master_budget_data() -> chain:
    """Runs synchronization of traffic and budget components from IOTRON."""

    sync_chain = sync_monthly_aggregated_traffic.si() | sync_external_agreements.si()

    return sync_chain


@shared_task()
def sync_monthly_aggregated_traffic() -> None:
    data_job = create_auto_import_data_job(
        model=consts.MONTHLY_AGGREGATED_TRAFFIC_RECORD_IOTRON,
        entity_type=EntityTypeEnum.HISTORICAL_MONTHLY_AGGREGATIONS,
        model_resource=MonthlyAggregatedTrafficRecordIOTRONResource,
    )

    run_import_data_job(data_job.pk, data_job.model, dry_run=False, run_callbacks=False)


@shared_task()
def sync_external_agreements() -> None:
    data_job = create_auto_import_data_job(
        model=consts.EXTERNAL_AGREEMENTS_RECORD,
        entity_type=EntityTypeEnum.LIVE_AGREEMENTS,
        model_resource=ExternalAgreementResource,
    )

    run_import_data_job(data_job.pk, data_job.model, dry_run=False, run_callbacks=False)


@inject
def create_auto_import_data_job(
    model: str,
    entity_type: EntityTypeEnum,
    model_resource: Type[ModelResource],
    import_data_service_factory: ImportDataServiceFactory = Closing[Provide["import_data_service_factory"]],
) -> ImportDataJob:
    """Creates ImportDataJob instance for auto run."""

    updated_since = ImportDataJob.objects.last_auto_run_sync_date(model=model)

    import_data_service = import_data_service_factory.create(entity_type)

    data = import_data_service.get_data(entity_type=entity_type, updated_since=updated_since)

    file = create_csv_file(
        data=data,
        entity_type=entity_type,
        model_resource=model_resource,
    )

    home_operators = ",".join(HomeOperator.objects.values_list("operator__pmn_code", flat=True))

    data_job = ImportDataJob.objects.create(
        home_operators=home_operators,
        updated_since=updated_since,
        format="text/csv",
        file=file,
        model=model,
        auto_run=True,
        sync_via_api=True,
    )

    return data_job
