from abc import ABC, abstractmethod
from typing import Optional

from nga.apps.agreements.domain.dto import DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.domain.models import Discount, DiscountParameter
from nga.apps.agreements.enums import DiscountDirectionEnum
from nga.core.enums import ServiceTypeEnum
from nga.core.types import DatePeriod


class AbstractDiscountRepository(ABC):
    @abstractmethod
    def get_by_id(self, discount_id: int) -> Discount:
        """Returns discount instance by provided ID."""

    @abstractmethod
    def get_many(
        self,
        agreement_id: int,
        *,
        discount_ids: Optional[list[int]] = None,
        home_operators: Optional[list[int]] = None,
        partner_operators: Optional[list[int]] = None,
        directions: Optional[list[DiscountDirectionEnum]] = None,
        service_types: Optional[list[ServiceTypeEnum]] = None,
        period: Optional[DatePeriod] = None,
    ) -> tuple[Discount, ...]:
        """Returns discounts collection of provided agreement."""

    @abstractmethod
    def create(self, agreement_id: int, discount_dto: DiscountDTO) -> Discount:
        """Creates and persists Discount instance."""

    @abstractmethod
    def create_sub_discount(self, discount_id: int, sub_discount_dto: DiscountDTO) -> Discount:
        """Creates and persists sub-discount."""

    @abstractmethod
    def save(self, discount: Discount) -> Discount:
        """Persists Discount instance to storage."""

    @abstractmethod
    def set_parameters(
        self,
        discount_id: int,
        parameters_dtos: tuple[DiscountParameterDTO, ...],
    ) -> tuple[DiscountParameter, ...]:
        """Replaces discount parameters with new created from DTOs."""

        # TODO: Remove this method by moving its logic to save() method as Discount plays an Aggregate role

    @abstractmethod
    def delete_by_id(self, discount_id: int) -> None:
        """Performs delete action of provided Discount along with its Parameters."""

    @abstractmethod
    def delete_many(self, agreement_id: int) -> None:
        """Removes all discounts associated with provided agreement."""

    @abstractmethod
    def has_intersection_within_agreement(self, discount: Discount) -> bool:
        """Returns boolean that marks whether discount is intersected with agreement discounts."""

    @abstractmethod
    def has_intersection_between_sub_discounts(self, discount: Discount) -> bool:
        """Returns boolean that marks whether discount has intersection between child discounts."""
