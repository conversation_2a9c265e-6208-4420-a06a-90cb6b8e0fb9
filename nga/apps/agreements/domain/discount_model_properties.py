from nga.apps.agreements.domain.models import Discount
from nga.apps.agreements.enums import DiscountCalculationTypeEnum, DiscountModelTypeEnum, DiscountBoundTypeEnum


class DiscountModelProperties:
    def __init__(self, discount: Discount) -> None:
        self._discount = discount

    @property
    def is_bidirectional(self) -> bool:
        return self._discount.is_bidirectional

    @property
    def is_balancing(self) -> bool:
        balancing_model_types = (
            DiscountModelTypeEnum.BALANCED_UNBALANCED_STEPPED_TIERED,
            DiscountModelTypeEnum.BALANCED_UNBALANCED_SINGLE_RATE_EFFECTIVE,
        )

        return self._discount.model_type in balancing_model_types

    @property
    def is_sop_financial(self) -> bool:
        return self._discount.model_type == DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL

    @property
    def supports_sub_discounts(self) -> bool:
        if self._discount.model_type:
            model_types_with_sub_discounts = (DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,)

            return self._discount.model_type in model_types_with_sub_discounts

        discount_parameter_calculation_types_with_sub_discounts = (DiscountCalculationTypeEnum.SEND_OR_PAY_FINANCIAL,)

        supports_sub_discounts = any(
            param.calculation_type in discount_parameter_calculation_types_with_sub_discounts
            for param in self._discount.parameters
        )

        return supports_sub_discounts

    @property
    def has_commitment(self) -> bool:
        send_or_pay_model_types = (
            DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL,
            DiscountModelTypeEnum.SEND_OR_PAY_TRAFFIC_STEPPED_TIERED,
            DiscountModelTypeEnum.SEND_OR_PAY_TRAFFIC_SINGLE_RATE_EFFECTIVE,
        )

        return self._discount.model_type in send_or_pay_model_types

    @property
    def is_financial_threshold(self) -> bool:
        return any(
            param.calculation_type == DiscountCalculationTypeEnum.FINANCIAL_THRESHOLD
            and param.bound_type == DiscountBoundTypeEnum.FINANCIAL_THRESHOLD
            for param in self._discount.parameters
        )
