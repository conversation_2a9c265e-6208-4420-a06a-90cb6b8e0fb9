from dataclasses import dataclass
from datetime import date, datetime
from typing import Optional

from dateutil.relativedelta import relativedelta

from nga.apps.agreements.domain.exceptions import InvalidAgreementStatusTransition
from nga.apps.agreements.enums import AgreementCalculationStatusEnum, AgreementStatusEnum
from nga.core.types import DatePeriod

from .agreement import Agreement


@dataclass(kw_only=True)
class BudgetAgreement(Agreement):
    budget_id: int

    agreement_id: int

    is_active: bool

    applied_at: Optional[datetime]

    calculation_status: AgreementCalculationStatusEnum

    _next_statuses_map = {
        AgreementStatusEnum.DRAFT: (
            AgreementStatusEnum.IN_REVIEW,
            AgreementStatusEnum.REJECTED,
            AgreementStatusEnum.BUDGETING,
        ),
        AgreementStatusEnum.IN_REVIEW: (
            AgreementStatusEnum.DRAFT,
            AgreementStatusEnum.APPROVED,
            AgreementStatusEnum.REJECTED,
        ),
        AgreementStatusEnum.APPROVED: (AgreementStatusEnum.LIVE, AgreementStatusEnum.CLOSED),
        AgreementStatusEnum.SUBMITTED: (AgreementStatusEnum.LIVE,),
        AgreementStatusEnum.SUBMIT_FAILED: (AgreementStatusEnum.DRAFT, AgreementStatusEnum.APPROVED),
        AgreementStatusEnum.REJECTED: (AgreementStatusEnum.DRAFT,),
        AgreementStatusEnum.BUDGETING: (AgreementStatusEnum.REJECTED,),
        AgreementStatusEnum.LIVE: (AgreementStatusEnum.CLOSED,),
        AgreementStatusEnum.AUTO_RENEWED: (AgreementStatusEnum.APPROVED, AgreementStatusEnum.REJECTED),
    }

    def __hash__(self) -> int:
        return self.id

    @property
    def is_confirmed(self) -> bool:
        return self.status in AgreementStatusEnum.get_confirmed_statuses()

    @property
    def is_non_confirmed(self) -> bool:
        return self.status in AgreementStatusEnum.get_non_confirmed_statuses()

    def deactivate(self) -> None:
        self.is_active = False

        self.applied_at = None

        self.calculation_status = AgreementCalculationStatusEnum.NOT_APPLIED

    def activate(self) -> None:
        self.is_active = True

    def set_status(self, new_status: AgreementStatusEnum) -> None:
        if self.status == new_status:
            return

        next_statuses = self.get_next_statuses()

        check_invalid_status = not len(next_statuses) or new_status not in next_statuses
        if check_invalid_status and new_status != AgreementStatusEnum.AUTO_RENEWED:
            raise InvalidAgreementStatusTransition(next_statuses)

        self.status = new_status

        if self.status in [
            AgreementStatusEnum.APPROVED,
            AgreementStatusEnum.BUDGETING,
            AgreementStatusEnum.AUTO_RENEWED,
        ]:
            from nga.apps.agreements.domain.events import BudgetAgreementConfirmedEvent

            self._events.append(BudgetAgreementConfirmedEvent(budget_agreement=self))

    def get_next_statuses(self) -> tuple[AgreementStatusEnum, ...]:
        return self._next_statuses_map.get(self.status, tuple())

    def reset_calculation_status_after_modification(self) -> None:
        """
        After budget agreement updated (itself or any of discounts) it must reset calculation status to reflect current
        budget state. If budget agreement is changed, then budget has inconsistent state and should be recalculated.
        """

        match self.calculation_status:
            case AgreementCalculationStatusEnum.APPLIED:
                self.calculation_status = AgreementCalculationStatusEnum.OUTDATED

            case AgreementCalculationStatusEnum.FAILED:
                self.calculation_status = AgreementCalculationStatusEnum.NOT_APPLIED

    def renew_period(self) -> DatePeriod:
        renewed_start_date = self.period.end_date + relativedelta(months=1)
        renewed_end_date = renewed_start_date + relativedelta(months=self.period.total_months - 1)

        renewed_period = DatePeriod(renewed_start_date, renewed_end_date)

        return renewed_period

    def renew_name(self) -> str:
        return f"{self.name}_RENEWED"


@dataclass
class BudgetAgreementQueryOptions:
    sort_field: Optional[str]

    budget_name: str

    search: Optional[str]

    home_operators: Optional[list[int]]
    partner_operators: Optional[list[int]]
    partner_countries: Optional[list[int]]

    start_date_min: Optional[date]
    start_date_max: Optional[date]

    end_date_min: Optional[date]
    end_date_max: Optional[date]

    is_active: Optional[bool]

    include_satellite: Optional[bool]
    include_premium: Optional[bool]
    include_premium_in_commitment: Optional[bool]

    is_rolling: Optional[bool]

    calculation_statuses: Optional[list[AgreementCalculationStatusEnum]]

    statuses: Optional[list[AgreementStatusEnum]]

    negotiators: Optional[list[int]]

    updated_at_min: Optional[datetime]
    updated_at_max: Optional[datetime]

    applied_at_min: Optional[datetime]
    applied_at_max: Optional[datetime]
