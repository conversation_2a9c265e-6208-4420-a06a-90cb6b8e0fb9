from copy import copy
from dataclasses import asdict, dataclass
from decimal import Decimal
from typing import Any, Optional

from nga.apps.agreements.domain.value_objects import DiscountQualifyingRule
from nga.apps.agreements.enums import (
    DiscountDirectionEnum,
    DiscountModelTypeEnum,
    DiscountSettlementMethodEnum,
    TaxTypeEnum,
)
from nga.apps.budgets.domain import Budget
from nga.core.enums import CallDestinationEnum, IMSICountTypeEnum, ServiceTypeEnum, TrafficDirectionEnum, VolumeTypeEnum
from nga.core.types import DatePeriod

from .discount_parameter import DiscountParameter


@dataclass
class DiscountTrafficParameters:
    home_operators: tuple[int, ...]
    partner_operators: tuple[int, ...]

    direction: DiscountDirectionEnum
    service_types: tuple[ServiceTypeEnum, ...]
    period: DatePeriod

    call_destinations: Optional[tuple[CallDestinationEnum, ...]]
    called_countries: Optional[tuple[int, ...]]
    traffic_segments: Optional[tuple[int, ...]]

    imsi_count_type: Optional[IMSICountTypeEnum]


@dataclass(kw_only=True)
class Discount(DiscountTrafficParameters):
    id: int

    agreement_id: int

    model_type: Optional[DiscountModelTypeEnum]

    currency_code: str

    tax_type: TaxTypeEnum

    volume_type: VolumeTypeEnum

    settlement_method: DiscountSettlementMethodEnum

    qualifying_rule: Optional[DiscountQualifyingRule]

    parent_id: Optional[int]

    parameters: tuple["DiscountParameter", ...]

    sub_discounts: tuple["Discount", ...]

    include_premium: bool

    include_premium_in_commitment: bool

    financial_threshold: Optional[Decimal]

    above_financial_threshold_rate: Optional[Decimal]

    above_commitment_rate: Optional[Decimal]

    inbound_market_share: Optional[Decimal]

    commitment_distribution_parameters: Optional[tuple["CommitmentDistributionParameter", ...]]

    budget: Optional[Budget] = None

    def __hash__(self) -> int:
        return self.id

    def __copy__(self) -> "Discount":
        return Discount(
            id=self.id,
            agreement_id=self.agreement_id,
            model_type=self.model_type,
            currency_code=self.currency_code,
            tax_type=self.tax_type,
            volume_type=self.volume_type,
            settlement_method=self.settlement_method,
            qualifying_rule=self.qualifying_rule,
            parent_id=self.parent_id,
            parameters=tuple(copy(p) for p in self.parameters),
            sub_discounts=tuple(copy(sb) for sb in self.sub_discounts),
            home_operators=self.home_operators,
            partner_operators=self.partner_operators,
            direction=self.direction,
            service_types=self.service_types,
            period=copy(self.period),
            call_destinations=self.call_destinations,
            called_countries=self.called_countries,
            traffic_segments=self.traffic_segments,
            imsi_count_type=self.imsi_count_type,
            include_premium=self.include_premium,
            include_premium_in_commitment=self.include_premium_in_commitment,
            financial_threshold=self.financial_threshold,
            above_financial_threshold_rate=self.above_financial_threshold_rate,
            above_commitment_rate=self.above_commitment_rate,
            inbound_market_share=self.inbound_market_share,
            commitment_distribution_parameters=self.commitment_distribution_parameters,
        )

    @property
    def is_parent(self) -> bool:
        return self.parent_id is None

    @property
    def total_parameters(self) -> int:
        return len(self.parameters)

    @property
    def has_sub_discounts(self) -> bool:
        return len(self.sub_discounts) > 0

    @property
    def traffic_direction(self) -> TrafficDirectionEnum:
        match self.direction:
            case DiscountDirectionEnum.INBOUND:
                return TrafficDirectionEnum.INBOUND

            case DiscountDirectionEnum.OUTBOUND:
                return TrafficDirectionEnum.OUTBOUND

            case _:
                raise ValueError("Discount Direction cannot be mapped 1 to 1 to Traffic Direction")

    @property
    def needs_to_be_qualified(self) -> bool:
        return self.qualifying_rule is not None or self.model_type == DiscountModelTypeEnum.SEND_OR_PAY_FINANCIAL

    def add_sub_discount(self, sub_discount: "Discount") -> None:
        self.sub_discounts = tuple(
            [
                *self.sub_discounts,
                sub_discount,
            ]
        )

    def equal_by_traffic(self, other: "Discount") -> bool:
        """
        Compare two discounts by their traffic fields.

        Returns True if both discounts have the same:
        - agreement_id
        - home_operators
        - partner_operators
        - direction
        - service_types (as sets, order doesn't matter)
        - period
        - traffic_segments
        """

        return (
            self.home_operators == other.home_operators
            and self.partner_operators == other.partner_operators
            and self.direction == other.direction
            and set(self.service_types) == set(other.service_types)
            and self.period == other.period
            and self.traffic_segments == other.traffic_segments
        )

    def split_by_directions(self) -> tuple["Discount", "Discount"]:
        """Returns two copies (inbound, outbound) of bidirectional discount, each with parameters per direction."""

        inbound_discount = copy(self)
        inbound_discount.direction = DiscountDirectionEnum.INBOUND
        inbound_discount.sub_discounts = tuple()

        outbound_discount = copy(self)
        outbound_discount.direction = DiscountDirectionEnum.OUTBOUND
        outbound_discount.sub_discounts = tuple()

        strict_directed_sub_discounts: list["Discount"] = []

        for sb in self.sub_discounts:
            if sb.direction == DiscountDirectionEnum.BIDIRECTIONAL:
                strict_directed_sub_discounts.extend(sb.split_by_directions())
            else:
                strict_directed_sub_discounts.append(sb)

        for sub_discount in strict_directed_sub_discounts:
            match sub_discount.direction:
                case DiscountDirectionEnum.INBOUND:
                    inbound_discount.add_sub_discount(sub_discount)

                case DiscountDirectionEnum.OUTBOUND:
                    outbound_discount.add_sub_discount(sub_discount)

        return inbound_discount, outbound_discount

    @property
    def is_bidirectional(self) -> bool:
        return self.direction == DiscountDirectionEnum.BIDIRECTIONAL

    @property
    def exclude_premium(self) -> bool:
        return not self.include_premium

    def set_budget(self, budget: Budget) -> None:
        self.budget = budget

        if self.has_sub_discounts:
            for sub_discount in self.sub_discounts:
                sub_discount.set_budget(budget)

    def get_budget(self) -> Budget:
        if self.budget is not None:
            return self.budget

        raise ValueError()


@dataclass
class CommitmentDistributionParameter:
    """
    Class describes configuration (amount charge for pmns) for manual commitment distribution for
    SoP Financial discount model.
    """

    home_operators: tuple[int, ...]

    partner_operators: tuple[int, ...]

    charge: Decimal

    def to_dict(self) -> dict[str, Any]:
        return asdict(self)
