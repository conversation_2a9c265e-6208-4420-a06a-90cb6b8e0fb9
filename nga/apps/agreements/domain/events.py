from dataclasses import dataclass

from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.enums import AgreementStatusEnum
from nga.internal.domain import Event, EventName


@dataclass(kw_only=True)
class BudgetAgreementConfirmedEvent(Event):
    budget_agreement: BudgetAgreement

    @classmethod
    def get_event_name(cls) -> EventName:
        return "agreements.BudgetAgreementConfirmed"


@dataclass(kw_only=True)
class BudgetAgreementStatusChangedEvent(Event):
    budget_agreement: BudgetAgreement

    old_status: AgreementStatusEnum

    new_status: AgreementStatusEnum

    changed_by_user_id: int

    @classmethod
    def get_event_name(cls) -> EventName:
        return "agreements.BudgetAgreementStatusChanged"
