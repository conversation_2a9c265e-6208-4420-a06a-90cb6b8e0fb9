from datetime import date
from typing import Optional, Sequence

from dateutil.relativedelta import relativedelta
from django.conf import settings
from django.contrib.postgres.aggregates import ArrayAgg
from django.db.models import Case, F, Q, QuerySet, Value, When
from django.db.transaction import atomic
from django.utils import timezone

from nga.apps.agreements.domain.dto import BudgetAgreementCreateDTO
from nga.apps.agreements.domain.exceptions import BudgetAgreementDoesNotExist
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.agreements.enums import AgreementCalculationStatusEnum, AgreementStatusEnum
from nga.apps.agreements.infra.orm import models
from nga.apps.budgets.domain.dto import BudgetParametersFilters
from nga.apps.common.queryset_utils import period_intersection_query, to_pk_list
from nga.core.types import DatePeriod


class BudgetAgreementDjangoORMRepository(AbstractBudgetAgreementRepository):
    @classmethod
    def get_many_query(
        cls,
        budget_id: Optional[int] = None,
        budget_agreement_ids: Optional[list[int]] = None,
        budget_parameters: Optional[BudgetParametersFilters] = None,
        agreement_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        statuses: Optional[list[AgreementStatusEnum]] = None,
    ) -> QuerySet[models.Agreement]:
        """Returns get_many Agreements query."""

        q = cls._get_operators_query(budget_parameters=budget_parameters)

        qs = models.BudgetAgreement.objects.annotate(
            budget_agreement_id=F("id"),
            home_operators_ids=ArrayAgg("agreement__home_operators", distinct=True, default=[]),
            partner_operators_ids=ArrayAgg("agreement__partner_operators", distinct=True, default=[]),
            external_id=F("agreement__external_id"),
            parent_id=F("agreement__parent_id"),
            name=F("agreement__name"),
            start_date=F("agreement__start_date"),
            end_date=F("agreement__end_date"),
            status=F("agreement__status"),
            created_at=F("agreement__created_at"),
            updated_at=F("agreement__updated_at"),
            negotiator_id=F("agreement__negotiator_id"),
            include_satellite=F("agreement__include_satellite"),
            include_premium=F("agreement__include_premium"),
            include_premium_in_commitment=F("agreement__include_premium_in_commitment"),
            is_rolling=F("agreement__is_rolling"),
        ).filter(q)

        if budget_id is not None:
            qs = qs.filter(budget_id=budget_id)

        if budget_agreement_ids is not None:
            qs = qs.filter(budget_agreement_id__in=budget_agreement_ids)

        if agreement_id is not None:
            qs = qs.filter(agreement_id=agreement_id)

        if start_date is not None:
            qs = qs.filter(start_date__gte=start_date.replace(day=1))

        if end_date is not None:
            qs = qs.filter(end_date__lte=end_date + relativedelta(day=31))

        if statuses is not None:
            qs = qs.filter(status__in=statuses)

        return qs

    @classmethod
    def from_queryset_to_domain(cls, qs: QuerySet) -> tuple[BudgetAgreement, ...]:  # noqa
        records = (
            BudgetAgreement(
                id=values["budget_agreement_id"],
                budget_id=values["budget_id"],
                agreement_id=values["agreement_id"],
                external_id=values["external_id"],
                parent_id=values["parent_id"],
                name=values["name"],
                status=AgreementStatusEnum(values["status"]),
                calculation_status=AgreementCalculationStatusEnum(values["calculation_status"]),
                home_operators=values["home_operators_ids"],
                partner_operators=values["partner_operators_ids"],
                period=DatePeriod(values["start_date"], values["end_date"]),
                is_active=values["is_active"],
                updated_at=values["updated_at"],
                applied_at=values["applied_at"],
                negotiator_id=values["negotiator_id"],
                include_satellite=values["include_satellite"],
                include_premium=values["include_premium"],
                include_premium_in_commitment=values["include_premium_in_commitment"],
                is_rolling=values["is_rolling"],
            )
            for values in qs.values()
        )
        return tuple(records)

    def get_many(
        self,
        *,
        budget_id: Optional[int] = None,
        budget_agreement_ids: Optional[list[int]] = None,
        budget_parameters: Optional[BudgetParametersFilters] = None,
        agreement_id: Optional[int] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        statuses: Optional[list[AgreementStatusEnum]] = None,
        only_active: Optional[bool] = None,
        only_modified: Optional[bool] = None,
    ) -> tuple[BudgetAgreement, ...]:
        """Returns list of agreements from budget by specified parameters."""

        qs = self.get_many_query(
            budget_id=budget_id,
            budget_agreement_ids=budget_agreement_ids,
            budget_parameters=budget_parameters,
            agreement_id=agreement_id,
            start_date=start_date,
            end_date=end_date,
            statuses=statuses,
        )

        if only_active is not None:
            qs = qs.filter(is_active=only_active)

        if only_modified is not None:
            qs = qs.annotate(
                is_modified=Case(
                    When(applied_at__isnull=True, then=Value(True)),
                    When(agreement__updated_at__gt=F("applied_at"), then=Value(True)),
                    default=False,
                )
            )

            qs = qs.filter(is_modified=only_modified)

        records = self.from_queryset_to_domain(qs)

        return records

    @classmethod
    def _get_operators_query(
        cls,
        budget_parameters: Optional[BudgetParametersFilters] = None,
    ) -> Q:
        """Returns filter qs Home Operators ∩ (Partner Operators ∪ Partner Countries)."""

        if budget_parameters is None:
            budget_parameters = BudgetParametersFilters()

        q_home = Q()
        q_partner = Q()

        if budget_parameters.home_operators:
            q_home |= Q(agreement__home_operators__pk__in=budget_parameters.home_operators)

        if budget_parameters.partner_operators:
            q_partner = Q(agreement__partner_operators__pk__in=budget_parameters.partner_operators)

        if budget_parameters.partner_countries:
            q_partner = Q(agreement__partner_operators__country_id__in=budget_parameters.partner_countries)

        return q_home & q_partner

    def get_by_id(self, budget_agreement_id: int) -> BudgetAgreement:
        """Returns agreement by provided id."""

        orm_budget_agreement = self.get_orm_budget_agreement(budget_agreement_id)

        return from_orm_to_domain(orm_budget_agreement)

    def get_by_external_id(self, external_id: int, budget_id: int) -> Optional[BudgetAgreement]:
        """Returns agreement by provided external id and budget id."""
        try:
            orm_budget_agreement = models.BudgetAgreement.objects.get(
                agreement__external_id=external_id,
                budget__id=budget_id,
            )

            return from_orm_to_domain(orm_budget_agreement)

        except models.BudgetAgreement.DoesNotExist:

            return None

    def get_by_parent_id(self, parent_id: int, budget_id: int) -> Optional[BudgetAgreement]:
        """Returns agreement by provided parent id and budget id."""
        try:
            orm_budget_agreement = models.BudgetAgreement.objects.get(
                agreement__parent_id=parent_id,
                budget_id=budget_id,
            )

            return from_orm_to_domain(orm_budget_agreement)

        except models.BudgetAgreement.DoesNotExist:

            return None

    def get_linked_parents(self, budget_agreement_id: int, budget_id: int) -> list[BudgetAgreement]:
        qs = models.BudgetAgreement.objects.filter(budget_id=budget_id).filter_linked_agreements(
            budget_agreement_id,
            budget_id=budget_id,
            chronology="parents",
        )

        return [from_orm_to_domain(orm_ba) for orm_ba in qs]

    def get_linked_subsidiaries(self, budget_agreement_id: int, budget_id: int) -> list[BudgetAgreement]:
        qs = models.BudgetAgreement.objects.filter(budget_id=budget_id).filter_linked_agreements(
            budget_agreement_id,
            budget_id=budget_id,
            chronology="subsidiaries",
        )

        return [from_orm_to_domain(orm_ba) for orm_ba in qs]

    @classmethod
    def get_orm_budget_agreement(cls, budget_agreement_id: int) -> models.BudgetAgreement:
        try:
            orm_budget_agreement = models.BudgetAgreement.objects.get(pk=budget_agreement_id)
        except models.BudgetAgreement.DoesNotExist:
            raise BudgetAgreementDoesNotExist(budget_agreement_id)

        return orm_budget_agreement

    @atomic
    def create(self, agreement_dto: BudgetAgreementCreateDTO, budget_id: int) -> BudgetAgreement:
        """Creates agreement in a budget."""

        orm_agreement = models.Agreement.objects.create(
            name=agreement_dto.name,
            external_id=agreement_dto.external_id,
            start_date=agreement_dto.period.start_date,
            end_date=agreement_dto.period.end_date,
            status=AgreementStatusEnum.DRAFT,
            created_at=timezone.now(),
            updated_at=timezone.now(),
            negotiator_id=agreement_dto.negotiator_id,
            include_satellite=agreement_dto.include_satellite,
            include_premium=agreement_dto.include_premium,
            include_premium_in_commitment=agreement_dto.include_premium_in_commitment,
            is_rolling=agreement_dto.is_rolling,
        )
        orm_agreement.home_operators.set(agreement_dto.home_operators)
        orm_agreement.partner_operators.set(agreement_dto.partner_operators)

        orm_budget_agreement = models.BudgetAgreement.objects.create(
            budget_id=budget_id,
            agreement_id=orm_agreement.id,
            is_active=False,
        )

        return from_orm_to_domain(orm_budget_agreement)

    def delete_by_id(self, budget_agreement_id: int, *, for_all_budgets: bool = False) -> None:
        """Deletes agreement and its relations with budgets."""

        orm_budget_agreement = self.get_orm_budget_agreement(budget_agreement_id)

        orm_agreement = orm_budget_agreement.agreement

        total_budgets = orm_agreement.budgets.count()

        if total_budgets == 1 or for_all_budgets:
            orm_agreement.discounts.all().delete()
            orm_agreement.delete()

        orm_budget_agreement.delete()

    def has_intersection(
        self,
        budget_agreement: BudgetAgreement,
        with_statuses: Optional[Sequence[AgreementStatusEnum]] = None,
        with_active: Optional[bool] = None,
    ) -> bool:
        """Returns boolean that marks whether agreement is intersected with other agreements."""

        qs = models.BudgetAgreement.objects.filter(
            period_intersection_query(budget_agreement.period, prefix="agreement__"),
            budget_id=budget_agreement.budget_id,
            agreement__home_operators__pk__in=budget_agreement.home_operators,
            agreement__partner_operators__pk__in=budget_agreement.partner_operators,
        ).exclude(pk=budget_agreement.id)

        if with_statuses:
            qs = qs.filter(agreement__status__in=with_statuses)

        if with_active:
            qs = qs.filter(is_active=with_active)

        intersection_exists = qs.exists()

        return intersection_exists

    def get_intersected_many(
        self,
        budget_id: int,
        home_operators: list[int],
        partner_operators: list[int],
        period: DatePeriod,
        excluded_id: Optional[int] = None,
        with_statuses: Optional[Sequence[AgreementStatusEnum]] = None,
        with_active: Optional[bool] = None,
    ) -> tuple[BudgetAgreement, ...]:
        qs = models.BudgetAgreement.objects.filter(
            period_intersection_query(period, prefix="agreement__"),
            budget_id=budget_id,
            agreement__home_operators__pk__in=home_operators,
            agreement__partner_operators__pk__in=partner_operators,
        )

        if excluded_id is not None:
            qs = qs.exclude(pk=excluded_id)

        if with_statuses:
            qs = qs.filter(agreement__status__in=with_statuses)

        if with_active:
            qs = qs.filter(is_active=with_active)

        qs = qs.distinct()

        return tuple(map(from_orm_to_domain, qs))

    def copy(self, budget_agreement: BudgetAgreement, target_budget_id: int) -> BudgetAgreement:
        """Copies Agreement to target Budget."""

        orm_budget_agreement = self.get_orm_budget_agreement(budget_agreement.id)

        if budget_agreement.is_confirmed is True:
            copied_orm_budget_agreement = models.BudgetAgreement.objects.create(
                budget_id=target_budget_id,
                agreement_id=orm_budget_agreement.agreement_id,
            )

            return from_orm_to_domain(copied_orm_budget_agreement)

        else:
            dto = BudgetAgreementCreateDTO(
                name=budget_agreement.name,
                home_operators=budget_agreement.home_operators,
                partner_operators=budget_agreement.partner_operators,
                period=budget_agreement.period,
                negotiator_id=None,
                include_satellite=budget_agreement.include_satellite,
                include_premium=budget_agreement.include_premium,
                include_premium_in_commitment=budget_agreement.include_premium_in_commitment,
                is_rolling=budget_agreement.is_rolling,
            )
            copied_agreement = self.create(dto, target_budget_id)

            return copied_agreement

    def save(self, budget_agreement: BudgetAgreement) -> BudgetAgreement:
        orm_budget_agreement = self.get_orm_budget_agreement(budget_agreement.id)

        orm_budget_agreement.is_active = budget_agreement.is_active
        orm_budget_agreement.applied_at = budget_agreement.applied_at
        orm_budget_agreement.calculation_status = budget_agreement.calculation_status

        orm_budget_agreement.agreement.updated_at = budget_agreement.updated_at
        orm_budget_agreement.agreement.name = budget_agreement.name
        orm_budget_agreement.agreement.status = budget_agreement.status
        orm_budget_agreement.agreement.negotiator_id = budget_agreement.negotiator_id
        orm_budget_agreement.agreement.include_satellite = budget_agreement.include_satellite
        orm_budget_agreement.agreement.include_premium = budget_agreement.include_premium
        orm_budget_agreement.agreement.include_premium_in_commitment = budget_agreement.include_premium_in_commitment
        orm_budget_agreement.agreement.is_rolling = budget_agreement.is_rolling
        orm_budget_agreement.agreement.start_date = budget_agreement.period.start_date
        orm_budget_agreement.agreement.end_date = budget_agreement.period.end_date
        orm_budget_agreement.agreement.external_id = budget_agreement.external_id
        orm_budget_agreement.agreement.parent_id = budget_agreement.parent_id
        orm_budget_agreement.agreement.save(
            update_fields=[
                "updated_at",
                "name",
                "status",
                "negotiator_id",
                "include_satellite",
                "include_premium",
                "include_premium_in_commitment",
                "is_rolling",
                "start_date",
                "end_date",
                "external_id",
                "parent_id",
            ]
        )

        orm_budget_agreement.agreement.home_operators.set(budget_agreement.home_operators)
        orm_budget_agreement.agreement.partner_operators.set(budget_agreement.partner_operators)

        orm_budget_agreement.save(update_fields=["is_active", "applied_at", "calculation_status"])
        orm_budget_agreement.refresh_from_db()

        return from_orm_to_domain(orm_budget_agreement)

    def update_many(self, budget_agreements: Sequence[BudgetAgreement]) -> None:
        """Persists collection of agreements to the storage."""

        orm_budget_agreements = [
            models.BudgetAgreement(
                pk=ba.id,
                agreement_id=ba.agreement_id,
                is_active=ba.is_active,
                applied_at=ba.applied_at,
                calculation_status=ba.calculation_status,
            )
            for ba in budget_agreements
        ]

        models.BudgetAgreement.objects.bulk_update(
            orm_budget_agreements,
            fields=[
                "applied_at",
                "is_active",
                "calculation_status",
            ],
            batch_size=settings.DEFAULT_BATCH_SIZE,
        )

    def count(
        self,
        budget_id: int,
        *,
        budget_parameters: Optional[BudgetParametersFilters] = None,
        is_active: Optional[bool] = None,
    ) -> int:
        """Returns number of agreements by provided options."""

        q = self._get_operators_query(budget_parameters=budget_parameters)

        qs = models.BudgetAgreement.objects.filter(q, budget_id=budget_id)

        if budget_parameters and budget_parameters.period is not None:
            qs = qs.filter(period_intersection_query(budget_parameters.period, prefix="agreement__"))

        if is_active is not None:
            qs = qs.filter(is_active=is_active)

        total_agreements = qs.values_list("pk", flat=True).distinct().count()

        return total_agreements


def from_orm_to_domain(orm_budget_agreement: models.BudgetAgreement) -> BudgetAgreement:
    orm_agreement = orm_budget_agreement.agreement

    return BudgetAgreement(
        id=orm_budget_agreement.id,
        budget_id=orm_budget_agreement.budget_id,
        agreement_id=orm_agreement.id,
        external_id=orm_agreement.external_id,
        parent_id=orm_agreement.parent_id,
        name=orm_agreement.name,
        status=AgreementStatusEnum(orm_agreement.status),
        calculation_status=AgreementCalculationStatusEnum(orm_budget_agreement.calculation_status),
        period=DatePeriod(orm_agreement.start_date, orm_agreement.end_date),
        home_operators=to_pk_list(orm_agreement.home_operators.all()),
        partner_operators=to_pk_list(orm_agreement.partner_operators.all()),
        updated_at=orm_agreement.updated_at,
        applied_at=orm_budget_agreement.applied_at,
        is_active=orm_budget_agreement.is_active,
        negotiator_id=orm_agreement.negotiator_id,
        include_satellite=orm_agreement.include_satellite,
        include_premium=orm_agreement.include_premium,
        include_premium_in_commitment=orm_agreement.include_premium_in_commitment,
        is_rolling=orm_agreement.is_rolling,
    )
