from django.db import models

from nga.apps.agreements.enums import AgreementStatusEnum
from nga.apps.agreements.infra.orm.models import Agreement
from nga.apps.users.models import User
from nga.core.enums import get_choices


class AgreementStatusChangeLog(models.Model):
    agreement = models.ForeignKey(
        Agreement,
        on_delete=models.CASCADE,
        related_name="status_change_logs",
        related_query_name="status_change_log",
    )

    old_status = models.PositiveSmallIntegerField(choices=get_choices(AgreementStatusEnum))

    new_status = models.PositiveSmallIntegerField(choices=get_choices(AgreementStatusEnum))

    changed_by = models.ForeignKey(User, on_delete=models.CASCADE)

    changed_at = models.DateTimeField(auto_now_add=True, editable=False)

    class Meta:
        db_table = "agreement_status_change_logs"

    def __str__(self) -> str:
        return (
            f"Agreement #{self.agreement.id} | "
            f"Status: {AgreementStatusEnum(self.old_status).name} -> {AgreementStatusEnum(self.new_status).name}"
        )
