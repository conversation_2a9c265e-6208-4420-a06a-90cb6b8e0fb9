# Generated by Django 5.1.7 on 2025-07-29 10:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('agreements', '0055_alter_discount_financial_threshold'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AgreementStatusChangeLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_status', models.PositiveSmallIntegerField(choices=[(1, 'DRAFT'), (2, 'LIVE'), (3, 'IN_REVIEW'), (4, 'APPROVED'), (5, 'CLOSED'), (6, 'REJECTED'), (7, 'SUBMITTED'), (8, 'SUBMIT_FAILED'), (9, 'BUDGETING'), (10, 'AUTO_RENEWED')])),
                ('new_status', models.PositiveSmallIntegerField(choices=[(1, 'DRAFT'), (2, 'LIVE'), (3, 'IN_REVIEW'), (4, 'APPROVED'), (5, 'CLOSED'), (6, 'REJECTED'), (7, 'SUBMITTED'), (8, 'SUBMIT_FAILED'), (9, 'BUDGETING'), (10, 'AUTO_RENEWED')])),
                ('changed_at', models.DateTimeField(auto_now_add=True)),
                ('agreement', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_change_logs', related_query_name='status_change_log', to='agreements.agreement')),
                ('changed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'agreement_status_change_logs',
            },
        ),
    ]
