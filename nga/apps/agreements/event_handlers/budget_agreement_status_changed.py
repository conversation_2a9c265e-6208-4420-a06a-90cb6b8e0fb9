from typing import cast

from nga.apps.agreements.domain.events import BudgetAgreementStatusChangedEvent
from nga.apps.agreements.infra.orm import models
from nga.internal.domain import Event


class LogBudgetAgreementStatusChange:
    def handle(self, event: Event) -> None:
        status_changed = cast(BudgetAgreementStatusChangedEvent, event)

        models.AgreementStatusChangeLog.objects.create(
            agreement_id=status_changed.budget_agreement.agreement_id,
            old_status=status_changed.old_status,
            new_status=status_changed.new_status,
            changed_by_id=status_changed.changed_by_user_id,
        )
