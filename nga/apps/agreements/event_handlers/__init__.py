import logging

from nga.apps.agreements.domain.events import BudgetAgreementConfirmedEvent, BudgetAgreementStatusChangedEvent
from nga.apps.agreements.event_handlers.budget_agreement_confirmed import (
    ConnectWithBudgetsOnBudgetAgreementConfirmedEvent,
)
from nga.apps.agreements.event_handlers.budget_agreement_status_changed import LogBudgetAgreementStatusChange
from nga.internal.domain import EventSubscriber


def register_agreement_domain_event_handlers(event_dispatcher: EventSubscriber) -> None:
    event_dispatcher.subscribe(BudgetAgreementConfirmedEvent, ConnectWithBudgetsOnBudgetAgreementConfirmedEvent)

    event_dispatcher.subscribe(BudgetAgreementStatusChangedEvent, LogBudgetAgreementStatusChange)

    logging.info("agreement domain event handlers are registered")
