from dataclasses import dataclass
from typing import Any, Optional

from mediatr.mediator import GenericQuery

from nga.apps.agreements.domain.dto import BudgetAgreementCreateDTO, DiscountDTO, DiscountParameterDTO
from nga.apps.agreements.domain.models import BudgetAgreement, Discount
from nga.apps.agreements.enums import AgreementNoteTypeEnum, AgreementStatusEnum

__all__ = [
    "AutoRenewBudgetAgreementCommand",
    "BulkActivateBudgetAgreementCommand",
    "BulkCopyBudgetAgreementCommand",
    "CreateAgreementNoteCommand",
    "CreateDiscountCommand",
    "CreateSubDiscountCommand",
    "BulkDeactivateBudgetAgreementCommand",
    "BulkDeleteBudgetAgreementCommand",
    "DeleteDiscountCommand",
    "BulkRenewBudgetAgreementCommand",
    "BulkStatusChangeBudgetAgreementCommand",
    "UpdateBudgetAgreementAfterDiscountModifiedCommand",
    "UpdateDiscountCommand",
    "UpdateSubDiscountCommand",
    "UpdateAgreementCommand",
    "CreateBudgetAgreementCommand",
    "BulkCloneBudgetAgreementCommand",
    "ConnectAgreementWithBudgetsCommand",
]


@dataclass
class BulkRenewBudgetAgreementCommand(GenericQuery):
    budget_id: int

    budget_agreement_ids: list[int]


@dataclass
class AutoRenewBudgetAgreementCommand(GenericQuery):
    budget_agreement_id: int


@dataclass
class CreateDiscountCommand(GenericQuery):
    budget_agreement: BudgetAgreement

    discount_dto: DiscountDTO


@dataclass
class CreateSubDiscountCommand(GenericQuery):
    discount: Discount

    budget_agreement: BudgetAgreement

    sub_discount_dto: DiscountDTO


@dataclass
class DeleteDiscountCommand(GenericQuery):
    discount: Discount

    budget_agreement: BudgetAgreement


@dataclass
class UpdateDiscountCommand(GenericQuery):
    discount: Discount

    budget_agreement: BudgetAgreement

    data: dict[str, Any]

    parameters_dtos: tuple[DiscountParameterDTO, ...]


@dataclass
class UpdateSubDiscountCommand(UpdateDiscountCommand):
    """Command that updates sub-discount"""


@dataclass
class BulkDeleteBudgetAgreementCommand(GenericQuery):
    budget_id: int

    budget_agreement_ids: list[int]


@dataclass
class BulkActivateBudgetAgreementCommand(GenericQuery):
    budget_id: int

    budget_agreement_ids: Optional[list[int]]


@dataclass
class BulkDeactivateBudgetAgreementCommand(GenericQuery):
    budget_id: int

    budget_agreement_ids: Optional[list[int]]


@dataclass
class UpdateBudgetAgreementAfterDiscountModifiedCommand(GenericQuery):
    budget_agreement_id: int


@dataclass
class BulkCopyBudgetAgreementCommand(GenericQuery):
    target_budget_id: int

    budget_agreement_ids: Optional[list[int]]

    source_budget_id: Optional[int]

    only_active: Optional[bool]


@dataclass
class UpdateAgreementCommand(GenericQuery):
    agreement_id: int

    name: str
    status: AgreementStatusEnum

    include_satellite: bool
    include_premium: bool
    include_premium_in_commitment: bool

    is_rolling: bool

    negotiator_id: Optional[int]

    user_id: int


@dataclass
class CreateBudgetAgreementCommand(GenericQuery):
    budget_id: int
    budget_agreement_dto: BudgetAgreementCreateDTO


@dataclass
class BulkCloneBudgetAgreementCommand(GenericQuery):
    budget_id: int
    budget_agreement_ids: list[int]

    new_name: Optional[str]  # to cover case with single agreement cloning


@dataclass
class ConnectAgreementWithBudgetsCommand(GenericQuery):
    budget_agreement_id: int


@dataclass
class CreateAgreementNoteCommand(GenericQuery):
    agreement_id: int

    type: AgreementNoteTypeEnum

    content: str


@dataclass
class BulkStatusChangeBudgetAgreementCommand(GenericQuery):
    budget_id: int

    initial_status: AgreementStatusEnum
    target_status: AgreementStatusEnum

    budget_agreement_ids: Optional[list[int]]
    only_active: Optional[bool]
