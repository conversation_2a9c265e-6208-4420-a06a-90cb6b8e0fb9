import traceback
from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from django.db.transaction import atomic
from mediatr import Mediator
from rest_framework.exceptions import APIException, ValidationError
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.mappers import AbstractBudgetAgreementListSchemaMapper
from nga.apps.agreements.api.serializers.agreement import AgreementSchemaSerializer, AgreementUpdateSerializer
from nga.apps.agreements.api.views.utils import get_budget_agreement_or_404
from nga.apps.agreements.commands import UpdateAgreementCommand
from nga.apps.agreements.domain.exceptions import AgreementValidationError
from nga.apps.agreements.domain.models import BudgetAgreement
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.view_utils import get_budget_or_404
from nga.core.exceptions import BaseNGAException


class AgreementPatchUpdateAPIView(GenericAPIView):
    serializer_class = AgreementUpdateSerializer
    response_serializer_class = AgreementSchemaSerializer
    permission_classes = [IsAuthenticated]

    update_agreement_command_class = UpdateAgreementCommand

    @inject
    def __init__(
        self,
        mediator: Mediator = Provide["mediator"],
        agreement_mapper: AbstractBudgetAgreementListSchemaMapper = Closing[Provide["agreement_list_schema_mapper"]],
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._mediator = mediator
        self._agreement_mapper = agreement_mapper
        self._budget_provider = budget_provider
        self._budget_agreement_repository = budget_agreement_repository

    def patch(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Agreement Parameters Update API."""

        get_budget_or_404(budget_provider=self._budget_provider, budget_id=self.kwargs["budget_id"])

        budget_agreement = self.update_agreement(request)

        schema = self._agreement_mapper.map([budget_agreement])[0]

        agreement_response = self.response_serializer_class(schema).data

        return Response(agreement_response)

    @atomic
    def update_agreement(self, request: Request) -> BudgetAgreement:
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        budget_agreement = get_budget_agreement_or_404(self._budget_agreement_repository, self.kwargs["agreement_id"])

        data = serializer.validated_data

        update_agreement_cmd = self.update_agreement_command_class(
            agreement_id=budget_agreement.id,
            name=data.name,
            status=data.status,
            negotiator_id=data.negotiator.id if data.negotiator else None,
            include_satellite=data.include_satellite,
            include_premium=data.include_premium,
            include_premium_in_commitment=data.include_premium_in_commitment,
            is_rolling=data.is_rolling,
            user_id=self.request.user.id,
        )

        try:
            budget_agreement = self._mediator.send(update_agreement_cmd)
        except AgreementValidationError as e:
            raise ValidationError(detail=e.message)
        except BaseNGAException as e:
            raise APIException(e.message)
        except Exception as e:
            traceback.print_exc()
            raise APIException(str(e))

        return budget_agreement
