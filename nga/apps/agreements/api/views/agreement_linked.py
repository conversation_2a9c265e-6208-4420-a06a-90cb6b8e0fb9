from typing import Any

from dependency_injector.wiring import Closing, Provide, inject
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.generics import GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.request import Request
from rest_framework.response import Response

from nga.apps.agreements.api.serializers import LinkedAgreementResponseSerializer
from nga.apps.agreements.api.views.utils import get_budget_agreement_or_404
from nga.apps.agreements.domain.repositories import AbstractBudgetAgreementRepository
from nga.apps.budgets.providers.budget import AbstractBudgetProvider
from nga.apps.common.consts import SWAGGER_AGREEMENTS_TAG
from nga.apps.common.view_utils import get_budget_or_404


class AgreementLinkedAPIView(GenericAPIView):
    serializer_class = LinkedAgreementResponseSerializer
    permission_classes = [IsAuthenticated]

    @inject
    def __init__(
        self,
        budget_provider: AbstractBudgetProvider = Closing[Provide["budget_provider"]],
        budget_agreement_repository: AbstractBudgetAgreementRepository = Closing[
            Provide["budget_agreement_repository"]
        ],
        **kwargs: Any
    ) -> None:
        super().__init__(**kwargs)

        self._budget_provider = budget_provider
        self._budget_agreement_repository = budget_agreement_repository

    @swagger_auto_schema(
        tags=[SWAGGER_AGREEMENTS_TAG],
        responses={status.HTTP_200_OK: serializer_class(many=True)},
    )
    def get(self, request: Request, *args: Any, **kwargs: Any) -> Response:
        """Linked Agreements GET API."""

        get_budget_or_404(budget_provider=self._budget_provider, budget_id=self.kwargs["budget_id"])

        budget_agreement = get_budget_agreement_or_404(self._budget_agreement_repository, self.kwargs["agreement_id"])

        linked_agreements = {budget_agreement}

        linked_parent_agreements = self._budget_agreement_repository.get_linked_parents(
            budget_agreement.id,
            budget_agreement.budget_id,
        )

        linked_agreements.update(linked_parent_agreements)

        linked_subsidiary_agreements = self._budget_agreement_repository.get_linked_subsidiaries(
            budget_agreement.id,
            budget_agreement.budget_id,
        )

        linked_agreements.update(linked_subsidiary_agreements)

        response_data = self.serializer_class(
            sorted(linked_agreements, key=lambda x: x.period.start_date), many=True
        ).data

        return Response(response_data)
