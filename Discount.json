{"home_operators": [2549], "partner_operators": [120, 121, 954, 763], "direction": "BIDIRECTIONAL", "service_types": ["DATA", "SMS_MO", "SMS_MT", "VOICE_MO", "VOICE_MT", "VOLTE"], "period": {"start_date": "2025-01-01", "end_date": "2025-12-31"}, "call_destinations": null, "called_countries": null, "traffic_segments": null, "imsi_count_type": null, "id": 620, "agreement_id": 96, "model_type": "SEND_OR_PAY_FINANCIAL", "currency_code": "USD", "tax_type": "NET", "volume_type": "ACTUAL", "settlement_method": "CREDIT_NOTE_EOA", "qualifying_rule": null, "parent_id": null, "parameters": [{"discount_id": 620, "calculation_type": "SEND_OR_PAY_FINANCIAL", "basis": "VALUE", "basis_value": null, "balancing": null, "bound_type": "FINANCIAL_COMMITMENT", "lower_bound": "100000.00", "upper_bound": null, "toll_rate": null, "airtime_rate": null, "fair_usage_rate": null, "fair_usage_threshold": null, "access_fee_rate": null, "incremental_rate": null}], "sub_discounts": [{"home_operators": [2549], "partner_operators": [120, 121, 954, 763], "direction": "BIDIRECTIONAL", "service_types": ["VOICE_MO"], "period": {"start_date": "2025-01-01", "end_date": "2025-12-31"}, "call_destinations": ["HOME", "LOCAL"], "called_countries": null, "traffic_segments": null, "imsi_count_type": null, "id": 622, "agreement_id": 96, "model_type": "SINGLE_RATE_EFFECTIVE", "currency_code": "USD", "tax_type": "NET", "volume_type": "ACTUAL", "settlement_method": "CREDIT_NOTE_EOA", "qualifying_rule": null, "parent_id": 620, "parameters": [{"discount_id": 622, "calculation_type": "SINGLE_RATE_EFFECTIVE", "basis": "VALUE", "basis_value": "0.1000000000", "balancing": null, "bound_type": null, "lower_bound": null, "upper_bound": null, "toll_rate": null, "airtime_rate": null, "fair_usage_rate": null, "fair_usage_threshold": null, "access_fee_rate": null, "incremental_rate": null}], "sub_discounts": [], "include_premium": true, "include_premium_in_commitment": true, "financial_threshold": null, "above_financial_threshold_rate": null, "above_commitment_rate": null, "inbound_market_share": null, "commitment_distribution_parameters": null, "budget": null}, {"home_operators": [2549], "partner_operators": [120, 121, 954, 763], "direction": "BIDIRECTIONAL", "service_types": ["VOICE_MO"], "period": {"start_date": "2025-01-01", "end_date": "2025-12-31"}, "call_destinations": ["INTERNATIONAL"], "called_countries": null, "traffic_segments": null, "imsi_count_type": null, "id": 623, "agreement_id": 96, "model_type": "SINGLE_RATE_EFFECTIVE", "currency_code": "USD", "tax_type": "NET", "volume_type": "ACTUAL", "settlement_method": "CREDIT_NOTE_EOA", "qualifying_rule": null, "parent_id": 620, "parameters": [{"discount_id": 623, "calculation_type": "SINGLE_RATE_EFFECTIVE", "basis": "VALUE", "basis_value": "0.1500000000", "balancing": null, "bound_type": null, "lower_bound": null, "upper_bound": null, "toll_rate": null, "airtime_rate": null, "fair_usage_rate": null, "fair_usage_threshold": null, "access_fee_rate": null, "incremental_rate": null}], "sub_discounts": [], "include_premium": true, "include_premium_in_commitment": true, "financial_threshold": null, "above_financial_threshold_rate": null, "above_commitment_rate": null, "inbound_market_share": null, "commitment_distribution_parameters": null, "budget": null}, {"home_operators": [2549], "partner_operators": [120, 121, 954, 763], "direction": "BIDIRECTIONAL", "service_types": ["VOICE_MT"], "period": {"start_date": "2025-01-01", "end_date": "2025-12-31"}, "call_destinations": null, "called_countries": null, "traffic_segments": null, "imsi_count_type": null, "id": 624, "agreement_id": 96, "model_type": "SINGLE_RATE_EFFECTIVE", "currency_code": "USD", "tax_type": "NET", "volume_type": "ACTUAL", "settlement_method": "CREDIT_NOTE_EOA", "qualifying_rule": null, "parent_id": 620, "parameters": [{"discount_id": 624, "calculation_type": "SINGLE_RATE_EFFECTIVE", "basis": "VALUE", "basis_value": "0E-10", "balancing": null, "bound_type": null, "lower_bound": null, "upper_bound": null, "toll_rate": null, "airtime_rate": null, "fair_usage_rate": null, "fair_usage_threshold": null, "access_fee_rate": null, "incremental_rate": null}], "sub_discounts": [], "include_premium": true, "include_premium_in_commitment": true, "financial_threshold": null, "above_financial_threshold_rate": null, "above_commitment_rate": null, "inbound_market_share": null, "commitment_distribution_parameters": null, "budget": null}, {"home_operators": [2549], "partner_operators": [120, 121, 954, 763], "direction": "BIDIRECTIONAL", "service_types": ["SMS_MO"], "period": {"start_date": "2025-01-01", "end_date": "2025-12-31"}, "call_destinations": null, "called_countries": null, "traffic_segments": null, "imsi_count_type": null, "id": 625, "agreement_id": 96, "model_type": "SINGLE_RATE_EFFECTIVE", "currency_code": "USD", "tax_type": "NET", "volume_type": "ACTUAL", "settlement_method": "CREDIT_NOTE_EOA", "qualifying_rule": null, "parent_id": 620, "parameters": [{"discount_id": 625, "calculation_type": "SINGLE_RATE_EFFECTIVE", "basis": "VALUE", "basis_value": "0.0020000000", "balancing": null, "bound_type": null, "lower_bound": null, "upper_bound": null, "toll_rate": null, "airtime_rate": null, "fair_usage_rate": null, "fair_usage_threshold": null, "access_fee_rate": null, "incremental_rate": null}], "sub_discounts": [], "include_premium": true, "include_premium_in_commitment": true, "financial_threshold": null, "above_financial_threshold_rate": null, "above_commitment_rate": null, "inbound_market_share": null, "commitment_distribution_parameters": null, "budget": null}, {"home_operators": [2549], "partner_operators": [120, 121, 954, 763], "direction": "BIDIRECTIONAL", "service_types": ["DATA"], "period": {"start_date": "2025-01-01", "end_date": "2025-12-31"}, "call_destinations": null, "called_countries": null, "traffic_segments": null, "imsi_count_type": null, "id": 626, "agreement_id": 96, "model_type": null, "currency_code": "USD", "tax_type": "NET", "volume_type": "ACTUAL", "settlement_method": "CREDIT_NOTE_EOA", "qualifying_rule": null, "parent_id": 620, "parameters": [{"discount_id": 626, "calculation_type": "SINGLE_RATE_EFFECTIVE", "basis": "VALUE", "basis_value": "0.0050000000", "balancing": null, "bound_type": null, "lower_bound": null, "upper_bound": null, "toll_rate": null, "airtime_rate": null, "fair_usage_rate": null, "fair_usage_threshold": null, "access_fee_rate": null, "incremental_rate": null}, {"discount_id": 626, "calculation_type": "STEPPED_TIERED", "basis": "VALUE", "basis_value": "0.0030000000", "balancing": null, "bound_type": "FINANCIAL_THRESHOLD", "lower_bound": null, "upper_bound": null, "toll_rate": null, "airtime_rate": null, "fair_usage_rate": null, "fair_usage_threshold": null, "access_fee_rate": null, "incremental_rate": null}], "sub_discounts": [], "include_premium": true, "include_premium_in_commitment": true, "financial_threshold": null, "above_financial_threshold_rate": null, "above_commitment_rate": null, "inbound_market_share": null, "commitment_distribution_parameters": null, "budget": null}], "include_premium": true, "include_premium_in_commitment": true, "financial_threshold": null, "above_financial_threshold_rate": null, "above_commitment_rate": null, "inbound_market_share": null, "commitment_distribution_parameters": null, "budget": null}