{"version": "0.2.0", "configurations": [{"name": "Python: <PERSON><PERSON><PERSON>", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/manage.py", "args": ["runserver", "127.0.0.1:8000"], "django": true, "justMyCode": false, "autoStartBrowser": false, "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}", "POETRY_VIRTUALENVS_CREATE": "true", "POETRY_VIRTUALENVS_IN_PROJECT": "true", "DJANGO_SETTINGS_MODULE": "nga.infra.settings", "PYTHONBREAKPOINT": "pdb.set_trace"}, "python": "${workspaceFolder}/.venv/bin/python"}, {"name": "Python: <PERSON><PERSON><PERSON> (Debuggable)", "type": "debugpy", "request": "launch", "module": "celery", "console": "integratedTerminal", "args": ["-A", "nga.infra.celery", "worker", "-l", "INFO", "--pool=solo"], "env": {"PYTHONPATH": "${workspaceFolder}", "POETRY_VIRTUALENVS_CREATE": "true", "POETRY_VIRTUALENVS_IN_PROJECT": "true", "DJANGO_SETTINGS_MODULE": "nga.infra.settings", "PYTHONBREAKPOINT": "pdb.set_trace"}, "justMyCode": false, "python": "${workspaceFolder}/.venv/bin/python"}, {"name": "Python: <PERSON><PERSON><PERSON>", "type": "debugpy", "request": "launch", "module": "celery", "console": "integratedTerminal", "args": ["-A", "nga.infra.celery", "beat", "-l", "INFO"], "env": {"PYTHONPATH": "${workspaceFolder}", "POETRY_VIRTUALENVS_CREATE": "true", "POETRY_VIRTUALENVS_IN_PROJECT": "true", "DJANGO_SETTINGS_MODULE": "nga.infra.settings", "PYTHONBREAKPOINT": "pdb.set_trace"}, "python": "${workspaceFolder}/.venv/bin/python"}]}